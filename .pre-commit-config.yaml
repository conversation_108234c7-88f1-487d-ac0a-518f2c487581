# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
    -   id: trailing-whitespace
        exclude: renv/activate.R
    -   id: end-of-file-fixer
        exclude: crisprprimer/.+\.json
    -   id: check-yaml
    -   id: check-added-large-files
        exclude: crisprprimer/NAU2MSU\.json
-   repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.12.0
    hooks:
    -   id: ruff
        args: [--fix]
    -   id: ruff-format
-   repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.16.1
    hooks:
    -   id: mypy
        additional_dependencies: [pydantic]
