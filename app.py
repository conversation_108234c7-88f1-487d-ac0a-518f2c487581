from __future__ import annotations

import os
import sys
import time
import tomllib
from collections import defaultdict
from itertools import groupby
from pathlib import Path
from typing import Literal, cast

import gradio as gr
from gradio.components.chatbot import (
    ChatMessage,
    MessageDict,
)
from mcp.client.stdio import StdioServerParameters
from openai import Async<PERSON>penAI
from openai.types.chat.chat_completion_message_param import ChatCompletionMessageParam
from swarmx import Agent, Swarm

client = AsyncOpenAI()

swarm = Swarm(mcpServers=dict(
    jupyter=StdioServerParameters(
        command=sys.executable,
        args=[str(Path(__file__).parent / "server.py"), "--server-url", "http://localhost:8890", "--token", "MY_TOKEN", "--path", "docs/index.ipynb"],
    )
))
agent = Agent(model="deepseek-chat")
swarm.add_node(0, type="agent", agent=agent)


def gradio_to_openai(
    history: list[MessageDict],
    keep_thoughts: bool = False,
) -> list[ChatCompletionMessageParam]:
    """Convert Gradio chat history to OpenAI message format.

    Args:
        history: List of Gradio message dictionaries containing 'role' and 'content'
        keep_thoughts: Whether to keep the reasoning message. Default: False

    Returns:
        List of messages in OpenAI format with 'role' and 'content' fields
    """
    messages = []
    for role, grouped_msgs in groupby(history, key=lambda x: x["role"]):
        role = cast(Literal["user", "assistant", "system"], role)
        content = ""

        for msg in grouped_msgs:
            match (msg.get("metadata") or {}).get("title"):
                case "_Thought_":
                    if keep_thoughts:
                        content += f"<think>{msg['content']}</think>"
                case None:
                    content += msg["content"]
        messages.append({"role": role, "content": content})
    return messages


def explore_message(message: ChatMessage | tuple[ChatMessage, ChatMessage]):
    if isinstance(message, tuple):
        yield message[0]
        yield message[1]
    else:
        yield message


async def main(query: str, history: list[MessageDict]):
    stream = await swarm.run(
        messages=[
            *gradio_to_openai(history),
            {"role": "user", "content": query},
        ],
        stream=True,
    )
    # Rationale for messages data structure designed
    # because chunks might be out of order, we need to keep track of them by id
    # For system and user message, it is very simple, no reasoning, no tool call.
    # for assistant message, it might have three parts, thinking + nested tool call + regular answer.
    messages: dict[str, ChatMessage | tuple[ChatMessage, ChatMessage]] = defaultdict(lambda: ChatMessage(content="", metadata={"status": "pending"}))

    # key is tool call id, value is (parent assistant message id, index)
    tool_calls: dict[tuple[str, int], str] = {}
    async for chunk in stream:
        choice = chunk.choices[0]
        delta = choice.delta
        if delta.role == "tool":
            if chunk.id not in messages:
                raise ValueError(f"Tool call {chunk.id} not found in {messages}")
            if delta.refusal or delta.tool_calls or getattr(delta, "reasoning_content", None):
                raise ValueError("Malformed response from agent.")
        message = messages[chunk.id]
        if isinstance(message, tuple):
            reasoning, message = message
        else:
            reasoning = None
        if message.metadata.get("id") is None:
            message.metadata["id"] = chunk.id
        log_string = message.metadata.get("log", "")
        log = tomllib.loads(log_string)  # use toml for appending
        created: float | None = log.get("created")
        if created is None:
            created = time.time()  # openai provided created in integer
            message.metadata["log"] = f"{created = }\n" + log_string
        if delta.role is not None:
            message.role = ({"developer": "system", "tool": "assistant"}).get(delta.role, delta.role)
        if (reasoning_content := getattr(delta, "reasoning_content", None)) is not None:
            if reasoning is None:
                reasoning = ChatMessage(content="", metadata={"title": "_Thinking_", "status": "pending"})
                messages[chunk.id] = (reasoning, message)
            reasoning.content += reasoning_content
            reasoning.metadata["duration"] = time.time() - created
        if delta.content is not None:
            if reasoning is not None and reasoning.metadata.get("status", "done") == "pending":
                reasoning.metadata["status"] = "done"
                completed = time.time()
                reasoning.metadata["duration"] = completed - created
                reasoning.metadata["title"] = "_Thought_"
                reasoning.metadata["log"] = f"{created = }\n{completed = }\n"
            message.content += delta.content
        for tool_call in (delta.tool_calls or []):
            if tool_call.id is not None:
                tool_calls[(chunk.id, tool_call.index)] = tool_call.id
            tool_call_id = tool_calls[(chunk.id, tool_call.index)]
            tool_call_message = cast(ChatMessage, messages[tool_call_id])
            if tool_call.function is not None:
                if tool_call.function.name is not None:
                    tool_call_message.metadata["title"] = tool_call.function.name + "("
                tool_call_message.metadata["title"] += tool_call.function.arguments or ""
        if (finish_reason := choice.finish_reason) is not None:
            message.metadata["status"] = "done"
            completed = time.time()
            message.metadata["duration"] = completed - created
            message.metadata["log"] += f"{completed = }\n{finish_reason = }\n"
            for (message_id, _), tool_call_id in tool_calls.items():
                if finish_reason == "tool_calls" and message_id == chunk.id:
                    tool_call_message = cast(ChatMessage, messages[tool_call_id])
                    tool_call_message.metadata["title"] += ")"
                    tool_call_message.metadata["log"] = f"created = {completed}\n"
        yield [msg for msgs in messages.values() for msg in explore_message(msgs)]


demo = gr.ChatInterface(
    main,
    title="GEEPilot 🧬",
    type="messages",
    save_history=True,
    editable=True,
)
demo.saved_conversations.storage_key = "geepilot_saved_conversations"
demo.saved_conversations.secret = os.getenv("GEEPILOT_SECRET", "geepilot")

demo.launch()
