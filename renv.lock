{"R": {"Version": "4.5.1", "Repositories": [{"Name": "BioCsoft", "URL": "https://bioconductor.org/packages/devel/bioc"}, {"Name": "BioCann", "URL": "https://bioconductor.org/packages/devel/data/annotation"}, {"Name": "BioCexp", "URL": "https://bioconductor.org/packages/devel/data/experiment"}, {"Name": "BioCworkflows", "URL": "https://bioconductor.org/packages/devel/workflows"}, {"Name": "BioCbooks", "URL": "https://bioconductor.org/packages/devel/books"}, {"Name": "CRAN", "URL": "https://cloud.r-project.org"}]}, "Bioconductor": {"Version": "devel"}, "Packages": {"AnnotationDbi": {"Package": "AnnotationDbi", "Version": "1.71.1", "Source": "Bioconductor", "Title": "Manipulation of SQLite-based annotations in Bioconductor", "Description": "Implements a user-friendly interface for querying SQLite-based annotation data packages.", "biocViews": "Annotation, Microarray, Sequencing, GenomeAnnotation", "URL": "https://bioconductor.org/packages/AnnotationDbi", "Video": "https://www.youtube.com/watch?v=8qvGNTVz3Ik", "BugReports": "https://github.com/Bioconductor/AnnotationDbi/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Author": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>", "Maintainer": "Bioconductor Package Maintainer <<EMAIL>>", "Depends": ["R (>= 2.7.0)", "methods", "stats4", "BiocGenerics (>= 0.29.2)", "Biobase (>= 1.17.0)", "IRanges"], "Imports": ["DBI", "RSQLite", "S4Vectors (>= 0.9.25)", "stats", "KEGGREST"], "Suggests": ["utils", "hgu95av2.db", "GO.db", "org.Sc.sgd.db", "org.At.tair.db", "RUnit", "TxDb.Hsapiens.UCSC.hg19.knownGene", "org.Hs.eg.db", "reactome.db", "AnnotationForge", "graph", "EnsDb.Hsapiens.v75", "BiocStyle", "knitr"], "VignetteBuilder": "knitr", "Collate": "00RTobjs.R AllGenerics.R AllClasses.R unlist2.R utils.R SQL.R FlatBimap.R AnnDbObj-lowAPI.R Bimap.R GOTerms.R BimapFormatting.R Bimap-envirAPI.R flatten.R methods-AnnotationDb.R methods-SQLiteConnection.R methods-geneCentricDbs.R methods-geneCentricDbs-keys.R methods-ReactomeDb.R methods-OrthologyDb.R loadDb.R createAnnObjs-utils.R createAnnObjs.NCBIORG_DBs.R createAnnObjs.NCBICHIP_DBs.R createAnnObjs.ORGANISM_DB.R createAnnObjs.YEASTCHIP_DB.R createAnnObjs.COELICOLOR_DB.R createAnnObjs.ARABIDOPSISCHIP_DB.R createAnnObjs.MALARIA_DB.R createAnnObjs.YEAST_DB.R createAnnObjs.YEASTNCBI_DB.R createAnnObjs.ARABIDOPSIS_DB.R createAnnObjs.GO_DB.R createAnnObjs.KEGG_DB.R createAnnObjs.PFAM_DB.R AnnDbPkg-templates-common.R AnnDbPkg-checker.R print.probetable.R makeMap.R inpIDMapper.R test_AnnotationDbi_package.R", "git_url": "https://git.bioconductor.org/packages/AnnotationDbi", "git_branch": "devel", "git_last_commit": "b9b3717", "git_last_commit_date": "2025-07-28", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no"}, "AnnotationHub": {"Package": "AnnotationHub", "Version": "3.99.6", "Source": "Bioconductor", "Type": "Package", "Title": "Client to access AnnotationHub resources", "Authors@R": "c(person(\"Bioconductor Package\", \"Maintainer\", email=\"<EMAIL>\", role=\"cre\"), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>berchain\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"))", "biocViews": "Infrastructure, DataImport, GUI, ThirdPartyClient", "Description": "This package provides a client for the Bioconductor AnnotationHub web resource. The AnnotationHub web resource provides a central location where genomic files (e.g., VCF, bed, wig) and other resources from standard locations (e.g., UCSC, Ensembl) can be discovered. The resource includes metadata about each resource, e.g., a textual description, tags, and date of modification. The client creates and manages a local cache of files retrieved by the user, helping with quick and reproducible access.", "License": "Artistic-2.0", "Depends": ["BiocGenerics (>= 0.15.10)", "BiocFileCache (>= 2.99.3)"], "Imports": ["utils", "methods", "grDevices", "RSQLite", "BiocManager", "BiocVersion", "curl", "rapp<PERSON>s", "AnnotationDbi (>= 1.31.19)", "S4Vectors", "httr2", "yaml", "dplyr"], "Suggests": ["IRanges", "Seqinfo", "GenomeInfoDb", "GenomicRanges", "VariantAnnotation", "Rsamtools", "rtracklayer", "BiocStyle", "knitr", "AnnotationForge", "rBiopaxParser", "RUnit", "txdbmaker", "MSnbase", "mzR", "Biostrings", "CompoundDb", "keras", "ensembldb", "SummarizedExperiment", "ExperimentHub", "gdsfmt", "rmarkdown", "<PERSON><PERSON><PERSON><PERSON>"], "Enhances": ["AnnotationHubData"], "Collate": "AnnotationHubOption.R AllGenerics.R Hub-class.R db-utils.R AnnotationHub-class.R AnnotationHubResource-class.R BEDResource-class.R ProteomicsResource-class.R EpigenomeResource-class.R EnsDbResource-class.R utilities.R sql-utils.R Hub-utils.R cache-utils.R zzz.R", "VignetteBuilder": "knitr", "BugReports": "https://github.com/Bioconductor/AnnotationHub/issues", "NeedsCompilation": "yes", "RoxygenNote": "7.3.2", "git_url": "https://git.bioconductor.org/packages/AnnotationHub", "git_branch": "devel", "git_last_commit": "21cf789", "git_last_commit_date": "2025-06-20", "Repository": "Bioconductor 3.22", "Author": "Bioconductor Package Maintainer [cre], <PERSON> [aut], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [aut]", "Maintainer": "Bioconductor Package Maintainer <<EMAIL>>"}, "BH": {"Package": "BH", "Version": "1.87.0-1", "Source": "Repository", "Type": "Package", "Title": "Boost C++ Header Files", "Date": "2024-12-17", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-6419-907X\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"),  person(\"<PERSON>\", \"<PERSON>\", role = \"aut\",  comment = c(ORCID = \"0000-0003-1899-6662\")))", "Description": "Boost provides free peer-reviewed portable C++ source  libraries.  A large part of Boost is provided as C++ template code which is resolved entirely at compile-time without linking.  This  package aims to provide the most useful subset of Boost libraries  for template use among CRAN packages. By placing these libraries in  this package, we offer a more efficient distribution system for CRAN  as replication of this code in the sources of other packages is  avoided. As of release 1.84.0-0, the following Boost libraries are included: 'accumulators' 'algorithm' 'align' 'any' 'atomic' 'beast' 'bimap' 'bind' 'circular_buffer' 'compute' 'concept' 'config' 'container' 'date_time' 'detail' 'dynamic_bitset' 'exception' 'flyweight' 'foreach' 'functional' 'fusion' 'geometry' 'graph' 'heap' 'icl' 'integer' 'interprocess' 'intrusive' 'io' 'iostreams' 'iterator' 'lambda2' 'math' 'move' 'mp11' 'mpl' 'multiprecision' 'numeric' 'pending' 'phoenix' 'polygon' 'preprocessor' 'process' 'propery_tree' 'qvm' 'random' 'range' 'scope_exit' 'smart_ptr' 'sort' 'spirit' 'tuple' 'type_traits' 'typeof' 'unordered' 'url' 'utility' 'uuid'.", "License": "BSL-1.0", "URL": "https://github.com/eddelbuettel/bh, https://dirk.eddelbuettel.com/code/bh.html", "BugReports": "https://github.com/eddelbuettel/bh/issues", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0001-6419-907X>), <PERSON> [aut], <PERSON> [aut] (<https://orcid.org/0000-0003-1899-6662>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "BSgenome": {"Package": "BSgenome", "Version": "1.77.1", "Source": "Bioconductor", "Title": "Software infrastructure for efficient representation of full genomes and their SNPs", "Description": "Infrastructure shared by all the Biostrings-based genome data packages.", "biocViews": "Genetics, Infrastructure, DataRepresentation, SequenceMatching, Annotation, SNP", "URL": "https://bioconductor.org/packages/BSgenome", "BugReports": "https://github.com/Bioconductor/BSgenome/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\")", "Depends": ["R (>= 2.8.0)", "methods", "BiocGenerics (>= 0.13.8)", "S4Vectors (>= 0.17.28)", "IRanges (>= 2.13.16)", "Seqinfo", "GenomicRanges (>= 1.61.1)", "Biostrings (>= 2.77.2)", "BiocIO", "rtracklayer (>= 1.69.1)"], "Imports": ["utils", "stats", "matrixStats", "XVector", "Rsamtools (>= 2.25.1)"], "Suggests": ["BiocManager", "GenomeInfoDb", "BSgenome.Celegans.UCSC.ce2", "BSgenome.Hsapiens.UCSC.hg38", "BSgenome.Hsapiens.UCSC.hg38.masked", "BSgenome.Mmusculus.UCSC.mm10", "BSgenome.Rnorvegicus.UCSC.rn5", "BSgenome.Scerevisiae.UCSC.sacCer1", "BSgenome.Hsapiens.NCBI.GRCh38", "TxDb.Hsapiens.UCSC.hg38.knownGene", "TxDb.Mmusculus.UCSC.mm10.knownGene", "SNPlocs.Hsapiens.dbSNP144.GRCh38", "XtraSNPlocs.Hsapiens.dbSNP144.GRCh38", "hgu95av2probe", "RUnit", "BSgenomeForge"], "LazyLoad": "yes", "Collate": "utils.R OnDiskLongTable_old-class.R OnDiskLongTable-class.R OnDiskNamedSequences-class.R SNPlocs-class.R ODLT_SNPlocs-class.R OldFashionSNPlocs-class.R InjectSNPsHandler-class.R XtraSNPlocs-class.R BSgenome-class.R available.genomes.R injectSNPs.R getSeq-methods.R extractAt-methods.R bsapply.R BSgenomeViews-class.R BSgenome-utils.R export-methods.R AdvancedBSgenomeForge.R", "git_url": "https://git.bioconductor.org/packages/BSgenome", "git_branch": "devel", "git_last_commit": "8187a70", "git_last_commit_date": "2025-06-20", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "Biobase": {"Package": "Biobase", "Version": "2.69.0", "Source": "Bioconductor", "Title": "Biobase: Base functions for Bioconductor", "Description": "Functions that are needed by many other packages or which replace R functions.", "biocViews": "Infrastructure", "URL": "https://bioconductor.org/packages/Biobase", "BugReports": "https://github.com/Bioconductor/Biobase/issues", "License": "Artistic-2.0", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"'esApply' and 'BiobaseDevelopment' vignette translation from Sweave to Rmarkdown / HTML\" ), person(\"Bioconductor Package Maintainer\", role = \"cre\", email = \"<EMAIL>\" ))", "Suggests": ["tools", "tkWidgets", "ALL", "RUnit", "golubEsets", "BiocStyle", "knitr", "limma"], "Depends": ["R (>= 2.10)", "BiocGenerics (>= 0.27.1)", "utils"], "Imports": ["methods"], "VignetteBuilder": "knitr", "LazyLoad": "yes", "Collate": "tools.R strings.R environment.R vignettes.R packages.R AllGenerics.R VersionsClass.R VersionedClasses.R methods-VersionsNull.R methods-VersionedClass.R DataClasses.R methods-aggregator.R methods-container.R methods-MIAxE.R methods-MIAME.R methods-AssayData.R methods-AnnotatedDataFrame.R methods-eSet.R methods-ExpressionSet.R methods-MultiSet.R methods-SnpSet.R methods-NChannelSet.R anyMissing.R rowOp-methods.R updateObjectTo.R methods-ScalarObject.R zzz.R", "git_url": "https://git.bioconductor.org/packages/Biobase", "git_branch": "devel", "git_last_commit": "22a9cc8", "git_last_commit_date": "2025-04-15", "Repository": "Bioconductor 3.22", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut], <PERSON><PERSON> [aut], <PERSON><PERSON> [aut], <PERSON><PERSON> [aut], <PERSON><PERSON> [ctb] ('esApply' and 'BiobaseDevelopment' vignette translation from Sweave to Rmarkdown / HTML), Bioconductor Package Maintainer [cre]", "Maintainer": "Bioconductor Package Maintainer <<EMAIL>>"}, "BiocFileCache": {"Package": "BiocFileCache", "Version": "2.99.5", "Source": "Bioconductor", "Title": "Manage Files Across Sessions", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = \"aut\"))", "Description": "This package creates a persistent on-disk cache of files that the user can add, update, and retrieve. It is useful for managing resources (such as custom Txdb objects) that are costly or difficult to create, web resources, and data files used across sessions.", "Depends": ["R (>= 3.4.0)", "dbplyr (>= 1.0.0)"], "Imports": ["methods", "stats", "utils", "dplyr", "RSQLite", "DBI", "filelock", "curl", "httr2"], "BugReports": "https://github.com/Bioconductor/BiocFileCache/issues", "DevelopmentURL": "https://github.com/Bioconductor/BiocFileCache", "License": "Artistic-2.0", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "biocViews": "DataImport", "VignetteBuilder": "knitr", "Suggests": ["testthat", "knitr", "BiocStyle", "rmarkdown", "rtracklayer"], "git_url": "https://git.bioconductor.org/packages/BiocFileCache", "git_branch": "devel", "git_last_commit": "e88fb8e", "git_last_commit_date": "2025-05-15", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON> [aut]", "Maintainer": "<PERSON> <<EMAIL>>"}, "BiocGenerics": {"Package": "BiocGenerics", "Version": "0.55.1", "Source": "Bioconductor", "Title": "S4 generic functions used in Bioconductor", "Description": "The package defines many S4 generic functions used in Bioconductor.", "biocViews": "Infrastructure", "URL": "https://bioconductor.org/packages/BiocGenerics", "BugReports": "https://github.com/Bioconductor/BiocGenerics/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "c( person(\"The Bioconductor Dev Team\", role=\"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\", comment=c(ORCID=\"0009-0002-8272-4522\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\", email=\"<EMAIL>\", comment=c(ORCID=\"0000-0002-1520-2268\")), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\", email=\"<EMAIL>\", comment=c(ORCID=\"0000-0002-5874-8148\")), person(\"<PERSON>\", \"<PERSON>benchain\", role=\"ctb\"))", "Depends": ["R (>= 4.0.0)", "methods", "utils", "graphics", "stats", "generics"], "Imports": ["methods", "utils", "graphics", "stats"], "Suggests": ["Biobase", "S4Vectors", "IRanges", "S4Arrays", "SparseArray", "DelayedArray", "HDF5Array", "GenomicRanges", "pwalign", "Rsamtools", "AnnotationDbi", "affy", "affyPLM", "DESeq2", "flowClust", "MSnbase", "annotate", "MultiAssayExperiment", "RUnit"], "Collate": "S3-classes-as-S4-classes.R utils.R normarg-utils.R replaceSlots.R aperm.R append.R as.data.frame.R as.list.R as.vector.R cbind.R do.call.R duplicated.R eval.R Extremes.R format.R funprog.R get.R grep.R is.unsorted.R lapply.R mapply.R match.R mean.R nrow.R order.R paste.R rank.R rep.R row_colnames.R saveRDS.R sort.R start.R subset.R t.R table.R tapply.R unique.R unlist.R unsplit.R which.R which.min.R relist.R boxplot.R image.R density.R IQR.R mad.R residuals.R var.R weights.R xtabs.R setops.R annotation.R combine.R containsOutOfMemoryData.R dbconn.R dge.R dims.R fileName.R longForm.R normalize.R Ontology.R organism_species.R paste2.R path.R plotMA.R plotPCA.R score.R strand.R toTable.R type.R updateObject.R testPackage.R zzz.R", "git_url": "https://git.bioconductor.org/packages/BiocGenerics", "git_branch": "devel", "git_last_commit": "bac3ea0", "git_last_commit_date": "2025-07-28", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "The Bioconductor Dev Team [aut], <PERSON><PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0009-0002-8272-4522>), <PERSON> [ctb] (ORCID: <https://orcid.org/0000-0002-1520-2268>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] (ORCID: <https://orcid.org/0000-0002-5874-8148>), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "BiocIO": {"Package": "BiocIO", "Version": "1.19.0", "Source": "Bioconductor", "Title": "Standard Input and Output for Bioconductor Packages", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", \"cre\", c(ORCID = \"0000-0002-3242-0582\") ))", "Description": "The `BiocIO` package contains high-level abstract classes and generics used by developers to build IO funcionality within the Bioconductor suite of packages. Implements `import()` and `export()` standard generics for importing and exporting biological data formats. `import()` supports whole-file as well as chunk-wise iterative import. The `import()` interface optionally provides a standard mechanism for 'lazy' access via `filter()` (on row or element-like components of the file resource), `select()` (on column-like components of the file resource) and `collect()`. The `import()` interface optionally provides transparent access to remote (e.g. via https) as well as local access. Developers can register a file extension, e.g., `.loom` for dispatch from character-based URIs to specific `import()` / `export()` methods based on classes representing file types, e.g., `LoomFile()`.", "License": "Artistic-2.0", "Encoding": "UTF-8", "Roxygen": "list(markdown = TRUE)", "RoxygenNote": "7.3.2", "Depends": ["R (>= 4.3.0)"], "Imports": ["BiocGenerics", "S4Vectors", "methods", "tools"], "Suggests": ["testthat", "knitr", "rmarkdown", "BiocStyle"], "Collate": "'BiocFile.R' 'import_export.R' 'compression.R' 'utils.R'", "VignetteBuilder": "knitr", "biocViews": "Annotation,DataImport", "BugReports": "https://github.com/Bioconductor/BiocIO/issues", "Date": "2024-11-21", "git_url": "https://git.bioconductor.org/packages/BiocIO", "git_branch": "devel", "git_last_commit": "4b1208c", "git_last_commit_date": "2025-04-15", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [cre] (ORCID: <https://orcid.org/0000-0002-3242-0582>)", "Maintainer": "<PERSON> <<EMAIL>>"}, "BiocManager": {"Package": "BiocManager", "Version": "1.30.25", "Source": "Repository", "Title": "Access the Bioconductor Project Package Repository", "Description": "A convenient tool to install and update Bioconductor packages.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-5874-8148\")), person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-3242-0582\")))", "Imports": ["utils"], "Suggests": ["BiocVersion", "BiocStyle", "remotes", "rmarkdown", "testthat", "withr", "curl", "knitr"], "URL": "https://bioconductor.github.io/BiocManager/", "BugReports": "https://github.com/Bioconductor/BiocManager/issues", "VignetteBuilder": "knitr", "License": "Artistic-2.0", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "NeedsCompilation": "no", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0002-5874-8148>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-3242-0582>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "BiocParallel": {"Package": "BiocParallel", "Version": "1.43.4", "Source": "Bioconductor", "Type": "Package", "Title": "Bioconductor facilities for parallel evaluation", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\",  email = \"<EMAIL>\",  role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role=\"aut\"), person(\"<PERSON>\", \"Obenchain\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON>\", email=\"<EMAIL>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON>\", email=\"<EMAIL>\", role=\"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"Translated 'Random Numbers' vignette from Sweave to RMarkdown / HTML.\" ), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = \"Translated 'Introduction to BiocParallel' vignette from Sweave to Rmarkdown / HTML.\" ), person( \"<PERSON>\", \"Oller\", role = \"ctb\", comment = c( \"Improved bpmapply() efficiency.\", \"ORCID\" = \"0000-0002-8994-1549\" ) ))", "Description": "This package provides modified versions and novel implementation of functions for parallel evaluation, tailored to use with Bioconductor objects.", "URL": "https://github.com/Bioconductor/BiocParallel", "BugReports": "https://github.com/Bioconductor/BiocParallel/issues", "biocViews": "Infrastructure", "License": "GPL-2 | GPL-3 | BSL-1.0", "SystemRequirements": "C++11", "Depends": ["methods", "R (>= 4.1.0)"], "Imports": ["stats", "utils", "futile.logger", "parallel", "snow", "codetools"], "Suggests": ["BiocGenerics", "tools", "foreach", "BBmisc", "doP<PERSON>llel", "GenomicRanges", "RNAseqData.HNRNPC.bam.chr14", "TxDb.Hsapiens.UCSC.hg19.knownGene", "VariantAnnotation", "Rsamtools", "GenomicAlignments", "ShortRead", "RUnit", "BiocStyle", "knitr", "batchtools", "data.table"], "Enhances": ["Rmpi"], "Collate": "AllGenerics.R DeveloperInterface.R prototype.R bploop.R ErrorHandling.R log.R bpbackend-methods.R bpisup-methods.R bplapply-methods.R bpiterate-methods.R bpstart-methods.R bpstop-methods.R BiocParallelParam-class.R bpmapply-methods.R bpschedule-methods.R bpvec-methods.R bpvectorize-methods.R bpworkers-methods.R bpaggregate-methods.R bpvalidate.R SnowParam-class.R MulticoreParam-class.R TransientMulticoreParam-class.R register.R SerialParam-class.R DoparParam-class.R SnowParam-utils.R BatchtoolsParam-class.R progress.R ipcmutex.R worker-number.R utilities.R rng.R bpinit.R reducer.R worker.R bpoptions.R cpp11.R BiocParallel-defunct.R", "LinkingTo": ["BH (>= 1.87.0)", "cpp11"], "VignetteBuilder": "knitr", "RoxygenNote": "7.1.2", "git_url": "https://git.bioconductor.org/packages/BiocParallel", "git_branch": "devel", "git_last_commit": "f72fed0", "git_last_commit_date": "2025-06-11", "Repository": "Bioconductor 3.22", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb] (Translated 'Random Numbers' vignette from Sweave to RMarkdown / HTML.), <PERSON><PERSON><PERSON> [ctb] (Translated 'Introduction to BiocParallel' vignette from Sweave to Rmarkdown / HTML.), <PERSON> [ctb] (Improved bpmapply() efficiency., ORCID: <https://orcid.org/0000-0002-8994-1549>)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "BiocVersion": {"Package": "BiocVersion", "Version": "3.22.0", "Source": "Bioconductor", "Title": "Set the appropriate version of Bioconductor packages", "Description": "This package provides repository information for the appropriate version of Bioconductor.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = \"ctb\"), person(\"Bioconductor\", \"Package Maintainer\", email  = \"<EMAIL>\", role = c(\"ctb\", \"cre\")))", "biocViews": "Infrastructure", "Depends": ["R (>= 4.5.0)"], "License": "Artistic-2.0", "Encoding": "UTF-8", "RoxygenNote": "6.0.1", "git_url": "https://git.bioconductor.org/packages/BiocVersion", "git_branch": "devel", "git_last_commit": "fea53ac", "git_last_commit_date": "2025-04-15", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [ctb], Bioconductor Package Maintainer [ctb, cre]", "Maintainer": "Bioconductor Package Maintainer <<EMAIL>>"}, "Biostrings": {"Package": "Biostrings", "Version": "2.77.2", "Source": "Bioconductor", "Title": "Efficient manipulation of biological strings", "Description": "Memory efficient string containers, string matching algorithms, and other utilities, for fast manipulation of large biological sequences or sets of sequences.", "biocViews": "SequenceMatching, Alignment, Sequencing, Genetics, DataImport, DataRepresentation, Infrastructure", "URL": "https://bioconductor.org/packages/Biostrings", "BugReports": "https://github.com/Bioconductor/Biostrings/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>b<PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\", comment=\"'matchprobes' vignette\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\", comment=\"Converted 'MultipleAlignments' vignette from Sweave to RMarkdown\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\", comment=\"Converted 'matchprobes' vignette from Sweave to RMarkdown\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"Vill\", role=\"ctb\"), person(\"Jen\", \"Wokaty\", role=\"ctb\", comment=\"Converted 'matchprobes' vignette from Sweave to RMarkdown\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"))", "Depends": ["R (>= 4.1.0)", "BiocGenerics (>= 0.37.0)", "S4Vectors (>= 0.27.12)", "IRanges (>= 2.31.2)", "XVector (>= 0.37.1)", "Seqinfo"], "Imports": ["methods", "utils", "grDevices", "stats", "crayon"], "LinkingTo": ["S4Vectors", "IRanges", "XVector"], "Suggests": ["graphics", "pwalign", "BSgenome (>= 1.13.14)", "BSgenome.Celegans.UCSC.ce2 (>= 1.3.11)", "BSgenome.Dmelanogaster.UCSC.dm3 (>= 1.3.11)", "BSgenome.Hsapiens.UCSC.hg18", "drosophila2probe", "hgu95av2probe", "hgu133aprobe", "GenomicFeatures (>= 1.3.14)", "hgu95av2cdf", "affy (>= 1.41.3)", "affydata (>= 1.11.5)", "RUnit", "BiocStyle", "knitr", "testthat (>= 3.0.0)", "covr"], "VignetteBuilder": "knitr", "Collate": "utils.R IUPAC_CODE_MAP.R AMINO_ACID_CODE.R GENETIC_CODE.R XStringCodec-class.R seqtype.R coloring.R XString-class.R XStringSet-class.R XStringSet-comparison.R XStringViews-class.R MaskedXString-class.R XStringSetList-class.R seqinfo-methods.R xscat.R XStringSet-io.R letter.R getSeq.R letterFrequency.R dinucleotideFrequencyTest.R chartr.R reverseComplement.R translate.R toComplex.R replaceAt.R replaceLetterAt.R injectHardMask.R padAndClip.R strsplit-methods.R misc.R SparseList-class.R MIndex-class.R lowlevel-matching.R match-utils.R matchPattern.R maskMotif.R matchLRPatterns.R trimLRPatterns.R matchProbePair.R matchPWM.R findPalindromes.R PDict-class.R matchPDict.R XStringPartialMatches-class.R XStringQuality-class.R QualityScaledXStringSet.R pmatchPattern.R MultipleAlignment.R matchprobes.R moved_to_pwalign.R zzz.R", "git_url": "https://git.bioconductor.org/packages/Biostrings", "git_branch": "devel", "git_last_commit": "1cd36a2", "git_last_commit_date": "2025-06-20", "Repository": "Bioconductor 3.22", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [aut], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] ('matchprobes' vignette), <PERSON><PERSON> [ctb] (Converted 'MultipleAlignments' vignette from Sweave to RMarkdown), <PERSON><PERSON> [ctb] (Converted 'matchprobes' vignette from Sweave to RMarkdown), <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] (Converted 'matchprobes' vignette from Sweave to RMarkdown), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "DBI": {"Package": "DBI", "Version": "1.2.3", "Source": "Repository", "Title": "R Database Interface", "Date": "2024-06-02", "Authors@R": "c( person(\"R Special Interest Group on Databases (R-SIG-DB)\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-1416-3412\")), person(\"R Consortium\", role = \"fnd\") )", "Description": "A database interface definition for communication between R and relational database management systems.  All classes in this package are virtual and need to be extended by the various R/DBMS implementations.", "License": "LGPL (>= 2.1)", "URL": "https://dbi.r-dbi.org, https://github.com/r-dbi/DBI", "BugReports": "https://github.com/r-dbi/DBI/issues", "Depends": ["methods", "R (>= 3.0.0)"], "Suggests": ["arrow", "blob", "covr", "DBItest", "dbplyr", "downlit", "dplyr", "glue", "hms", "knitr", "magrit<PERSON>", "nanoarrow (>= *******)", "RMariaDB", "rmarkdown", "rprojroot", "RSQLite (>= 1.1-2)", "testthat (>= 3.0.0)", "vctrs", "xml2"], "VignetteBuilder": "knitr", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "false", "Config/Needs/check": "r-dbi/DBItest", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "Config/Needs/website": "r-dbi/DBItest, r-dbi/dbitemplate, adbi, AzureKusto, bigrquery, DatabaseConnector, dittodb, duckdb, implyr, lazysf, odbc, pool, RAthena, IMSMWU/RClickhouse, RH2, RJDBC, RMariaDB, RMySQL, RPostgres, RPostgreSQL, RPresto, RSQLite, sergeant, sparklyr, withr", "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "R Special Interest Group on Databases (R-SIG-DB) [aut], <PERSON> [aut], <PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-1416-3412>), R Consortium [fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "DelayedArray": {"Package": "DelayedArray", "Version": "0.35.2", "Source": "Bioconductor", "Title": "A unified framework for working transparently with on-disk and in-memory array-like datasets", "Description": "Wrapping an array-like object (typically an on-disk object) in a DelayedArray object allows one to perform common array operations on it without loading the object in memory. In order to reduce memory usage and optimize performance, operations on the object are either delayed or executed using a block processing mechanism. Note that this also works on in-memory array-like objects like DataFrame objects (typically with Rle columns), Matrix objects, ordinary arrays and, data frames.", "biocViews": "Infrastructure, DataRepresentation, Annotation, GenomeAnnotation", "URL": "https://bioconductor.org/packages/DelayedArray", "BugReports": "https://github.com/Bioconductor/DelayedArray/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\", email=\"<EMAIL>\"), person(\"<PERSON>\", \"Hickey\", role=\"ctb\", email=\"<EMAIL>\"))", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Depends": ["R (>= 4.0.0)", "methods", "stats4", "Matrix", "BiocGenerics (>= 0.53.3)", "MatrixGenerics (>= 1.1.3)", "S4Vectors (>= 0.27.2)", "IRanges (>= 2.17.3)", "S4Arrays (>= 1.5.4)", "SparseArray (>= 1.7.5)"], "Imports": ["stats"], "LinkingTo": ["S4Vectors"], "Suggests": ["BiocParallel", "HDF5Array (>= 1.17.12)", "genefilter", "SummarizedExperiment", "airway", "lobstr", "DelayedMatrixStats", "knitr", "rmarkdown", "BiocStyle", "RUnit"], "VignetteBuilder": "knitr", "Collate": "compress_atomic_vector.R makeCappedVolumeBox.R AutoBlock-global-settings.R AutoGrid.R blockApply.R DelayedOp-class.R DelayedSubset-class.R DelayedAperm-class.R DelayedUnaryIsoOpStack-class.R DelayedUnaryIsoOpWithArgs-class.R DelayedSubassign-class.R DelayedSetDimnames-class.R DelayedNaryIsoOp-class.R DelayedAbind-class.R showtree.R simplify.R DelayedArray-class.R DelayedArray-subsetting.R chunkGrid.R RealizationSink-class.R realize.R DelayedArray-utils.R DelayedArray-stats.R matrixStats-methods.R DelayedMatrix-rowsum.R DelayedMatrix-mult.R ConstantArray-class.R RleArraySeed-class.R RleArray-class.R compat.R zzz.R", "git_url": "https://git.bioconductor.org/packages/DelayedArray", "git_branch": "devel", "git_last_commit": "f430b84", "git_last_commit_date": "2025-04-18", "Repository": "Bioconductor 3.22", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [ctb], <PERSON> [ctb]"}, "ExperimentHub": {"Package": "ExperimentHub", "Version": "2.99.5", "Source": "Bioconductor", "Type": "Package", "Title": "Client to access ExperimentHub resources", "Authors@R": "c(person(\"Bioconductor Package\", \"Maintainer\", email=\"<EMAIL>\", role=\"cre\"), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>berchain\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"))", "Description": "This package provides a client for the Bioconductor ExperimentHub web resource. ExperimentHub provides a central location where curated data from experiments, publications or training courses can be accessed. Each resource has associated metadata, tags and date of modification. The client creates and manages a local cache of files retrieved enabling quick and reproducible access.", "License": "Artistic-2.0", "biocViews": "Infrastructure, DataImport, GUI, ThirdPartyClient", "Depends": ["methods", "BiocGenerics (>= 0.15.10)", "AnnotationHub (>= 3.99.3)", "BiocFileCache (>= 2.99.3)"], "Imports": ["utils", "S4Vectors", "BiocManager", "rapp<PERSON>s"], "Suggests": ["knitr", "BiocStyle", "rmarkdown", "<PERSON><PERSON><PERSON><PERSON>", "GenomicAlignments"], "Enhances": ["ExperimentHubData"], "BugReports": "https://github.com/Bioconductor/ExperimentHub/issues", "VignetteBuilder": "knitr", "RoxygenNote": "7.0.0", "git_url": "https://git.bioconductor.org/packages/ExperimentHub", "git_branch": "devel", "git_last_commit": "ecd291f", "git_last_commit_date": "2025-05-15", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "Bioconductor Package Maintainer [cre], <PERSON> [aut], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [aut]", "Maintainer": "Bioconductor Package Maintainer <<EMAIL>>"}, "GenomeInfoDb": {"Package": "GenomeInfoDb", "Version": "1.45.10", "Source": "Bioconductor", "Title": "Utilities for manipulating chromosome names, including modifying them to follow a particular naming style", "Description": "Contains data and functions that define and allow translation between different chromosome sequence naming conventions (e.g., \"chr1\" versus \"1\"), including a function that attempts to place sequence names in their natural, rather than lexicographic, order.", "biocViews": "Genetics, DataRepresentation, Annotation, GenomeAnnotation", "URL": "https://bioconductor.org/packages/GenomeInfoDb", "Video": "http://youtu.be/wdEjCYSXa7w", "BugReports": "https://github.com/Bioconductor/GenomeInfoDb/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"Atuh<PERSON>ra Kirabo\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\", comment=\"vignette translation from Sweave to Rmarkdown / HTML\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"))", "Depends": ["R (>= 4.0.0)", "methods", "BiocGenerics (>= 0.53.2)", "S4Vectors (>= 0.45.2)", "IRanges (>= 2.41.1)", "Seqinfo (>= 0.99.2)"], "Imports": ["stats", "utils", "UCSC.utils"], "Suggests": ["GenomeInfoDbData", "<PERSON><PERSON>utils", "data.table", "GenomicRanges", "Rsamtools", "GenomicAlignments", "BSgenome", "GenomicFeatures", "TxDb.Dmelanogaster.UCSC.dm3.ensGene", "BSgenome.Scerevisiae.UCSC.sacCer2", "BSgenome.Celegans.UCSC.ce2", "BSgenome.Hsapiens.NCBI.GRCh38", "RUnit", "BiocStyle", "knitr"], "VignetteBuilder": "knitr", "Collate": "utils.R fetch_table_dump_from_Ensembl_FTP.R list_ftp_dir.R NCBI-utils.R UCSC-utils.R Ensembl-utils.R getChromInfoFromNCBI.R getChromInfoFromUCSC.R getChromInfoFromEnsembl.R loadTaxonomyDb.R mapGenomeBuilds.R seqlevelsStyle.R seqlevels-wrappers.R zzz.R", "git_url": "https://git.bioconductor.org/packages/GenomeInfoDb", "git_branch": "devel", "git_last_commit": "3775ca4", "git_last_commit_date": "2025-08-18", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb] (vignette translation from Sweave to Rmarkdown / HTML), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "GenomicAlignments": {"Package": "GenomicAlignments", "Version": "1.45.2", "Source": "Bioconductor", "Title": "Representation and manipulation of short genomic alignments", "Description": "Provides efficient containers for storing and manipulating short genomic alignments (typically obtained by aligning short reads to a reference genome). This includes read counting, computing the coverage, junction detection, and working with the nucleotide content of the alignments.", "biocViews": "Infrastructure, DataImport, Genetics, Sequencing, RNASeq, SNP, Coverage, Alignment, ImmunoOncology", "URL": "https://bioconductor.org/packages/GenomicAlignments", "Video": "https://www.youtube.com/watch?v=2KqBSbkfhRo , https://www.youtube.com/watch?v=3PK_jx44QTs", "BugReports": "https://github.com/Bioconductor/GenomicAlignments/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\"), person(\"<PERSON>\", \"Obenchain\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"Halimat C.\", \"<PERSON><PERSON>\", role=\"ctb\", comment=\"Translated 'WorkingWithAlignedNucleotides' vignette from Sweave to RMarkdown / HTML.\" ))", "Depends": ["R (>= 4.0.0)", "methods", "BiocGenerics (>= 0.37.0)", "S4Vectors (>= 0.27.12)", "IRanges (>= 2.23.9)", "Seqinfo", "GenomicRanges (>= 1.61.1)", "SummarizedExperiment (>= 1.39.1)", "Biostrings (>= 2.77.2)", "Rsamtools (>= 2.25.1)"], "Imports": ["methods", "utils", "stats", "BiocGenerics", "S4Vectors", "IRanges", "GenomicRanges", "Biostrings", "Rsamtools", "BiocParallel"], "LinkingTo": ["S4Vectors", "IRanges"], "Suggests": ["ShortRead", "rtracklayer", "BSgenome", "GenomicFeatures", "RNAseqData.HNRNPC.bam.chr14", "pasillaBamSubset", "TxDb.Hsapiens.UCSC.hg19.knownGene", "TxDb.Dmelanogaster.UCSC.dm3.ensGene", "BSgenome.Dmelanogaster.UCSC.dm3", "BSgenome.Hsapiens.UCSC.hg19", "DESeq2", "edgeR", "RUnit", "knitr", "BiocStyle"], "Collate": "utils.R cigar-utils.R GAlignments-class.R GAlignmentPairs-class.R GAlignmentsList-class.R GappedReads-class.R OverlapEncodings-class.R findMateAlignment.R readGAlignments.R junctions-methods.R sequenceLayer.R pileLettersAt.R stackStringsFromGAlignments.R intra-range-methods.R coverage-methods.R setops-methods.R findOverlaps-methods.R coordinate-mapping-methods.R encodeOverlaps-methods.R findCompatibleOverlaps-methods.R summarizeOverlaps-methods.R findSpliceOverlaps-methods.R zzz.R", "VignetteBuilder": "knitr", "git_url": "https://git.bioconductor.org/packages/GenomicAlignments", "git_branch": "devel", "git_last_commit": "5227f2c", "git_last_commit_date": "2025-07-28", "Repository": "Bioconductor 3.22", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> <PERSON><PERSON> [ctb] (Translated 'WorkingWithAlignedNucleotides' vignette from Sweave to RMarkdown / HTML.)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "GenomicFeatures": {"Package": "GenomicFeatures", "Version": "1.61.6", "Source": "Bioconductor", "Title": "Query the gene models of a given organism/assembly", "Description": "Extract the genomic locations of genes, transcripts, exons, introns, and CDS, for the gene models stored in a TxDb object. A TxDb object is a small database that contains the gene models of a given organism/assembly. Bioconductor provides a small collection of TxDb objects in the form of ready-to-install TxDb packages for the most commonly studied organisms. Additionally, the user can easily make a TxDb object (or package) for the organism/assembly of their choice by using the tools from the txdbmaker package.", "biocViews": "Genetics, Infrastructure, Annotation, Sequencing, GenomeAnnotation", "URL": "https://bioconductor.org/packages/GenomicFeatures", "BugReports": "https://github.com/Bioconductor/GenomicFeatures/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON>.\", \"<PERSON><PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"V.\", \"Obenchain\", role=\"aut\"), person(\"S.\", \"<PERSON><PERSON>ra\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"S.\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON> Twisk\", role=\"ctb\"))", "Depends": ["R (>= 3.5.0)", "BiocGenerics (>= 0.51.2)", "S4Vectors (>= 0.17.29)", "IRanges (>= 2.37.1)", "Seqinfo (>= 0.99.2)", "GenomicRanges (>= 1.61.1)", "AnnotationDbi (>= 1.41.4)"], "Imports": ["methods", "utils", "stats", "DBI", "XVector", "Biostrings (>= 2.77.2)", "rtracklayer (>= 1.69.1)"], "Suggests": ["GenomeInfoDb", "txdbmaker", "org.Mm.eg.db", "org.Hs.eg.db", "BSgenome", "BSgenome.Hsapiens.UCSC.hg19 (>= 1.3.17)", "BSgenome.Celegans.UCSC.ce11", "BSgenome.Dmelanogaster.UCSC.dm3 (>= 1.3.17)", "FDb.UCSC.tRNAs", "TxDb.Hsapiens.UCSC.hg19.knownGene", "TxDb.Celegans.UCSC.ce11.ensGene", "TxDb.Dmelanogaster.UCSC.dm3.ensGene (>= 2.7.1)", "TxDb.Mmusculus.UCSC.mm10.knownGene (>= 3.4.7)", "TxDb.Hsapiens.UCSC.hg19.lincRNAsTranscripts", "TxDb.Hsapiens.UCSC.hg38.knownGene (>= 3.4.6)", "SNPlocs.Hsapiens.dbSNP144.GRCh38", "Rsamtools", "pasillaBamSubset (>= 0.0.5)", "GenomicAlignments (>= 1.15.7)", "ensembldb", "AnnotationFilter", "RUnit", "BiocStyle", "knitr", "markdown"], "VignetteBuilder": "knitr", "Collate": "utils.R TxDb-schema.R TxDb-SELECT-helpers.R TxDb-class.R FeatureDb-class.R mapIdsToRanges.R id2name.R transcripts.R transcriptsBy.R transcriptsByOverlaps.R transcriptLengths.R exonicParts.R extendExonsIntoIntrons.R features.R tRNAs.R extractTranscriptSeqs.R extractUpstreamSeqs.R getPromoterSeq-methods.R select-methods.R nearest-methods.R transcriptLocs2refLocs.R coordinate-mapping-methods.R proteinToGenome.R coverageByTranscript.R makeTxDb.R makeTxDbFromUCSC.R makeTxDbFromBiomart.R makeTxDbFromEnsembl.R makeTxDbFromGRanges.R makeTxDbFromGFF.R makeFeatureDbFromUCSC.R makeTxDbPackage.R zzz.R", "git_url": "https://git.bioconductor.org/packages/GenomicFeatures", "git_branch": "devel", "git_last_commit": "d83c94b", "git_last_commit_date": "2025-07-28", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut], <PERSON><PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON><PERSON> [aut], <PERSON><PERSON> [aut], <PERSON><PERSON> [aut], <PERSON><PERSON> [aut], <PERSON><PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>"}, "GenomicRanges": {"Package": "GenomicRanges", "Version": "1.61.1", "Source": "Bioconductor", "Title": "Representation and manipulation of genomic intervals", "Description": "The ability to efficiently represent and manipulate genomic annotations and alignments is playing a central role when it comes to analyzing high-throughput sequencing data (a.k.a. NGS data). The GenomicRanges package defines general purpose containers for storing and manipulating genomic intervals and variables defined along a genome. More specialized containers for representing and manipulating short alignments against a reference genome, or a matrix-like summarization of an experiment, are defined in the GenomicAlignments and SummarizedExperiment packages, respectively. Both packages build on top of the GenomicRanges infrastructure.", "biocViews": "Genetics, Infrastructure, DataRepresentation, Sequencing, Annotation, GenomeAnnotation, Coverage", "URL": "https://bioconductor.org/packages/GenomicRanges", "BugReports": "https://github.com/Bioconductor/GenomicRanges/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>bencha<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON> <PERSON><PERSON><PERSON>\", role=\"ctb\"))", "Depends": ["R (>= 4.0.0)", "methods", "stats4", "BiocGenerics (>= 0.53.2)", "S4Vectors (>= 0.45.2)", "IRanges (>= 2.41.1)", "Seqinfo"], "Imports": ["utils", "stats", "XVector (>= 0.29.2)"], "LinkingTo": ["S4Vectors", "IRanges"], "Suggests": ["GenomeInfoDb", "Biobase", "AnnotationDbi", "annotate", "Biostrings (>= 2.77.2)", "SummarizedExperiment (>= 1.39.1)", "Rsamtools", "GenomicAlignments", "rtracklayer", "BSgenome", "GenomicFeatures", "txdbmaker", "Gviz", "VariantAnnotation", "AnnotationHub", "DESeq2", "DEXSeq", "edgeR", "KEGGgraph", "RNAseqData.HNRNPC.bam.chr14", "pasillaBamSubset", "KEGGREST", "hgu95av2.db", "hgu95av2probe", "BSgenome.Scerevisiae.UCSC.sacCer2", "BSgenome.Hsapiens.UCSC.hg38", "BSgenome.Mmusculus.UCSC.mm10", "TxDb.Athaliana.BioMart.plantsmart22", "TxDb.Dmelanogaster.UCSC.dm3.ensGene", "TxDb.Hsapiens.UCSC.hg38.knownGene", "TxDb.Mmusculus.UCSC.mm10.knownGene", "RUnit", "digest", "knitr", "rmarkdown", "BiocStyle"], "VignetteBuilder": "knitr", "Collate": "normarg-utils.R utils.R phicoef.R transcript-utils.R constraint.R strand-utils.R genomic-range-squeezers.R GenomicRanges-class.R GenomicRanges-comparison.R GRanges-class.R GPos-class.R GRangesFactor-class.R DelegatingGenomicRanges-class.R GNCList-class.R GenomicRangesList-class.R GRangesList-class.R makeGRangesFromDataFrame.R makeGRangesListFromDataFrame.R RangedData-methods.R findOverlaps-methods.R intra-range-methods.R inter-range-methods.R coverage-methods.R setops-methods.R subtract-methods.R nearest-methods.R absoluteRanges.R tileGenome.R tile-methods.R genomicvars.R zzz.R", "git_url": "https://git.bioconductor.org/packages/GenomicRanges", "git_branch": "devel", "git_last_commit": "096060d", "git_last_commit_date": "2025-06-21", "Repository": "Bioconductor 3.22", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre], <PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "IRanges": {"Package": "IRanges", "Version": "2.43.0", "Source": "Bioconductor", "Title": "Foundation of integer range manipulation in Bioconductor", "Description": "Provides efficient low-level and highly reusable S4 classes for storing, manipulating and aggregating over annotated ranges of integers. Implements an algebra of range operations, including efficient algorithms for finding overlaps and nearest neighbors. Defines efficient list-like classes for storing, transforming and aggregating large grouped data, i.e., collections of atomic vectors and DataFrames.", "biocViews": "Infrastructure, DataRepresentation", "URL": "https://bioconductor.org/packages/IRanges", "BugReports": "https://github.com/Bioconductor/IRanges/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"))", "Depends": ["R (>= 4.0.0)", "methods", "utils", "stats", "BiocGenerics (>= 0.53.2)", "S4Vectors (>= 0.45.4)"], "Imports": ["stats4"], "LinkingTo": ["S4Vectors"], "Suggests": ["XVector", "GenomicRanges", "Rsamtools", "GenomicAlignments", "GenomicFeatures", "BSgenome.Celegans.UCSC.ce2", "pasillaBamSubset", "RUnit", "BiocStyle"], "Collate": "range-squeezers.R Vector-class-leftovers.R DataFrameList-class.R DataFrameList-utils.R AtomicList-class.R AtomicList-utils.R Ranges-and-RangesList-classes.R IPosRanges-class.R IPosRanges-comparison.R IntegerRangesList-class.R IRanges-class.R IRanges-constructor.R IRanges-utils.R Rle-class-leftovers.R IPos-class.R subsetting-utils.R Grouping-class.R Views-class.R RleViews-class.R RleViews-utils.R extractList.R seqapply.R multisplit.R SimpleGrouping-class.R IRangesList-class.R IPosList-class.R ViewsList-class.R RleViewsList-class.R RleViewsList-utils.R RangedSelection-class.R MaskCollection-class.R read.Mask.R CompressedList-class.R CompressedList-comparison.R CompressedHitsList-class.R CompressedDataFrameList-class.R CompressedAtomicList-class.R CompressedGrouping-class.R CompressedRangesList-class.R Hits-class-leftovers.R NCList-class.R findOverlaps-methods.R windows-methods.R intra-range-methods.R inter-range-methods.R reverse-methods.R coverage-methods.R cvg-methods.R slice-methods.R setops-methods.R nearest-methods.R cbind-Rle-methods.R tile-methods.R extractListFragments.R zzz.R", "git_url": "https://git.bioconductor.org/packages/IRanges", "git_branch": "devel", "git_last_commit": "66bc855", "git_last_commit_date": "2025-04-15", "Repository": "Bioconductor 3.22", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "IRdisplay": {"Package": "IRdisplay", "Version": "1.1", "Source": "Repository", "Title": "'Jupyter' Display Machinery", "Description": "An interface to the rich display capabilities of 'Jupyter' front-ends (e.g. 'Jupyter Notebook') <https://jupyter.org>. Designed to be used from a running 'IRkernel' session <https://irkernel.github.io>.", "Authors@R": "c( person('<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', role = c('aut', 'cph'), email = '<EMAIL>'), person('<PERSON>', '<PERSON><PERSON>', role = c('aut', 'cph', 'cre'), email = '<EMAIL>', comment = c(ORCID = \"0000-0002-0369-2888\")), person('<PERSON>', '<PERSON><PERSON><PERSON>', role = c('aut', 'cph'), email = '<EMAIL>'))", "URL": "https://github.com/IRkernel/IRdisplay", "BugReports": "https://github.com/IRkernel/IRdisplay/issues/", "Depends": ["R (>= 3.0.1)"], "Suggests": ["testthat", "withr"], "Imports": ["methods", "repr"], "License": "MIT + file LICENSE", "Encoding": "UTF-8", "RoxygenNote": "7.1.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cph], <PERSON> [aut, cph, cre] (<https://orcid.org/0000-0002-0369-2888>), <PERSON> [aut, cph]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "IRkernel": {"Package": "IRkernel", "Version": "1.3.2", "Source": "Repository", "Title": "Native <PERSON> for the 'Jupyter Notebook'", "Description": "The R kernel for the 'Jupyter' environment executes R code which the front-end ('Jupyter Notebook' or other front-ends) submits to the kernel via the network.", "Authors@R": "c( person('<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', role = c('aut', 'cph'), email = '<EMAIL>'), person('<PERSON>', '<PERSON><PERSON>', role = c('aut', 'cph', 'cre'), email = '<EMAIL>', comment = c(ORCID = \"0000-0002-0369-2888\")), person('<PERSON>', '<PERSON><PERSON><PERSON>', role = c('aut', 'cph'), email = '<EMAIL>'), person('Karth<PERSON>', '<PERSON>', role = c('aut', 'cph'), email = '<EMAIL>'))", "URL": "https://irkernel.github.io", "BugReports": "https://github.com/IRkernel/IRkernel/issues/", "Depends": ["R (>= 3.2.0)"], "Suggests": ["testthat", "roxygen2"], "SystemRequirements": "jupyter, jupyter_kernel_test (Python package for testing)", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "Imports": ["repr (>= 0.4.99)", "methods", "evaluate (>= 0.10)", "IRdisplay (>= 0.3.0.9999)", "pbdZMQ (>= 0.2-1)", "crayon", "jsonlite (>= 0.9.6)", "uuid", "digest"], "Collate": "'class_unions.r' 'logging.r' 'comm_manager.r' 'compat.r' 'completion.r' 'environment_runtime.r' 'environment_shadow.r' 'options.r' 'execution.r' 'handlers.r' 'help.r' 'installspec.r' 'utils.r' 'kernel.r' 'main.r' 'onload.r'", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cph], <PERSON> [aut, cph, cre] (<https://orcid.org/0000-0002-0369-2888>), <PERSON> [aut, cph], <PERSON><PERSON><PERSON> [aut, cph]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "KEGGREST": {"Package": "KEGGREST", "Version": "1.49.1", "Source": "Bioconductor", "Title": "Client-side REST access to the Kyoto Encyclopedia of Genes and Genomes (KEGG)", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"Bioconductor Package\", \"Maintainer\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\") )", "Depends": ["R (>= 3.5.0)"], "Imports": ["methods", "httr", "png", "Biostrings"], "Suggests": ["RUnit", "BiocGenerics", "BiocStyle", "knitr", "markdown"], "Description": "A package that provides a client interface to the Kyoto Encyclopedia of Genes and Genomes (KEGG) REST API. Only for academic use by academic users belonging to academic institutions (see <https://www.kegg.jp/kegg/rest/>). Note that KEGGREST is based on KEGGSOAP by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, and KEGG (python package) by <PERSON><PERSON><PERSON>.", "URL": "https://bioconductor.org/packages/KEGGREST", "BugReports": "https://github.com/Bioconductor/KEGGREST/issues", "License": "Artistic-2.0", "VignetteBuilder": "knitr", "biocViews": "Annotation, Pathways, ThirdPartyClient, KEGG", "RoxygenNote": "7.1.1", "Date": "2025-06-18", "git_url": "https://git.bioconductor.org/packages/KEGGREST", "git_branch": "devel", "git_last_commit": "6125ec8", "git_last_commit_date": "2025-06-18", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON> [aut], Bioconductor Package Maintainer [aut, cre], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "Bioconductor Package Maintainer <<EMAIL>>"}, "MatrixGenerics": {"Package": "MatrixGenerics", "Version": "1.21.0", "Source": "Bioconductor", "Title": "S4 Generic Summary Statistic Functions that Operate on Matrix-Like Objects", "Description": "S4 generic functions modeled after the 'matrixStats' API for alternative matrix implementations. Packages with alternative matrix implementation can depend on this package and implement the generic functions that are defined here for a useful set of row and column summary statistics. Other package developers can import this package and handle a different matrix implementations without worrying about incompatibilities.", "biocViews": "Infrastructure, Software", "URL": "https://bioconductor.org/packages/MatrixGenerics", "BugReports": "https://github.com/Bioconductor/MatrixGenerics/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\"), comment = c(ORCID = \"0000-0002-3762-068X\")), person(\"<PERSON>\", \"Hickey\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-8153-6258\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = \"aut\"))", "Depends": ["matrixStats (>= 1.4.1)"], "Imports": ["methods"], "Suggests": ["Matrix", "sparseMatrixStats", "SparseArray", "DelayedArray", "DelayedMatrixStats", "SummarizedExperiment", "testthat (>= 2.1.0)"], "RoxygenNote": "7.3.2", "Roxygen": "list(markdown = TRUE, old_usage = TRUE)", "Collate": "'MatrixGenerics-package.R' 'rowAlls.R' 'rowAnyNAs.R' 'rowAnys.R' 'rowAvgsPerColSet.R' 'rowCollapse.R' 'rowCounts.R' 'rowCummaxs.R' 'rowCummins.R' 'rowCumprods.R' 'rowCumsums.R' 'rowDiffs.R' 'rowIQRDiffs.R' 'rowIQRs.R' 'rowLogSumExps.R' 'rowMadDiffs.R' 'rowMads.R' 'rowMaxs.R' 'rowMeans.R' 'rowMeans2.R' 'rowMedians.R' 'rowMins.R' 'rowOrderStats.R' 'rowProds.R' 'rowQuantiles.R' 'rowRanges.R' 'rowRanks.R' 'rowSdDiffs.R' 'rowSds.R' 'rowSums.R' 'rowSums2.R' 'rowTabulates.R' 'rowVarDiffs.R' 'rowVars.R' 'rowWeightedMads.R' 'rowWeightedMeans.R' 'rowWeightedMedians.R' 'rowWeightedSds.R' 'rowWeightedVars.R'", "git_url": "https://git.bioconductor.org/packages/MatrixGenerics", "git_branch": "devel", "git_last_commit": "2a70895", "git_last_commit_date": "2025-04-15", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "Constantin <PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-3762-068X>), <PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-8153-6258>), <PERSON><PERSON><PERSON> [aut]", "Maintainer": "<PERSON> <<EMAIL>>"}, "R.cache": {"Package": "<PERSON><PERSON><PERSON>", "Version": "0.17.0", "Source": "Repository", "Depends": ["R (>= 2.14.0)"], "Imports": ["utils", "<PERSON>.<PERSON>S3 (>= 1.8.1)", "R.oo (>= 1.24.0)", "<PERSON>.utils (>= 2.10.1)", "digest (>= 0.6.13)"], "Title": "Fast and Light-Weight Caching (Memoization) of Objects and Results to Speed Up Computations", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\"))", "Author": "<PERSON> [aut, cre, cph]", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Memoization can be used to speed up repetitive and computational expensive function calls.  The first time a function that implements memoization is called the results are stored in a cache memory.  The next time the function is called with the same set of parameters, the results are momentarily retrieved from the cache avoiding repeating the calculations.  With this package, any R object can be cached in a key-value storage where the key can be an arbitrary set of R objects.  The cache memory is persistent (on the file system).", "License": "LGPL (>= 2.1)", "LazyLoad": "TRUE", "URL": "https://github.com/HenrikBengtsson/R.cache", "BugReports": "https://github.com/HenrikBengtsson/R.cache/issues", "Encoding": "UTF-8", "NeedsCompilation": "no", "Repository": "CRAN"}, "R.methodsS3": {"Package": "<PERSON>.<PERSON>S3", "Version": "1.8.2", "Source": "Repository", "Depends": ["R (>= 2.13.0)"], "Imports": ["utils"], "Suggests": ["codetools"], "Title": "S3 Methods Simplified", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\"))", "Author": "<PERSON> [aut, cre, cph]", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Methods that simplify the setup of S3 generic functions and S3 methods.  Major effort has been made in making definition of methods as simple as possible with a minimum of maintenance for package developers.  For example, generic functions are created automatically, if missing, and naming conflict are automatically solved, if possible.  The method setMethodS3() is a good start for those who in the future may want to migrate to S4.  This is a cross-platform package implemented in pure R that generates standard S3 methods.", "License": "LGPL (>= 2.1)", "LazyLoad": "TRUE", "URL": "https://github.com/HenrikBengtsson/R.methodsS3", "BugReports": "https://github.com/HenrikBengtsson/R.methodsS3/issues", "NeedsCompilation": "no", "Repository": "CRAN"}, "R.oo": {"Package": "<PERSON>.oo", "Version": "1.27.1", "Source": "Repository", "Depends": ["R (>= 2.13.0)", "<PERSON>.<PERSON>S3 (>= 1.8.2)"], "Imports": ["methods", "utils"], "Suggests": ["tools"], "Title": "R Object-Oriented Programming with or without References", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\"))", "Author": "<PERSON> [aut, cre, cph]", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Methods and classes for object-oriented programming in R with or without references.  Large effort has been made on making definition of methods as simple as possible with a minimum of maintenance for package developers.  The package has been developed since 2001 and is now considered very stable.  This is a cross-platform package implemented in pure R that defines standard S3 classes without any tricks.", "License": "LGPL (>= 2.1)", "LazyLoad": "TRUE", "URL": "https://henrikbengtsson.github.io/R.oo/, https://github.com/HenrikB<PERSON>tsson/R.oo", "BugReports": "https://github.com/HenrikBengtsson/R.oo/issues", "NeedsCompilation": "no", "Repository": "CRAN"}, "R.utils": {"Package": "<PERSON><PERSON>utils", "Version": "2.13.0", "Source": "Repository", "Depends": ["R (>= 2.14.0)", "<PERSON>.oo"], "Imports": ["methods", "utils", "tools", "<PERSON>.<PERSON>S3"], "Suggests": ["datasets", "digest (>= 0.6.10)"], "Title": "Various Programming Utilities", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\"))", "Author": "<PERSON> [aut, cre, cph]", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Utility functions useful when programming and developing R packages.", "License": "LGPL (>= 2.1)", "LazyLoad": "TRUE", "URL": "https://henrikbengtsson.github.io/R.utils/, https://github.com/Henrik<PERSON>son/R.utils", "BugReports": "https://github.com/HenrikBengtsson/R.utils/issues", "NeedsCompilation": "no", "Repository": "CRAN"}, "R6": {"Package": "R6", "Version": "2.6.1", "Source": "Repository", "Title": "Encapsulated Classes with Reference Semantics", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Creates classes with reference semantics, similar to R's built-in reference classes. Compared to reference classes, R6 classes are simpler and lighter-weight, and they are not built on S4 classes so they do not require the methods package. These classes allow public and private members, and they support inheritance, even when the classes are defined in different packages.", "License": "MIT + file LICENSE", "URL": "https://r6.r-lib.org, https://github.com/r-lib/R6", "BugReports": "https://github.com/r-lib/R6/issues", "Depends": ["R (>= 3.6)"], "Suggests": ["lobstr", "testthat (>= 3.0.0)"], "Config/Needs/website": "tidyverse/tidytemplate, ggplot2, microbenchmark, scales", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "RCurl": {"Package": "<PERSON><PERSON><PERSON>", "Version": "1.98-1.17", "Source": "Repository", "Title": "General Network (HTTP/FTP/...) Client Interface for R", "Authors@R": "c(person(\"CRAN Team\", role = c('ctb', 'cre'), email = \"<EMAIL>\", comment = \"de facto maintainer since 2013\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0159-1546\")))", "SystemRequirements": "GNU make, libcurl", "Description": "A wrapper for 'libcurl' <https://curl.se/libcurl/> Provides functions to allow one to compose general HTTP requests and provides convenient functions to fetch URIs, get & post forms, etc. and process the results returned by the Web server. This provides a great deal of control over the HTTP/FTP/... connection and the form of the request while providing a higher-level interface than is available just using R socket connections.  Additionally, the underlying implementation is robust and extensive, supporting FTP/FTPS/TFTP (uploads and downloads), SSL/HTTPS, telnet, dict, ldap, and also supports cookies, redirects, authentication, etc.", "License": "BSD_3_clause + file LICENSE", "Depends": ["R (>= 3.4.0)", "methods"], "Imports": ["bitops"], "Suggests": ["XML"], "Collate": "aclassesEnums.R bitClasses.R xbits.R base64.R binary.S classes.S curl.S curlAuthConstants.R curlEnums.R curlError.R curlInfo.S dynamic.R form.S getFormParams.R getURLContent.R header.R http.R httpError.R httpErrors.R iconv.R info.S mime.R multi.S options.S scp.R support.S upload.R urlExists.R zclone.R zzz.R", "NeedsCompilation": "yes", "Author": "CRAN Team [ctb, cre] (de facto maintainer since 2013), <PERSON> [aut] (<https://orcid.org/0000-0003-0159-1546>)", "Maintainer": "CRAN Team <<EMAIL>>", "Repository": "CRAN"}, "RSQLite": {"Package": "RSQLite", "Version": "2.4.3", "Source": "Repository", "Title": "SQLite Interface for R", "Date": "2025-08-01", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-1416-3412\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(c(\"<PERSON>\", \"<PERSON><PERSON>\"), \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON> Richard\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"for the included SQLite sources\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"for the included SQLite sources\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", comment = \"for the included SQLite sources\"), person(, \"SQLite Authors\", role = \"ctb\", comment = \"for the included SQLite sources\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"for the included SQLite sources\"), person(\"R Consortium\", role = \"fnd\"), person(, \"RStu<PERSON>\", role = \"cph\") )", "Description": "Embeds the SQLite database engine in R and provides an interface compliant with the DBI package. The source for the SQLite engine (version 3.50.4) and for various extensions is included. System libraries will never be consulted because this package relies on static linking for the plugins it includes; this also ensures a consistent experience across all installations.", "License": "LGPL (>= 2.1)", "URL": "https://rsqlite.r-dbi.org, https://github.com/r-dbi/RSQLite", "BugReports": "https://github.com/r-dbi/RSQLite/issues", "Depends": ["R (>= 3.1.0)"], "Imports": ["bit64", "blob (>= 1.2.0)", "DBI (>= 1.2.0)", "memoise", "methods", "pkgconfig", "rlang"], "Suggests": ["callr", "cli", "DBItest (>= 1.8.0)", "decor", "gert", "gh", "hms", "knitr", "magrit<PERSON>", "rmarkdown", "rvest", "testthat (>= 3.0.0)", "withr", "xml2"], "LinkingTo": ["plogr (>= 0.2.0)", "cpp11 (>= 0.4.0)"], "VignetteBuilder": "knitr", "Config/Needs/website": "r-dbi/dbitemplate", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "false", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "Collate": "'SQLiteConnection.R' 'SQLKeywords_SQLiteConnection.R' 'SQLiteDriver.R' 'SQLite.R' 'SQLiteResult.R' 'coerce.R' 'compatRowNames.R' 'copy.R' 'cpp11.R' 'datasetsDb.R' 'dbAppendTable_SQLiteConnection.R' 'dbBeginTransaction.R' 'dbBegin_SQLiteConnection.R' 'dbBind_SQLiteResult.R' 'dbClearResult_SQLiteResult.R' 'dbColumnInfo_SQLiteResult.R' 'dbCommit_SQLiteConnection.R' 'dbConnect_SQLiteConnection.R' 'dbConnect_SQLiteDriver.R' 'dbDataType_SQLiteConnection.R' 'dbDataType_SQLiteDriver.R' 'dbDisconnect_SQLiteConnection.R' 'dbExistsTable_SQLiteConnection_Id.R' 'dbExistsTable_SQLiteConnection_character.R' 'dbFetch_SQLiteResult.R' 'dbGetException_SQLiteConnection.R' 'dbGetInfo_SQLiteConnection.R' 'dbGetInfo_SQLiteDriver.R' 'dbGetPreparedQuery.R' 'dbGetPreparedQuery_SQLiteConnection_character_data.frame.R' 'dbGetRowCount_SQLiteResult.R' 'dbGetRowsAffected_SQLiteResult.R' 'dbGetStatement_SQLiteResult.R' 'dbHasCompleted_SQLiteResult.R' 'dbIsValid_SQLiteConnection.R' 'dbIsValid_SQLiteDriver.R' 'dbIsValid_SQLiteResult.R' 'dbListResults_SQLiteConnection.R' 'dbListTables_SQLiteConnection.R' 'dbQuoteIdentifier_SQLiteConnection_SQL.R' 'dbQuoteIdentifier_SQLiteConnection_character.R' 'dbReadTable_SQLiteConnection_character.R' 'dbRemoveTable_SQLiteConnection_character.R' 'dbRollback_SQLiteConnection.R' 'dbSendPreparedQuery.R' 'dbSendPreparedQuery_SQLiteConnection_character_data.frame.R' 'dbSendQuery_SQLiteConnection_character.R' 'dbUnloadDriver_SQLiteDriver.R' 'dbUnquoteIdentifier_SQLiteConnection_SQL.R' 'dbWriteTable_SQLiteConnection_character_character.R' 'dbWriteTable_SQLiteConnection_character_data.frame.R' 'db_bind.R' 'deprecated.R' 'export.R' 'fetch_SQLiteResult.R' 'import-standalone-check_suggested.R' 'import-standalone-purrr.R' 'initExtension.R' 'initRegExp.R' 'isSQLKeyword_SQLiteConnection_character.R' 'make.db.names_SQLiteConnection_character.R' 'pkgconfig.R' 'show_SQLiteConnection.R' 'sqlData_SQLiteConnection.R' 'table.R' 'transactions.R' 'utils.R' 'version.R' 'zzz.R'", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>), <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [ctb] (for the included SQLite sources), <PERSON> [ctb] (for the included SQLite sources), <PERSON> [ctb] (for the included SQLite sources), SQLite Authors [ctb] (for the included SQLite sources), <PERSON> [ctb] (for the included SQLite sources), R Consortium [fnd], RStudio [cph]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "Rbowtie": {"Package": "<PERSON><PERSON><PERSON>", "Version": "1.49.0", "Source": "Bioconductor", "Type": "Package", "Title": "R bowtie wrapper", "Date": "2023-01-29", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"),  email = \"<EMAIL>\",  comment = c(ORCID = \"0000-0002-2269-4934\")))", "Maintainer": "<PERSON> <<EMAIL>>", "Imports": ["utils"], "Suggests": ["testthat", "parallel", "BiocStyle", "knitr", "rmarkdown"], "SystemRequirements": "GNU make", "Description": "This package provides an R wrapper around the popular bowtie short read aligner and around SpliceMap, a de novo splice junction discovery and alignment tool. The package is used by the QuasR bioconductor package. We recommend to use the QuasR package instead of using Rbowtie directly.", "License": "Artistic-2.0 | file LICENSE", "LazyLoad": "yes", "URL": "https://github.com/fmicompbio/Rbowtie", "BugReports": "https://github.com/fmicompbio/Rbowtie/issues", "biocViews": "Sequencing, Alignment", "VignetteBuilder": "knitr", "git_url": "https://git.bioconductor.org/packages/Rbowtie", "git_branch": "devel", "git_last_commit": "2572ca9", "git_last_commit_date": "2025-04-15", "Repository": "Bioconductor 3.22", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-2269-4934>)"}, "Rcpp": {"Package": "Rcpp", "Version": "1.1.0", "Source": "Repository", "Title": "Seamless R and C++ Integration", "Date": "2025-07-01", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-6419-907X\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2444-4226\")), person(\"<PERSON><PERSON>\", \"<PERSON>aire\", role = \"aut\", comment = c(ORCID = \"0000-0003-0174-9868\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0003-2880-7407\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-6786-5453\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-6403-5550\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-8316-9503\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"))", "Description": "The 'Rcpp' package provides R functions as well as C++ classes which offer a seamless integration of R and C++. Many R data types and objects can be mapped back and forth to C++ equivalents which facilitates both writing of new code as well as easier integration of third-party libraries. Documentation about 'Rcpp' is provided by several vignettes included in this package, via the 'Rcpp Gallery' site at <https://gallery.rcpp.org>, the paper by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (2011, <doi:10.18637/jss.v040.i08>), the book by <PERSON><PERSON><PERSON><PERSON><PERSON> (2013, <doi:10.1007/978-1-4614-6868-4>) and the paper by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2018, <doi:10.1080/00031305.2017.1375990>); see 'citation(\"Rcpp\")' for details.", "Imports": ["methods", "utils"], "Suggests": ["tinytest", "inline", "rbenchmark", "pkgKitten (>= 0.1.2)"], "URL": "https://www.rcpp.org, https://dirk.eddelbuettel.com/code/rcpp.html, https://github.com/RcppCore/Rcpp", "License": "GPL (>= 2)", "BugReports": "https://github.com/RcppCore/Rcpp/issues", "MailingList": "<EMAIL>", "RoxygenNote": "6.1.1", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0001-6419-907X>), <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-2444-4226>), <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-0174-9868>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-2880-7407>), <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0001-6786-5453>), <PERSON> [aut], <PERSON><PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0001-6403-5550>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0001-8316-9503>), <PERSON> [aut]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "RcppTOML": {"Package": "RcppTOML", "Version": "0.2.3", "Source": "Repository", "Type": "Package", "Title": "'<PERSON><PERSON><PERSON>' Bindings to <PERSON><PERSON><PERSON> for \"Tom's Obvious Mark<PERSON>\"", "Date": "2025-03-08", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-6419-907X\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = \"Author of 'toml++' header library\"))", "Description": "The configuration format defined by 'TOML' (which expands to \"Tom's Obvious Markup Language\") specifies an excellent format (described at <https://toml.io/en/>) suitable for both human editing as well as the common uses of a machine-readable format. This package uses 'Rcpp' to connect to the 'toml++' parser written by <PERSON> to R.", "SystemRequirements": "A C++17 compiler", "BugReports": "https://github.com/eddelbuettel/rcpptoml/issues", "URL": "http://dirk.eddelbuettel.com/code/rcpp.toml.html", "Imports": ["Rcpp (>= 1.0.8)"], "Depends": ["R (>= 3.3.0)"], "LinkingTo": ["Rcpp"], "Suggests": ["tinytest"], "License": "GPL (>= 2)", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0001-6419-907X>), <PERSON> [aut] (Author of 'toml++' header library)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "Rhtslib": {"Package": "Rhtslib", "Version": "3.5.0", "Source": "Bioconductor", "Title": "HTSlib high-throughput sequencing library as an R package", "Description": "This package provides version 1.18 of the 'HTSlib' C library for high-throughput sequence analysis. The package is primarily useful to developers of other R packages who wish to make use of HTSlib. Motivation and instructions for use of this package are in the vignette, vignette(package=\"Rhtslib\", \"Rhtslib\").", "biocViews": "DataImport, Sequencing", "URL": "https://bioconductor.org/packages/Rhtslib, http://www.htslib.org/", "BugReports": "https://github.com/Bioconductor/Rhtslib/issues", "License": "LGPL (>= 2)", "Copyright": "Unless otherwise noted in the file, all files outside src/htslib-1.18 or inst/include copyright Bioconductor; for files inside src/htslib-1.18 or inst/include, see file src/htslib-1.18/LICENSE.", "Encoding": "UTF-8", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", role=c(\"led\", \"aut\"), email=\"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\", email=\"<EMAIL>\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"))", "Imports": ["tools"], "Suggests": ["knitr", "rmarkdown", "BiocStyle"], "SystemRequirements": "libbz2 & liblzma & libcurl (with header files), GNU make", "StagedInstall": "no", "VignetteBuilder": "knitr", "git_url": "https://git.bioconductor.org/packages/Rhtslib", "git_branch": "devel", "git_last_commit": "2cb15f8", "git_last_commit_date": "2025-04-15", "Repository": "Bioconductor 3.22", "NeedsCompilation": "yes", "Author": "<PERSON> [led, aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "Rsamtools": {"Package": "Rsamtools", "Version": "2.25.2", "Source": "Bioconductor", "Type": "Package", "Title": "Binary alignment (BAM), FASTA, variant call (BCF), and tabix file import", "Description": "This package provides an interface to the 'samtools', 'bcftools', and 'tabix' utilities for manipulating SAM (Sequence Alignment / Map), FASTA, binary variant call (BCF) and compressed indexed tab-delimited (tabix) files.", "biocViews": "DataImport, Sequencing, Coverage, Alignment, QualityControl", "URL": "https://bioconductor.org/packages/Rsamtools", "Video": "https://www.youtube.com/watch?v=Rfon-DQYbWA&list=UUqaMSQd_h-2EDGsU6WDiX0Q", "BugReports": "https://github.com/Bioconductor/Rsamtools/issues", "License": "Artistic-2.0 | file LICENSE", "Encoding": "UTF-8", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"Obenchain\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"Converted Rsamtools vignette from Sweave to RMarkdown / HTML.\"), person(\"Bioconductor Package Maintainer\", email = \"<EMAIL>\", role = \"cre\"))", "Depends": ["R (>= 3.5.0)", "methods", "Seqinfo", "GenomicRanges (>= 1.61.1)", "Biostrings (>= 2.77.2)"], "Imports": ["utils", "BiocGenerics (>= 0.25.1)", "S4Vectors (>= 0.17.25)", "IRanges (>= 2.13.12)", "XVector (>= 0.19.7)", "bitops", "BiocParallel", "stats"], "Suggests": ["GenomicAlignments", "ShortRead (>= 1.19.10)", "GenomicFeatures", "TxDb.Dmelanogaster.UCSC.dm3.ensGene", "TxDb.Hsapiens.UCSC.hg18.knownGene", "RNAseqData.HNRNPC.bam.chr14", "BSgenome.Hsapiens.UCSC.hg19", "RUnit", "BiocStyle", "knitr"], "LinkingTo": ["Rhtslib (>= 3.3.1)", "S4Vectors", "IRanges", "XVector", "Biostrings"], "LazyLoad": "yes", "SystemRequirements": "GNU make", "VignetteBuilder": "knitr", "git_url": "https://git.bioconductor.org/packages/Rsamtools", "git_branch": "devel", "git_last_commit": "e5e8fa0", "git_last_commit_date": "2025-07-28", "Repository": "Bioconductor 3.22", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON><PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [ctb] (Converted Rsamtools vignette from Sweave to RMarkdown / HTML.), Bioconductor Package Maintainer [cre]", "Maintainer": "Bioconductor Package Maintainer <<EMAIL>>"}, "S4Arrays": {"Package": "S4Arrays", "Version": "1.9.1", "Source": "Bioconductor", "Title": "Foundation of array-like containers in Bioconductor", "Description": "The S4Arrays package defines the Array virtual class to be extended by other S4 classes that wish to implement a container with an array-like semantic. It also provides: (1) low-level functionality meant to help the developer of such container to implement basic operations like display, subsetting, or coercion of their array-like objects to an ordinary matrix or array, and (2) a framework that facilitates block processing of array-like objects (typically on-disk objects).", "biocViews": "Infrastructure, DataRepresentation", "URL": "https://bioconductor.org/packages/S4Arrays", "BugReports": "https://github.com/Bioconductor/S4Arrays/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\", comment=c(ORCID=\"0009-0002-8272-4522\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"))", "Depends": ["R (>= 4.3.0)", "methods", "Matrix", "abind", "BiocGenerics (>= 0.45.2)", "S4Vectors", "IRanges"], "Imports": ["stats", "crayon"], "LinkingTo": ["S4Vectors"], "Suggests": ["BiocParallel", "SparseArray (>= 0.0.4)", "DelayedArray", "HDF5Array", "testthat", "knitr", "rmarkdown", "BiocStyle"], "VignetteBuilder": "knitr", "Collate": "utils.R rowsum.R abind.R aperm2.R array_selection.R Nindex-utils.R arep.R array_recycling.R Array-class.R dim-tuning-utils.R Array-subsetting.R Array-subassignment.R ArrayGrid-class.R mapToGrid.R extract_array.R type.R is_sparse.R read_block.R write_block.R show-utils.R Array-kronecker-methods.R zzz.R", "git_url": "https://git.bioconductor.org/packages/S4Arrays", "git_branch": "devel", "git_last_commit": "da54bbd", "git_last_commit_date": "2025-05-27", "Repository": "Bioconductor 3.22", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0009-0002-8272-4522>), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "S4Vectors": {"Package": "S4Vectors", "Version": "0.47.0", "Source": "Bioconductor", "Title": "Foundation of vector-like and list-like containers in Bioconductor", "Description": "The S4Vectors package defines the Vector and List virtual classes and a set of generic functions that extend the semantic of ordinary vectors and lists in R. Package developers can easily implement vector-like or list-like objects as concrete subclasses of Vector or List. In addition, a few low-level concrete subclasses of general interest (e.g. DataFrame, Rle, Factor, and Hits) are implemented in the S4Vectors package itself (many more are implemented in the IRanges package and in other Bioconductor infrastructure packages).", "biocViews": "Infrastructure, DataRepresentation", "URL": "https://bioconductor.org/packages/S4Vectors", "BugReports": "https://github.com/Bioconductor/S4Vectors/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\", comment=\"Converted vignettes from Sweave to RMarkdown\"))", "Depends": ["R (>= 4.0.0)", "methods", "utils", "stats", "stats4", "BiocGenerics (>= 0.53.2)"], "Suggests": ["IRanges", "GenomicRanges", "SummarizedExperiment", "Matrix", "DelayedArray", "ShortRead", "graph", "data.table", "RUnit", "BiocStyle", "knitr"], "VignetteBuilder": "knitr", "Collate": "S4-utils.R show-utils.R utils.R normarg-utils.R bindROWS.R LLint-class.R isSorted.R subsetting-utils.R vector-utils.R integer-utils.R character-utils.R raw-utils.R eval-utils.R map_ranges_to_runs.R RectangularData-class.R Annotated-class.R DataFrame_OR_NULL-class.R Vector-class.R Vector-comparison.R Vector-setops.R Vector-merge.R Hits-class.R Hits-comparison.R Hits-setops.R Rle-class.R Rle-utils.R Factor-class.R List-class.R List-comparison.R splitAsList.R List-utils.R SimpleList-class.R HitsList-class.R DataFrame-class.R DataFrame-combine.R DataFrame-comparison.R DataFrame-utils.R DataFrameFactor-class.R TransposedDataFrame-class.R Pairs-class.R FilterRules-class.R stack-methods.R expand-methods.R aggregate-methods.R shiftApply-methods.R zzz.R", "git_url": "https://git.bioconductor.org/packages/S4Vectors", "git_branch": "devel", "git_last_commit": "4564bfa", "git_last_commit_date": "2025-04-15", "Repository": "Bioconductor 3.22", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], <PERSON><PERSON> [ctb] (Converted vignettes from Sweave to RMarkdown)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "Seqinfo": {"Package": "Seqinfo", "Version": "0.99.2", "Source": "Bioconductor", "Title": "A simple S4 class for storing basic information about a collection of genomic sequences", "Description": "The Seqinfo class stores the names, lengths, circularity flags, and genomes for a particular collection of sequences. These sequences are typically the chromosomes and/or scaffolds of a specific genome assembly of a given organism. Seqinfo objects are rarely used as standalone objects. Instead, they are used as part of higher-level objects to represent their seqinfo() component. Examples of such higher-level objects are GRanges, RangedSummarizedExperiment, VCF, GAlignments, etc... defined in other Bioconductor infrastructure packages.", "biocViews": "Infrastructure, DataRepresentation, GenomeAssembly, Annotation, GenomeAnnotation", "URL": "https://bioconductor.org/packages/Seqinfo", "BugReports": "https://github.com/Bioconductor/Seqinfo/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\", comment=c(ORCID=\"0009-0002-8272-4522\"))", "Depends": ["methods", "BiocGenerics"], "Imports": ["stats", "S4Vectors", "IRanges"], "Suggests": ["GenomeInfoDb", "GenomicRanges", "BSgenome", "GenomicFeatures", "TxDb.Hsapiens.UCSC.hg38.knownGene", "TxDb.Dmelanogaster.UCSC.dm3.ensGene", "BSgenome.Hsapiens.UCSC.hg38", "BSgenome.Celegans.UCSC.ce2", "RUnit", "knitr", "rmarkdown", "BiocStyle"], "VignetteBuilder": "knitr", "Collate": "utils.R rankSeqlevels.R seqinfo.R sortSeqlevels.R Seqinfo-class.R seqlevelsInUse.R GenomeDescription-class.R zzz.R", "git_url": "https://git.bioconductor.org/packages/Seqinfo", "git_branch": "devel", "git_last_commit": "535c4d7", "git_last_commit_date": "2025-07-21", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0009-0002-8272-4522>)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "SparseArray": {"Package": "SparseArray", "Version": "1.9.1", "Source": "Bioconductor", "Title": "High-performance sparse data representation and manipulation in R", "Description": "The SparseArray package provides array-like containers for efficient in-memory representation of multidimensional sparse data in R (arrays and matrices). The package defines the SparseArray virtual class and two concrete subclasses: COO_SparseArray and SVT_SparseArray. Each subclass uses its own internal representation of the nonzero multidimensional data: the \"COO layout\" and the \"SVT layout\", respectively. SVT_SparseArray objects mimic as much as possible the behavior of ordinary matrix and array objects in base R. In particular, they suppport most of the \"standard matrix and array API\" defined in base R and in the matrixStats package from CRAN.", "biocViews": "Infrastructure, DataRepresentation", "URL": "https://bioconductor.org/packages/SparseArray", "BugReports": "https://github.com/Bioconductor/SparseArray/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\", comment=c(ORCID=\"0009-0002-8272-4522\")), person(\"<PERSON>\", \"<PERSON>\", role=\"fnd\", email=\"<EMAIL>\", comment=c(ORCID=\"0000-0003-4046-0063\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"fnd\", email=\"<EMAIL>\", comment=c(ORCID=\"0000-0002-3944-4309\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\", comment=c(ORCID=\"0000-0002-4295-0624\")))", "Depends": ["R (>= 4.3.0)", "methods", "Matrix", "BiocGenerics (>= 0.43.1)", "MatrixGenerics (>= 1.11.1)", "S4Vectors (>= 0.43.2)", "S4Arrays (>= 1.5.11)"], "Imports": ["utils", "stats", "matrixStats", "IRanges", "XVector"], "LinkingTo": ["S4Vectors", "IRanges", "XVector"], "Suggests": ["HDF5Array", "ExperimentHub", "testthat", "knitr", "rmarkdown", "BiocStyle"], "VignetteBuilder": "knitr", "Collate": "utils.R options.R OPBufTree.R thread-control.R sparseMatrix-utils.R is_nonzero.R SparseArray-class.R COO_SparseArray-class.R SVT_SparseArray-class.R extract_sparse_array.R read_block_as_sparse.R SparseArray-dim-tuning.R SparseArray-aperm.R SparseArray-subsetting.R SparseArray-subassignment.R SparseArray-abind.R SparseArray-summarization.R SparseArray-Arith-methods.R SparseArray-Compare-methods.R SparseArray-Logic-methods.R SparseArray-Math-methods.R SparseArray-Complex-methods.R SparseArray-misc-methods.R SparseArray-matrixStats.R rowsum-methods.R SparseMatrix-mult.R randomSparseArray.R readSparseCSV.R is_nonna.R NaArray-class.R NaArray-aperm.R NaArray-subsetting.R NaArray-subassignment.R NaArray-abind.R NaArray-summarization.R NaArray-Arith-methods.R NaArray-Compare-methods.R NaArray-Logic-methods.R NaArray-Math-methods.R NaArray-misc-methods.R NaArray-matrixStats.R zzz.R", "git_url": "https://git.bioconductor.org/packages/SparseArray", "git_branch": "devel", "git_last_commit": "0a2c62a", "git_last_commit_date": "2025-07-18", "Repository": "Bioconductor 3.22", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0009-0002-8272-4522>), <PERSON> [fnd] (ORCID: <https://orcid.org/0000-0003-4046-0063>), <PERSON> [fnd] (ORCID: <https://orcid.org/0000-0002-3944-4309>), <PERSON> [ctb] (ORCID: <https://orcid.org/0000-0002-4295-0624>)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "SummarizedExperiment": {"Package": "SummarizedExperiment", "Version": "1.39.1", "Source": "Bioconductor", "Title": "A container (S4 class) for matrix-like assays", "Description": "The SummarizedExperiment container contains one or more assays, each represented by a matrix-like object of numeric or other mode. The rows typically represent genomic ranges of interest and the columns represent samples.", "biocViews": "Genetics, Infrastructure, Sequencing, Annotation, Coverage, GenomeAnnotation", "URL": "https://bioconductor.org/packages/SummarizedExperiment", "BugReports": "https://github.com/Bioconductor/SummarizedExperiment/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"Obenchain\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\"))", "Depends": ["R (>= 4.0.0)", "methods", "MatrixGenerics (>= 1.1.3)", "GenomicRanges (>= 1.61.1)", "Biobase"], "Imports": ["utils", "stats", "tools", "Matrix", "BiocGenerics (>= 0.51.3)", "S4Vectors (>= 0.33.7)", "IRanges (>= 2.23.9)", "Seqinfo", "S4Arrays (>= 1.1.1)", "DelayedArray (>= 0.31.12)"], "Suggests": ["GenomeInfoDb (>= 1.45.5)", "rhdf5", "HDF5Array (>= 1.7.5)", "annotate", "AnnotationDbi", "GenomicFeatures", "SparseArray", "SingleCellExperiment", "TxDb.Hsapiens.UCSC.hg19.knownGene", "hgu95av2.db", "airway (>= 1.15.1)", "BiocStyle", "knitr", "rmarkdown", "RUnit", "testthat", "digest"], "VignetteBuilder": "knitr", "Collate": "Assays-class.R SummarizedExperiment-class.R RangedSummarizedExperiment-class.R intra-range-methods.R inter-range-methods.R coverage-methods.R combine-methods.R findOverlaps-methods.R nearest-methods.R makeSummarizedExperimentFromExpressionSet.R makeSummarizedExperimentFromDataFrame.R makeSummarizedExperimentFromLoom.R zzz.R", "git_url": "https://git.bioconductor.org/packages/SummarizedExperiment", "git_branch": "devel", "git_last_commit": "a03fd86", "git_last_commit_date": "2025-06-20", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "UCSC.utils": {"Package": "UCSC.utils", "Version": "1.5.0", "Source": "Bioconductor", "Title": "Low-level utilities to retrieve data from the UCSC Genome Browser", "Description": "A set of low-level utilities to retrieve data from the UCSC Genome Browser. Most functions in the package access the data via the UCSC REST API but some of them query the UCSC MySQL server directly. Note that the primary purpose of the package is to support higher-level functionalities implemented in downstream packages like GenomeInfoDb or txdbmaker.", "biocViews": "Infrastructure, GenomeAssembly, Annotation, GenomeAnnotation, DataImport", "URL": "https://bioconductor.org/packages/UCSC.utils", "BugReports": "https://github.com/Bioconductor/UCSC.utils/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\")", "Imports": ["methods", "stats", "httr", "jsonlite", "S4Vectors"], "Suggests": ["DBI", "RMariaDB", "GenomeInfoDb", "testthat", "knitr", "rmarkdown", "BiocStyle"], "VignetteBuilder": "knitr", "Collate": "00utils.R UCSC.api.url.R REST_API.R list_UCSC_genomes.R get_UCSC_chrom_sizes.R list_UCSC_tracks.R fetch_UCSC_track_data.R UCSC_dbselect.R zzz.R", "git_url": "https://git.bioconductor.org/packages/UCSC.utils", "git_branch": "devel", "git_last_commit": "41ea0ce", "git_last_commit_date": "2025-04-15", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "VariantAnnotation": {"Package": "VariantAnnotation", "Version": "1.55.1", "Source": "Bioconductor", "Type": "Package", "Title": "Annotation of Genetic Variants", "Description": "Annotate variants, compute amino acid coding changes, predict coding outcomes.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>chain\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"Bioconductor Package Maintainer\", role=\"cre\", email=\"<EMAIL>\"))", "License": "Artistic-2.0", "Depends": ["R (>= 4.0.0)", "methods", "BiocGenerics (>= 0.37.0)", "MatrixGenerics", "Seqinfo", "GenomicRanges (>= 1.61.1)", "SummarizedExperiment (>= 1.39.1)", "Rsamtools (>= 2.25.1)"], "Imports": ["utils", "DBI", "Biobase", "S4Vectors (>= 0.27.12)", "IRanges (>= 2.23.9)", "XVector (>= 0.29.2)", "Biostrings (>= 2.77.2)", "AnnotationDbi (>= 1.27.9)", "rtracklayer (>= 1.69.1)", "BSgenome (>= 1.77.1)", "GenomicFeatures (>= 1.61.4)"], "Suggests": ["GenomeInfoDb", "RUnit", "AnnotationHub", "BSgenome.Hsapiens.UCSC.hg19", "TxDb.Hsapiens.UCSC.hg19.knownGene", "SNPlocs.Hsapiens.dbSNP144.GRCh37", "SIFT.Hsapiens.dbSNP132", "SIFT.Hsapiens.dbSNP137", "PolyPhen.Hsapiens.dbSNP131", "snpStats", "ggplot2", "BiocStyle", "knitr", "magick", "jsonlite", "httr", "rjsoncons"], "LinkingTo": ["S4Vectors", "IRanges", "XVector", "Biostrings", "Rhtslib (>= 2.99.0)"], "SystemRequirements": "GNU make", "LazyLoad": "yes", "biocViews": "DataImport, Sequencing, SNP, Annotation, Genetics, VariantAnnotation", "Video": "https://www.youtube.com/watch?v=Ro0lHQ_J--I&list=UUqaMSQd_h-2EDGsU6WDiX0Q", "RoxygenNote": "7.3.1", "VignetteBuilder": "knitr", "git_url": "https://git.bioconductor.org/packages/VariantAnnotation", "git_branch": "devel", "git_last_commit": "5b9c386", "git_last_commit_date": "2025-06-20", "Repository": "Bioconductor 3.22", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], Bioconductor Package Maintainer [cre]", "Maintainer": "Bioconductor Package Maintainer <<EMAIL>>"}, "XML": {"Package": "XML", "Version": "3.99-0.19", "Source": "Repository", "Authors@R": "c(person(\"CRAN Team\", role = c('ctb', 'cre'), email = \"<EMAIL>\", comment = \"de facto maintainer since 2013\"), person(\"<PERSON>\", \"<PERSON> Lang\", role = c(\"aut\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0159-1546\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"))", "Title": "Tools for Parsing and Generating XML Within R and S-Plus", "Depends": ["R (>= 4.0.0)", "methods", "utils"], "Suggests": ["bitops", "<PERSON><PERSON><PERSON>"], "SystemRequirements": "libxml2 (>= 2.6.3)", "Description": "Many approaches for both reading and creating XML (and HTML) documents (including DTDs), both local and accessible via HTTP or FTP.  Also offers access to an 'XPath' \"interpreter\".", "URL": "https://www.omegahat.net/RSXML/", "License": "BSD_3_clause + file LICENSE", "Collate": "AAA.R DTD.R DTDClasses.R DTDRef.R SAXMethods.R XMLClasses.R applyDOM.R assignChild.R catalog.R createNode.R dynSupports.R error.R flatTree.R nodeAccessors.R parseDTD.R schema.R summary.R tangle.R toString.R tree.R version.R xmlErrorEnums.R xmlEventHandler.R xmlEventParse.R xmlHandler.R xmlInternalSource.R xmlOutputDOM.R xmlNodes.R xmlOutputBuffer.R xmlTree.R xmlTreeParse.R htmlParse.R hashTree.R zzz.R supports.R parser.R libxmlFeatures.R xmlString.R saveXML.R namespaces.R readHTMLTable.R reflection.R xmlToDataFrame.R bitList.R compare.R encoding.R fixNS.R xmlRoot.R serialize.R xmlMemoryMgmt.R keyValueDB.R solrDocs.R XMLRErrorInfo.R xincludes.R namespaceHandlers.R tangle1.R htmlLinks.R htmlLists.R getDependencies.R getRelativeURL.R xmlIncludes.R simplifyPath.R", "NeedsCompilation": "yes", "Author": "CRAN Team [ctb, cre] (de facto maintainer since 2013), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-0159-1546>), <PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "CRAN Team <<EMAIL>>", "Repository": "CRAN"}, "XVector": {"Package": "XVector", "Version": "0.49.0", "Source": "Bioconductor", "Title": "Foundation of external vector representation and manipulation in Bioconductor", "Description": "Provides memory efficient S4 classes for storing sequences \"externally\" (e.g. behind an R external pointer, or on disk).", "biocViews": "Infrastructure, DataRepresentation", "URL": "https://bioconductor.org/packages/XVector", "BugReports": "https://github.com/Bioconductor/XVector/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Author": "<PERSON><PERSON><PERSON> and <PERSON>", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Depends": ["R (>= 4.0.0)", "methods", "BiocGenerics (>= 0.37.0)", "S4Vectors (>= 0.27.12)", "IRanges (>= 2.23.9)"], "Imports": ["methods", "utils", "tools", "BiocGenerics", "S4Vectors", "IRanges"], "LinkingTo": ["S4Vectors", "IRanges"], "Suggests": ["Biostrings", "drosophila2probe", "RUnit"], "Collate": "io-utils.R RDS-random-access.R SharedVector-class.R SharedRaw-class.R SharedInteger-class.R SharedDouble-class.R XVector-class.R XRaw-class.R XInteger-class.R XDouble-class.R XVectorList-class.R XRawList-class.R XRawList-comparison.R XIntegerViews-class.R XDoubleViews-class.R OnDiskRaw-class.R RdaCollection-class.R RdsCollection-class.R intra-range-methods.R compact-methods.R reverse-methods.R slice-methods.R view-summarization-methods.R updateObject-methods.R zzz.R", "git_url": "https://git.bioconductor.org/packages/XVector", "git_branch": "devel", "git_last_commit": "9fa6469", "git_last_commit_date": "2025-04-15", "Repository": "Bioconductor 3.22", "NeedsCompilation": "yes"}, "abind": {"Package": "abind", "Version": "1.4-8", "Source": "Repository", "Date": "2024-09-08", "Title": "Combine Multidimensional Arrays", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\")))", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Combine multidimensional arrays into a single array. This is a generalization of 'cbind' and 'rbind'.  Works with vectors, matrices, and higher-dimensional arrays (aka tensors). Also provides functions 'adrop', 'asub', and 'afill' for manipulating, extracting and replacing data in arrays.", "Depends": ["R (>= 1.5.0)"], "Imports": ["methods", "utils"], "License": "MIT + file LICENSE", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON> [aut]", "Repository": "CRAN"}, "askpass": {"Package": "askpass", "Version": "1.2.1", "Source": "Repository", "Type": "Package", "Title": "Password Entry Utilities for R, Git, and SSH", "Authors@R": "person(\"<PERSON><PERSON><PERSON>\", \"O<PERSON>\", role = c(\"aut\", \"cre\"),  email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\"))", "Description": "Cross-platform utilities for prompting the user for credentials or a  passphrase, for example to authenticate with a server or read a protected key. Includes native programs for MacOS and Windows, hence no 'tcltk' is required.  Password entry can be invoked in two different ways: directly from R via the  askpass() function, or indirectly as password-entry back-end for 'ssh-agent'  or 'git-credential' via the SSH_ASKPASS and GIT_ASKPASS environment variables. Thereby the user can be prompted for credentials or a passphrase if needed  when R calls out to git or ssh.", "License": "MIT + file LICENSE", "URL": "https://r-lib.r-universe.dev/askpass", "BugReports": "https://github.com/r-lib/askpass/issues", "Encoding": "UTF-8", "Imports": ["sys (>= 2.1)"], "RoxygenNote": "7.2.3", "Suggests": ["testthat"], "Language": "en-US", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "backports": {"Package": "backports", "Version": "1.5.0", "Source": "Repository", "Type": "Package", "Title": "Reimplementations of Functions Introduced Since R-3.0.0", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", <PERSON><PERSON><PERSON>, \"mi<PERSON><PERSON>@gmail.com\", role = c(\"cre\", \"aut\"), comment = c(ORCID = \"0000-0001-9754-0393\")), person(\"<PERSON>\", \"<PERSON>\", NULL, \"<EMAIL>\", role = c(\"aut\")), person(\"R Core Team\", role = \"aut\"))", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Functions introduced or changed since R v3.0.0 are re-implemented in this package. The backports are conditionally exported in order to let R resolve the function name to either the implemented backport, or the respective base version, if available. Package developers can make use of new functions or arguments by selectively importing specific backports to support older installations.", "URL": "https://github.com/r-lib/backports", "BugReports": "https://github.com/r-lib/backports/issues", "License": "GPL-2 | GPL-3", "NeedsCompilation": "yes", "ByteCompile": "yes", "Depends": ["R (>= 3.0.0)"], "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "Author": "<PERSON> [cre, aut] (<https://orcid.org/0000-0001-9754-0393>), <PERSON> [aut], R <PERSON> [aut]", "Repository": "CRAN"}, "base64enc": {"Package": "base64enc", "Version": "0.1-3", "Source": "Repository", "Title": "Tools for base64 encoding", "Author": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Maintainer": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Depends": ["R (>= 2.9.0)"], "Enhances": ["png"], "Description": "This package provides tools for handling base64 encoding. It is more flexible than the orphaned base64 package.", "License": "GPL-2 | GPL-3", "URL": "http://www.rforge.net/base64enc", "NeedsCompilation": "yes", "Repository": "CRAN"}, "basilisk": {"Package": "basilisk", "Version": "1.21.5", "Source": "Bioconductor", "Date": "2025-05-19", "Title": "Freezing Python Dependencies Inside Bioconductor Packages", "Authors@R": "c(person(\"<PERSON>\", \"Lun\", role=c(\"aut\", \"cre\", \"cph\"), email=\"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"))", "Depends": ["reticulate"], "Imports": ["utils", "methods", "parallel", "dir.expiry"], "Suggests": ["knitr", "rmarkdown", "BiocStyle", "testthat", "callr"], "biocViews": "Infrastructure", "Description": "Installs a self-contained conda instance that is managed by the R/Bioconductor installation machinery. This aims to provide a consistent Python environment that can be used reliably by Bioconductor packages. Functions are also provided to enable smooth interoperability of multiple Python environments in a single R session.", "License": "GPL-3", "RoxygenNote": "7.3.2", "StagedInstall": "false", "VignetteBuilder": "knitr", "BugReports": "https://github.com/LTLA/basilisk/issues", "Encoding": "UTF-8", "git_url": "https://git.bioconductor.org/packages/basilisk", "git_branch": "devel", "git_last_commit": "e93af07", "git_last_commit_date": "2025-05-19", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre, cph], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>"}, "biomaRt": {"Package": "biomaRt", "Version": "2.65.3", "Source": "Bioconductor", "Title": "Interface to BioMart databases (i.e. Ensembl)", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON> S\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"ctb\", \"cre\"), comment = c(ORCID = \"0000-0002-7800-3848\")) )", "Description": "In recent years a wealth of biological data has become available in public data repositories. Easy access to these valuable data resources and firm integration with data analysis is needed for comprehensive bioinformatics data analysis.  biomaRt provides an interface to a growing collection of databases implementing the BioMart software suite (<http://www.biomart.org>). The package enables retrieval of large amounts of data in a uniform way without the need to know the underlying database schemas or write complex SQL queries. The most prominent examples of BioMart databases are maintain by Ensembl, which provides biomaRt users direct access to a diverse set of data and enables a wide range of powerful online queries from gene annotation to database mining.", "License": "Artistic-2.0", "URL": "https://github.com/Huber-group-EMBL/biomaRt", "BugReports": "https://github.com/Huber-group-EMBL/biomaRt/issues", "Depends": ["R (>= 4.1.0)", "methods"], "Imports": ["AnnotationDbi", "BiocFileCache", "curl", "digest", "httr2", "progress", "rapp<PERSON>s", "stringr", "utils", "xml2"], "Suggests": ["BiocStyle", "httptest2", "knitr", "mockery", "rmarkdown", "testthat"], "VignetteBuilder": "knitr", "biocViews": "Annotation", "Encoding": "UTF-8", "LazyLoad": "yes", "NeedsCompilation": "no", "RoxygenNote": "7.3.2", "Collate": "'biomaRtClasses.R' 'methods-Mart.R' 'biomaRt.R' 'caching.R' 'ensembl.R' 'ensembl_wrappers.R' 'ensembl_ssl_settings.R' 'utilityFunctions.R' 'non-biomart-utils.R'", "git_url": "https://git.bioconductor.org/packages/biomaRt", "git_branch": "devel", "git_last_commit": "3d9094f", "git_last_commit_date": "2025-08-20", "Repository": "Bioconductor 3.22", "Author": "<PERSON><PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb, cre] (ORCID: <https://orcid.org/0000-0002-7800-3848>)", "Maintainer": "<PERSON> <<EMAIL>>"}, "bit": {"Package": "bit", "Version": "4.6.0", "Source": "Repository", "Title": "Classes and Methods for Fast Memory-Efficient Boolean Selections", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\") )", "Depends": ["R (>= 3.4.0)"], "Suggests": ["testthat (>= 3.0.0)", "roxygen2", "knitr", "markdown", "rmarkdown", "microbenchmark", "bit64 (>= 4.0.0)", "ff (>= 4.0.0)"], "Description": "Provided are classes for boolean and skewed boolean vectors, fast boolean methods, fast unique and non-unique integer sorting, fast set operations on sorted and unsorted sets of integers, and foundations for ff (range index, compression, chunked processing).", "License": "GPL-2 | GPL-3", "LazyLoad": "yes", "ByteCompile": "yes", "Encoding": "UTF-8", "URL": "https://github.com/r-lib/bit", "VignetteBuilder": "knitr, rmarkdown", "RoxygenNote": "7.3.2", "Config/testthat/edition": "3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "bit64": {"Package": "bit64", "Version": "4.6.0-1", "Source": "Repository", "Title": "A S3 Class for Vectors of 64bit Integers", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"micha<PERSON><PERSON><PERSON><EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\") )", "Depends": ["R (>= 3.4.0)", "bit (>= 4.0.0)"], "Description": "Package 'bit64' provides serializable S3 atomic 64bit (signed) integers. These are useful for handling database keys and exact counting in +-2^63. WARNING: do not use them as replacement for 32bit integers, integer64 are not supported for subscripting by R-core and they have different semantics when combined with double, e.g. integer64 + double => integer64. Class integer64 can be used in vectors, matrices, arrays and data.frames. Methods are available for coercion from and to logicals, integers, doubles, characters and factors as well as many elementwise and summary functions. Many fast algorithmic operations such as 'match' and 'order' support inter- active data exploration and manipulation and optionally leverage caching.", "License": "GPL-2 | GPL-3", "LazyLoad": "yes", "ByteCompile": "yes", "URL": "https://github.com/r-lib/bit64", "Encoding": "UTF-8", "Imports": ["graphics", "methods", "stats", "utils"], "Suggests": ["testthat (>= 3.0.3)", "withr"], "Config/testthat/edition": "3", "Config/needs/development": "testthat", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [ctb], <PERSON><PERSON> [ctb]", "Maintainer": "<PERSON> <michael<PERSON><EMAIL>>", "Repository": "CRAN"}, "bitops": {"Package": "bitops", "Version": "1.0-9", "Source": "Repository", "Date": "2024-10-03", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\", comment = \"S original; then (after MM's port) revised and modified\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"cre\", \"aut\"), email = \"<EMAIL>\", comment = c(\"Initial R port; tweaks\", ORCID = \"0000-0002-8685-9910\")))", "Title": "Bitwise Operations", "Description": "Functions for bitwise operations on integer vectors.", "License": "GPL (>= 2)", "URL": "https://github.com/mmaechler/R-bitops", "BugReports": "https://github.com/mmaechler/R-bitops/issues", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (S original; then (after MM's port) revised and modified), <PERSON> [cre, aut] (Initial R port; tweaks, <https://orcid.org/0000-0002-8685-9910>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "blob": {"Package": "blob", "Version": "1.2.4", "Source": "Repository", "Title": "A Simple S3 Class for Representing Vectors of Binary Data ('BLOBS')", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"cre\"), person(\"RStudio\", role = c(\"cph\", \"fnd\")) )", "Description": "R's raw vector is useful for storing a single binary object. What if you want to put a vector of them in a data frame? The 'blob' package provides the blob object, a list of raw vectors, suitable for use as a column in data frame.", "License": "MIT + file LICENSE", "URL": "https://blob.tidyverse.org, https://github.com/tidyverse/blob", "BugReports": "https://github.com/tidyverse/blob/issues", "Imports": ["methods", "rlang", "vctrs (>= 0.2.1)"], "Suggests": ["covr", "crayon", "pillar (>= 1.2.1)", "testthat"], "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "false", "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON><PERSON> [cre], <PERSON><PERSON><PERSON> [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "brew": {"Package": "brew", "Version": "1.0-10", "Source": "Repository", "Type": "Package", "Title": "Templating Framework for Report Generation", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cph\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\")) )", "Description": "Implements a templating framework for mixing text and R code for report generation. brew template syntax is similar to PHP, Ruby's erb module, Java Server Pages, and Python's psp module.", "License": "GPL (>= 2)", "URL": "https://github.com/gregfrog/brew", "BugReports": "https://github.com/gregfrog/brew/issues", "Suggests": ["testthat (>= 3.0.0)"], "Config/testthat/edition": "3", "Encoding": "UTF-8", "Repository": "CRAN", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cph], <PERSON> [aut, cre, cph]", "Maintainer": "<PERSON> <<EMAIL>>"}, "cachem": {"Package": "cachem", "Version": "1.1.0", "Source": "Repository", "Title": "<PERSON><PERSON> R Objects with Automatic Pruning", "Description": "Key-value stores with automatic pruning. Caches can limit either their total size or the age of the oldest object (or both), automatically pruning objects to maintain the constraints.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", c(\"aut\", \"cre\")), person(family = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")))", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "ByteCompile": "true", "URL": "https://cachem.r-lib.org/, https://github.com/r-lib/cachem", "Imports": ["rlang", "fastmap (>= 1.2.0)"], "Suggests": ["testthat"], "RoxygenNote": "7.2.3", "Config/Needs/routine": "lobstr", "Config/Needs/website": "pkgdown", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "callr": {"Package": "callr", "Version": "3.7.6", "Source": "Repository", "Title": "Call R from R", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\"), comment = c(ORCID = \"0000-0001-7098-9676\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"Ascent Digital Services\", role = c(\"cph\", \"fnd\")) )", "Description": "It is sometimes useful to perform a computation in a separate R process, without affecting the current R process at all.  This packages does exactly that.", "License": "MIT + file LICENSE", "URL": "https://callr.r-lib.org, https://github.com/r-lib/callr", "BugReports": "https://github.com/r-lib/callr/issues", "Depends": ["R (>= 3.4)"], "Imports": ["processx (>= 3.6.1)", "R6", "utils"], "Suggests": ["asciicast (>= 2.3.1)", "cli (>= 1.1.0)", "mockery", "ps", "rprojroot", "spelling", "testthat (>= 3.2.0)", "withr (>= 2.3.0)"], "Config/Needs/website": "r-lib/asciicast, glue, htmlwidgets, igraph, tibble, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.3.1.9000", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre, cph] (<https://orcid.org/0000-0001-7098-9676>), <PERSON> [aut], Posit Software, PBC [cph, fnd], Ascent Digital Services [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "cli": {"Package": "cli", "Version": "3.6.5", "Source": "Repository", "Title": "Helpers for Developing Command Line Interfaces", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"g<PERSON><PERSON>@posit.co\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-5329-5987\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A suite of tools to build attractive command line interfaces ('CLIs'), from semantic elements: headings, lists, alerts, paragraphs, etc. Supports custom themes via a 'CSS'-like language. It also contains a number of lower level 'CLI' elements: rules, boxes, trees, and 'Unicode' symbols with 'ASCII' alternatives. It support ANSI colors and text styles as well.", "License": "MIT + file LICENSE", "URL": "https://cli.r-lib.org, https://github.com/r-lib/cli", "BugReports": "https://github.com/r-lib/cli/issues", "Depends": ["R (>= 3.4)"], "Imports": ["utils"], "Suggests": ["callr", "covr", "crayon", "digest", "glue (>= 1.6.0)", "grDevices", "htmltools", "htmlwidgets", "knitr", "methods", "processx", "ps (>= 1.3.4.9000)", "rlang (>= 1.0.2.9003)", "rmarkdown", "rprojroot", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testthat (>= 3.2.0)", "tibble", "whoami", "withr"], "Config/Needs/website": "r-lib/asciicast, bench, brio, cpp11, decor, desc, fansi, prettyunits, sessioninfo, tidyverse/tidytemplate, usethis, vctrs", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-5329-5987>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "clipr": {"Package": "clipr", "Version": "0.8.0", "Source": "Repository", "Type": "Package", "Title": "Read and Write from the System Clipboard", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4387-3384\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\") )", "Description": "Simple utility functions to read from and write to the Windows, OS X, and X11 clipboards.", "License": "GPL-3", "URL": "https://github.com/mdlincoln/clipr, http://matthewlincoln.net/clipr/", "BugReports": "https://github.com/mdlincoln/clipr/issues", "Imports": ["utils"], "Suggests": ["covr", "knitr", "rmarkdown", "rstudioa<PERSON> (>= 0.5)", "testthat (>= 2.0.0)"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.1.2", "SystemRequirements": "xclip (https://github.com/astrand/xclip) or xsel (http://www.vergenet.net/~conrad/software/xsel/) for accessing the X11 clipboard, or wl-clipboard (https://github.com/bugaevc/wl-clipboard) for systems using Wayland.", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-4387-3384>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "collections": {"Package": "collections", "Version": "0.3.9", "Source": "Repository", "Type": "Package", "Title": "High Performance Container Data Types", "Date": "2025-08-16", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = \"cph\", comment = \"tommy hash table library\"), person(given = \"Yann\", family = \"Collet\", role = \"cph\", comment = \"xxhash algorithm\"))", "Description": "Provides high performance container data types such as queues, stacks, deques, dicts and ordered dicts. Benchmarks <https://randy3k.github.io/collections/articles/benchmark.html> have shown that these containers are asymptotically more efficient than those offered by other packages.", "License": "MIT + file LICENSE", "URL": "https://github.com/randy3k/collections/", "Suggests": ["testthat (>= 2.3.1)"], "ByteCompile": "yes", "Encoding": "UTF-8", "NeedsCompilation": "yes", "RoxygenNote": "7.1.0", "Author": "<PERSON> [aut, cre], <PERSON> [cph] (tommy hash table library), <PERSON><PERSON> [cph] (xxhash algorithm)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "commonmark": {"Package": "commonmark", "Version": "2.0.0", "Source": "Repository", "Type": "Package", "Title": "High Performance CommonMark and Github Markdown Rendering in R", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", ,\"jero<PERSON><PERSON>@gmail.com\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", role = \"cph\", comment = \"Author of cmark\"))", "Description": "The CommonMark specification <https://github.github.com/gfm/> defines a rationalized version of markdown syntax. This package uses the 'cmark'  reference implementation for converting markdown text into various formats including html, latex and groff man. In addition it exposes the markdown parse tree in xml format. Also includes opt-in support for GFM extensions including tables, autolinks, and strikethrough text.", "License": "BSD_2_clause + file LICENSE", "URL": "https://docs.ropensci.org/commonmark/ https://ropensci.r-universe.dev/commonmark", "BugReports": "https://github.com/r-lib/commonmark/issues", "Suggests": ["curl", "testthat", "xml2"], "RoxygenNote": "7.3.2", "Language": "en-US", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-4035-0289>), <PERSON> [cph] (Author of cmark)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "cpp11": {"Package": "cpp11", "Version": "0.5.2", "Source": "Repository", "Title": "A C++11 Interface for R's C Interface", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4777-038X\")), person(\"<PERSON>\",\"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2739-7082\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2444-4226\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Provides a header only, C++11 interface to R's C interface.  Compared to other approaches 'cpp11' strives to be safe against long jumps from the C API as well as C++ exceptions, conform to normal R function semantics and supports interaction with 'ALTREP' vectors.", "License": "MIT + file LICENSE", "URL": "https://cpp11.r-lib.org, https://github.com/r-lib/cpp11", "BugReports": "https://github.com/r-lib/cpp11/issues", "Depends": ["R (>= 4.0.0)"], "Suggests": ["bench", "brio", "callr", "cli", "covr", "decor", "desc", "ggplot2", "glue", "knitr", "lobstr", "mockery", "progress", "rmarkdown", "scales", "Rcpp", "testthat (>= 3.2.0)", "tibble", "utils", "vctrs", "withr"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/Needs/cpp11/cpp_register": "brio, cli, decor, desc, glue, tibble, vctrs", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-4777-038X>), <PERSON> [aut] (<https://orcid.org/0000-0002-2739-7082>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-2444-4226>), <PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "crayon": {"Package": "crayon", "Version": "1.5.3", "Source": "Repository", "Title": "Colored Terminal Output", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON><PERSON>\", \"Gaslam\", , \"<EMAIL>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "The crayon package is now superseded. Please use the 'cli' package for new projects.  Colored terminal output on terminals that support 'ANSI' color and highlight codes. It also works in 'Emacs' 'ESS'. 'ANSI' color support is automatically detected. Colors and highlighting can be combined and nested. New styles can also be created easily.  This package was inspired by the 'chalk' 'JavaScript' project.", "License": "MIT + file LICENSE", "URL": "https://r-lib.github.io/crayon/, https://github.com/r-lib/crayon", "BugReports": "https://github.com/r-lib/crayon/issues", "Imports": ["grDevices", "methods", "utils"], "Suggests": ["mockery", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testthat", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "Collate": "'aaa-rstudio-detect.R' 'aaaa-rematch2.R' 'aab-num-ansi-colors.R' 'aac-num-ansi-colors.R' 'ansi-256.R' 'ansi-palette.R' 'combine.R' 'string.R' 'utils.R' 'crayon-package.R' 'disposable.R' 'enc-utils.R' 'has_ansi.R' 'has_color.R' 'link.R' 'styles.R' 'machinery.R' 'parts.R' 'print.R' 'style-var.R' 'show.R' 'string_operations.R'", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON><PERSON><PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "crisprBase": {"Package": "crisprBase", "Version": "1.13.0", "Source": "Bioconductor", "Date": "2024-07-23", "Title": "Base functions and classes for CRISPR gRNA design", "Authors@R": "person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\"))", "Depends": ["utils", "methods", "R (>= 4.1)"], "Imports": ["BiocGenerics", "Biostrings", "GenomicRanges", "graphics", "IRanges", "S4Vectors", "stringr"], "Suggests": ["BiocStyle", "knitr", "rmarkdown", "testthat"], "biocViews": "CRISPR, FunctionalGenomics", "Description": "Provides S4 classes for general nucleases, CRISPR nucleases, CRISPR nickases, and base editors.Several CRISPR-specific genome arithmetic functions are implemented to help extract genomic coordinates of spacer and protospacer sequences. Commonly-used CRISPR nuclease objects are provided that can be readily used in other packages. Both DNA- and RNA-targeting nucleases are supported.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "VignetteBuilder": "knitr", "BugReports": "https://github.com/crisprVerse/crisprBase/issues", "URL": "https://github.com/crisprVerse/crisprBase", "Collate": "'AllGenerics.R' 'rebase.R' 'Nuclease-class.R' 'Nickase-class.R' 'CrisprNuclease-class.R' 'CrisprNickase-class.R' 'BaseEditor-class.R' 'arithmetics.R' 'data.R' 'utils.R' 'converters.R' 'annotateMismatches.R'", "LazyData": "true", "git_url": "https://git.bioconductor.org/packages/crisprBase", "git_branch": "devel", "git_last_commit": "8f83bde", "git_last_commit_date": "2025-04-15", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "crisprBowtie": {"Package": "crisprB<PERSON>tie", "Version": "1.13.1", "Source": "Bioconductor", "Date": "2025-07-22", "Title": "Bowtie-based alignment of CRISPR gRNA spacer sequences", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")))", "Depends": ["methods"], "Imports": ["BiocGenerics", "Biostrings", "BSgenome", "crisprBase (>= 0.99.15)", "Seqinfo", "GenomicRanges", "IRanges", "<PERSON><PERSON><PERSON>", "readr", "stats", "stringr", "utils"], "Suggests": ["BiocStyle", "BSgenome.Hsapiens.UCSC.hg38", "knitr", "rmarkdown", "testthat"], "biocViews": "CRISPR, FunctionalGenomics, Alignment", "Description": "Provides a user-friendly interface to map on-targets and off-targets of CRISPR gRNA spacer sequences using bowtie. The alignment is fast,  and can be performed using either commonly-used or custom CRISPR nucleases. The alignment can work with any reference or custom genomes. Both DNA- and RNA-targeting nucleases are supported.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "RoxygenNote": "7.2.1", "VignetteBuilder": "knitr", "BugReports": "https://github.com/crisprVerse/crisprBowtie/issues", "URL": "https://github.com/crisprVerse/crisprBowtie", "git_url": "https://git.bioconductor.org/packages/crisprBowtie", "git_branch": "devel", "git_last_commit": "353222f", "git_last_commit_date": "2025-07-22", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "crisprDesign": {"Package": "crisprDesign", "Version": "1.11.1", "Source": "Bioconductor", "Title": "Comprehensive design of CRISPR gRNAs for nucleases and base editors", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\")) )", "Description": "Provides a comprehensive suite of functions to design and annotate CRISPR guide RNA (gRNAs) sequences. This  includes on- and off-target search, on-target efficiency scoring, off-target scoring, full gene and TSS contextual annotations, and SNP annotation (human only). It currently support five types of CRISPR  modalities (modes of perturbations): CRISPR knockout, CRISPR activation, CRISPR inhibition,  CRISPR base editing, and CRISPR knockdown. All types of CRISPR nucleases are supported, including DNA- and RNA-target nucleases such as Cas9, Cas12a, and Cas13d. All types of base editors are also supported. gRNA design can be performed on reference genomes, transcriptomes, and custom DNA and RNA sequences. Both unpaired and paired gRNA designs are enabled.", "Depends": ["R (>= 4.2.0)", "crisprBase (>= 1.1.3)"], "Imports": ["AnnotationDbi", "BiocGenerics", "Biostrings (>= 2.77.2)", "BSgenome (>= 1.77.1)", "crisprBowtie (>= 0.99.8)", "crisprScore (>= 1.1.6)", "GenomeInfoDb (>= 1.45.7)", "GenomicFeatures (>= 1.61.4)", "GenomicRanges (>= 1.61.1)", "IRanges", "Matrix", "MatrixGenerics", "methods", "rtracklayer (>= 1.69.1)", "S4Vectors", "Seqinfo", "stats", "txdbmaker (>= 1.5.6)", "utils", "VariantAnnotation (>= 1.55.1)"], "Suggests": ["biomaRt", "BSgenome.Hsapiens.UCSC.hg38", "BSgenome.Mmusculus.UCSC.mm10", "BiocStyle", "crisprBwa (>= 0.99.7)", "knitr", "rmarkdown", "<PERSON><PERSON><PERSON>", "Rbwa", "<PERSON><PERSON><PERSON>", "testthat"], "biocViews": "CRISPR, FunctionalGenomics, GeneTarget", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "VignetteBuilder": "knitr", "BugReports": "https://github.com/crisprVerse/crisprDesign/issues", "URL": "https://github.com/crisprVerse/crisprDesign", "LazyData": "true", "git_url": "https://git.bioconductor.org/packages/crisprDesign", "git_branch": "devel", "git_last_commit": "eb7b7de", "git_last_commit_date": "2025-07-17", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [aut]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "crisprScore": {"Package": "crisprScore", "Version": "1.13.1", "Source": "Bioconductor", "Date": "2025-06-20", "Title": "On-Target and Off-Target Scoring Algorithms for CRISPR gRNAs", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"ctb\")), person(\"<PERSON><PERSON><PERSON>\", \"Perampalam\", email = \"<EMAIL>\", role = c(\"ctb\")))", "Depends": ["R (>= 4.1)", "crisprScoreData (>= 1.1.3)"], "Imports": ["basilisk (>= 1.9.2)", "BiocGenerics", "Biostrings", "IRanges", "methods", "randomForest", "reticulate", "stringr", "utils", "XVector"], "Suggests": ["BiocStyle", "knitr", "rmarkdown", "testthat"], "biocViews": "CRISPR, FunctionalGenomics, FunctionalPrediction", "Description": "Provides R wrappers of several on-target and off-target scoring methods for CRISPR guide RNAs (gRNAs). The following nucleases are supported: SpCas9, AsCas12a, enAsCas12a, and RfxCas13d (CasRx). The available on-target cutting efficiency scoring methods are RuleSet1, Azimuth, DeepHF, DeepCpf1,  enPAM+GB, and CRISPRscan. Both the CFD and MIT scoring methods are available for off-target specificity prediction. The package also provides a Lindel-derived score to predict the probability of a gRNA to produce indels inducing a frameshift for the Cas9 nuclease. Note that DeepHF, DeepCpf1 and enPAM+GB are not available on Windows machines.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "VignetteBuilder": "knitr", "StagedInstall": "no", "BugReports": "https://github.com/crisprVerse/crisprScore", "URL": "https://github.com/crisprVerse/crisprScore/issues", "LazyData": "true", "git_url": "https://git.bioconductor.org/packages/crisprScore", "git_branch": "devel", "git_last_commit": "0e5cd77", "git_last_commit_date": "2025-07-09", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre, cph], <PERSON> [aut], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "crisprScoreData": {"Package": "crisprScoreData", "Version": "1.13.0", "Source": "Bioconductor", "Date": "2022-10-12", "Title": "Pre-trained models for the crisprScore package", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\")))", "Depends": ["ExperimentHub"], "Imports": ["AnnotationHub", "utils"], "Suggests": ["BiocStyle", "knitr", "rmarkdown", "testthat"], "biocViews": "ExperimentHub, Homo_sapiens_Data", "Description": "Provides an interface to access pre-trained models for on-target and off-target gRNA activity prediction algorithms implemented in the crisprScore package. Pre-trained model data are stored in  the ExperimentHub database. Users should consider using the crisprScore package directly to use and load the pre-trained models.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "RoxygenNote": "7.1.2", "VignetteBuilder": "knitr", "BugReports": "https://github.com/crisprVerse/crisprScoreData", "URL": "https://github.com/crisprVerse/crisprScoreData/issues", "git_url": "https://git.bioconductor.org/packages/crisprScoreData", "git_branch": "devel", "git_last_commit": "1d6e4b8", "git_last_commit_date": "2025-04-15", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre, cph]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>"}, "curl": {"Package": "curl", "Version": "7.0.0", "Source": "Repository", "Type": "Package", "Title": "A Modern and Flexible Web Client for R", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"O<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = \"cph\"))", "Description": "Bindings to 'libcurl' <https://curl.se/libcurl/> for performing fully configurable HTTP/FTP requests where responses can be processed in memory, on disk, or streaming via the callback or connection interfaces. Some knowledge of 'libcurl' is recommended; for a more-user-friendly web client see the  'httr2' package which builds on this package with http specific tools and logic.", "License": "MIT + file LICENSE", "SystemRequirements": "libcurl (>= 7.73): libcurl-devel (rpm) or libcurl4-openssl-dev (deb)", "URL": "https://jeroen.r-universe.dev/curl", "BugReports": "https://github.com/jeroen/curl/issues", "Suggests": ["spelling", "testthat (>= 1.0.0)", "knitr", "jsonlite", "later", "rmarkdown", "httpuv (>= 1.4.4)", "webutils"], "VignetteBuilder": "knitr", "Depends": ["R (>= 3.0.0)"], "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "Language": "en-US", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-4035-0289>), <PERSON> [ctb], Posit Software, PBC [cph]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "dbplyr": {"Package": "dbplyr", "Version": "2.5.0", "Source": "Repository", "Type": "Package", "Title": "A 'dplyr' <PERSON> End for Databases", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A 'dplyr' back end for databases that allows you to work with remote database tables as if they are in-memory data frames.  Basic features works with any database that has a 'DBI' back end; more advanced features require 'SQL' translation to be provided by the package author.", "License": "MIT + file LICENSE", "URL": "https://dbplyr.tidyverse.org/, https://github.com/tidyverse/dbplyr", "BugReports": "https://github.com/tidyverse/dbplyr/issues", "Depends": ["R (>= 3.6)"], "Imports": ["blob (>= 1.2.0)", "cli (>= 3.6.1)", "DBI (>= 1.1.3)", "dplyr (>= 1.1.2)", "glue (>= 1.6.2)", "lifecycle (>= 1.0.3)", "magrit<PERSON>", "methods", "pillar (>= 1.9.0)", "purrr (>= 1.0.1)", "R6 (>= 2.2.2)", "rlang (>= 1.1.1)", "tibble (>= 3.2.1)", "tidyr (>= 1.3.0)", "tidyselect (>= 1.2.1)", "utils", "vctrs (>= 0.6.3)", "withr (>= 2.5.0)"], "Suggests": ["bit64", "covr", "knitr", "<PERSON><PERSON>", "nycflights13", "odbc (>= 1.4.2)", "RMariaDB (>= 1.2.2)", "rmarkdown", "RPostgres (>= 1.4.5)", "RPostgreSQL", "RSQLite (>= 2.3.1)", "testthat (>= 3.1.10)"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "TRUE", "Encoding": "UTF-8", "Language": "en-gb", "RoxygenNote": "7.3.1", "Collate": "'db-sql.R' 'utils-check.R' 'import-standalone-types-check.R' 'import-standalone-obj-type.R' 'utils.R' 'sql.R' 'escape.R' 'translate-sql-cut.R' 'translate-sql-quantile.R' 'translate-sql-string.R' 'translate-sql-paste.R' 'translate-sql-helpers.R' 'translate-sql-window.R' 'translate-sql-conditional.R' 'backend-.R' 'backend-access.R' 'backend-hana.R' 'backend-hive.R' 'backend-impala.R' 'verb-copy-to.R' 'backend-mssql.R' 'backend-mysql.R' 'backend-odbc.R' 'backend-oracle.R' 'backend-postgres.R' 'backend-postgres-old.R' 'backend-redshift.R' 'backend-snowflake.R' 'backend-spark-sql.R' 'backend-sqlite.R' 'backend-teradata.R' 'build-sql.R' 'data-cache.R' 'data-lahman.R' 'data-nycflights13.R' 'db-escape.R' 'db-io.R' 'db.R' 'dbplyr.R' 'explain.R' 'ident.R' 'import-standalone-s3-register.R' 'join-by-compat.R' 'join-cols-compat.R' 'lazy-join-query.R' 'lazy-ops.R' 'lazy-query.R' 'lazy-select-query.R' 'lazy-set-op-query.R' 'memdb.R' 'optimise-utils.R' 'pillar.R' 'progress.R' 'sql-build.R' 'query-join.R' 'query-select.R' 'query-semi-join.R' 'query-set-op.R' 'query.R' 'reexport.R' 'remote.R' 'rows.R' 'schema.R' 'simulate.R' 'sql-clause.R' 'sql-expr.R' 'src-sql.R' 'src_dbi.R' 'table-name.R' 'tbl-lazy.R' 'tbl-sql.R' 'test-frame.R' 'testthat.R' 'tidyeval-across.R' 'tidyeval.R' 'translate-sql.R' 'utils-format.R' 'verb-arrange.R' 'verb-compute.R' 'verb-count.R' 'verb-distinct.R' 'verb-do-query.R' 'verb-do.R' 'verb-expand.R' 'verb-fill.R' 'verb-filter.R' 'verb-group_by.R' 'verb-head.R' 'verb-joins.R' 'verb-mutate.R' 'verb-pivot-longer.R' 'verb-pivot-wider.R' 'verb-pull.R' 'verb-select.R' 'verb-set-ops.R' 'verb-slice.R' 'verb-summarise.R' 'verb-uncount.R' 'verb-window.R' 'zzz.R'", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "desc": {"Package": "desc", "Version": "1.4.3", "Source": "Repository", "Title": "Manipulate DESCRIPTION Files", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"csar<PERSON>.<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-2815-0399\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Description": "Tools to read, write, create, and manipulate DESCRIPTION files.  It is intended for packages that create or manipulate other packages.", "License": "MIT + file LICENSE", "URL": "https://desc.r-lib.org/, https://github.com/r-lib/desc", "BugReports": "https://github.com/r-lib/desc/issues", "Depends": ["R (>= 3.4)"], "Imports": ["cli", "R6", "utils"], "Suggests": ["callr", "covr", "gh", "spelling", "testthat", "whoami", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.2.3", "Collate": "'assertions.R' 'authors-at-r.R' 'built.R' 'classes.R' 'collate.R' 'constants.R' 'deps.R' 'desc-package.R' 'description.R' 'encoding.R' 'find-package-root.R' 'latex.R' 'non-oo-api.R' 'package-archives.R' 'read.R' 'remotes.R' 'str.R' 'syntax_checks.R' 'urls.R' 'utils.R' 'validate.R' 'version.R'", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-2815-0399>), Posit Software, PBC [cph, fnd]", "Repository": "CRAN"}, "digest": {"Package": "digest", "Version": "0.6.37", "Source": "Repository", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-6419-907X\")), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\", comment = c(ORCID = \"0000-0002-7579-5165\")), person(\"<PERSON>\", \"<PERSON>ek\", role=\"ctb\", comment = c(ORCID = \"0000-0003-2297-1732\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\", comment = c(ORCID = \"0000-0001-5180-0567\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\", comment = c(ORCID = \"0000-0001-6786-5453\")), person(\"Thierry\", \"Onkelinx\", role=\"ctb\", comment = c(ORCID = \"0000-0001-8804-4216\")), person(\"Michel\", \"Lang\", role=\"ctb\", comment = c(ORCID = \"0000-0001-9754-0393\")), person(\"Viliam\", \"Simko\", role=\"ctb\"), person(\"Kurt\", \"Hornik\", role=\"ctb\", comment = c(ORCID = \"0000-0003-4198-9911\")), person(\"Radford\", \"Neal\", role=\"ctb\", comment = c(ORCID = \"0000-0002-2473-3407\")), person(\"Kendon\", \"Bell\", role=\"ctb\", comment = c(ORCID = \"0000-0002-9093-8312\")), person(\"Matthew\", \"de Queljoe\", role=\"ctb\"), person(\"Dmitry\", \"Selivanov\", role=\"ctb\"), person(\"Ion\", \"Suruceanu\", role=\"ctb\"), person(\"Bill\", \"Denney\", role=\"ctb\"), person(\"Dirk\", \"Schumacher\", role=\"ctb\"), person(\"András\", \"Svraka\", role=\"ctb\"), person(\"Sergey\", \"Fedorov\", role=\"ctb\"), person(\"Will\", \"Landau\", role=\"ctb\", comment = c(ORCID = \"0000-0003-1878-3253\")), person(\"Floris\", \"Vanderhaeghe\", role=\"ctb\", comment = c(ORCID = \"0000-0002-6378-6229\")), person(\"Kevin\", \"Tappe\", role=\"ctb\"), person(\"Harris\", \"McGehee\", role=\"ctb\"), person(\"Tim\", \"Mastny\", role=\"ctb\"), person(\"Aaron\", \"Peikert\", role=\"ctb\", comment = c(ORCID = \"0000-0001-7813-818X\")), person(\"Mark\", \"van der Loo\", role=\"ctb\", comment = c(ORCID = \"0000-0002-9807-4686\")), person(\"Chris\", \"Muir\", role=\"ctb\", comment = c(ORCID = \"0000-0003-2555-3878\")), person(\"Moritz\", \"Beller\", role=\"ctb\", comment = c(ORCID = \"0000-0003-4852-0526\")), person(\"Sebastian\", \"Campbell\", role=\"ctb\"), person(\"Winston\", \"Chang\", role=\"ctb\", comment = c(ORCID = \"0000-0002-1576-2126\")), person(\"Dean\", \"Attali\", role=\"ctb\", comment = c(ORCID = \"0000-0002-5645-3493\")), person(\"Michael\", \"Chirico\", role=\"ctb\", comment = c(ORCID = \"0000-0003-0787-087X\")), person(\"Kevin\", \"Ushey\", role=\"ctb\"))", "Date": "2024-08-19", "Title": "Create Compact Hash Digests of R Objects", "Description": "Implementation of a function 'digest()' for the creation of hash digests of arbitrary R objects (using the 'md5', 'sha-1', 'sha-256', 'crc32', 'xxhash', 'murmurhash', 'spookyhash', 'blake3', 'crc32c', 'xxh3_64', and 'xxh3_128' algorithms) permitting easy comparison of R language objects, as well as functions such as'hmac()' to create hash-based message authentication code. Please note that this package is not meant to be deployed for cryptographic purposes for which more comprehensive (and widely tested) libraries such as 'OpenSSL' should be used.", "URL": "https://github.com/eddelbuettel/digest, https://dirk.eddelbuettel.com/code/digest.html", "BugReports": "https://github.com/eddelbuettel/digest/issues", "Depends": ["R (>= 3.3.0)"], "Imports": ["utils"], "License": "GPL (>= 2)", "Suggests": ["tinytest", "simplermarkdown"], "VignetteBuilder": "simplermarkdown", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0001-6419-907X>), <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0002-7579-5165>), <PERSON> [ctb] (<https://orcid.org/0000-0003-2297-1732>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0001-5180-0567>), <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0001-6786-5453>), <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0001-8804-4216>), <PERSON> [ctb] (<https://orcid.org/0000-0001-9754-0393>), <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0003-4198-9911>), <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-2473-3407>), <PERSON>don <PERSON> [ctb] (<https://or<PERSON>.org/0000-0002-9093-8312>), <PERSON> de <PERSON>l<PERSON>e [ctb], <PERSON> <PERSON><PERSON><PERSON> [ctb], <PERSON> <PERSON><PERSON><PERSON> [ctb], <PERSON> <PERSON><PERSON> [ctb], <PERSON> <PERSON> [ctb], Andr<PERSON> <PERSON>v<PERSON><PERSON> [ctb], <PERSON> <PERSON><PERSON>v [ctb], Will Landau [ctb] (<https://orcid.org/0000-0003-1878-3253>), Floris Vanderhaeghe [ctb] (<https://orcid.org/0000-0002-6378-6229>), Kevin Tappe [ctb], Harris McGehee [ctb], Tim Mastny [ctb], Aaron Peikert [ctb] (<https://orcid.org/0000-0001-7813-818X>), Mark van der Loo [ctb] (<https://orcid.org/0000-0002-9807-4686>), Chris Muir [ctb] (<https://orcid.org/0000-0003-2555-3878>), Moritz Beller [ctb] (<https://orcid.org/0000-0003-4852-0526>), Sebastian Campbell [ctb], Winston Chang [ctb] (<https://orcid.org/0000-0002-1576-2126>), Dean Attali [ctb] (<https://orcid.org/0000-0002-5645-3493>), Michael Chirico [ctb] (<https://orcid.org/0000-0003-0787-087X>), Kevin Ushey [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "dir.expiry": {"Package": "dir.expiry", "Version": "1.17.0", "Source": "Bioconductor", "Date": "2024-10-17", "Title": "Managing Expiration for Cache Directories", "Description": "Implements an expiration system for access to versioned directories. Directories that have not been accessed by a registered function within a certain time frame are deleted. This aims to reduce disk usage by eliminating obsolete caches generated by old versions of packages.", "Authors@R": "person(\"<PERSON>\", \"Lun\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\")", "License": "GPL-3", "Imports": ["utils", "filelock"], "Suggests": ["rmarkdown", "knitr", "testthat", "BiocStyle"], "biocViews": "Software, Infrastructure", "VignetteBuilder": "knitr", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "git_url": "https://git.bioconductor.org/packages/dir.expiry", "git_branch": "devel", "git_last_commit": "4a3cff0", "git_last_commit_date": "2025-04-15", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>"}, "dplyr": {"Package": "dplyr", "Version": "1.1.4", "Source": "Repository", "Type": "Package", "Title": "A Grammar of Data Manipulation", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2444-4226\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-4777-038X\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A fast, consistent tool for working with data frame like objects, both in memory and out of memory.", "License": "MIT + file LICENSE", "URL": "https://dplyr.tidyverse.org, https://github.com/tidyverse/dplyr", "BugReports": "https://github.com/tidyverse/dplyr/issues", "Depends": ["R (>= 3.5.0)"], "Imports": ["cli (>= 3.4.0)", "generics", "glue (>= 1.3.2)", "lifecycle (>= 1.0.3)", "magrittr (>= 1.5)", "methods", "pillar (>= 1.9.0)", "R6", "rlang (>= 1.1.0)", "tibble (>= 3.2.0)", "tidyselect (>= 1.2.0)", "utils", "vctrs (>= 0.6.4)"], "Suggests": ["bench", "broom", "callr", "covr", "DBI", "dbplyr (>= 2.2.1)", "ggplot2", "knitr", "<PERSON><PERSON>", "lobstr", "microbenchmark", "nycflights13", "purrr", "rmarkdown", "RMySQL", "RPostgreSQL", "RSQLite", "stringi (>= 1.7.6)", "testthat (>= 3.1.5)", "tidyr (>= 1.3.0)", "withr"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse, shiny, pkgdown, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.2.3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-4757-117X>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-2444-4226>), <PERSON> [aut], <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-1416-3412>), <PERSON> [aut] (<https://orcid.org/0000-0003-4777-038X>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "evaluate": {"Package": "evaluate", "Version": "1.0.4", "Source": "Repository", "Type": "Package", "Title": "Parsing and Evaluation Tools that Provide More Details than the Default", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0003-0645-5666\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Parsing and evaluation tools that make it easy to recreate the command line behaviour of R.", "License": "MIT + file LICENSE", "URL": "https://evaluate.r-lib.org/, https://github.com/r-lib/evaluate", "BugReports": "https://github.com/r-lib/evaluate/issues", "Depends": ["R (>= 3.6.0)"], "Suggests": ["callr", "covr", "ggplot2 (>= 3.3.6)", "lattice", "methods", "pkgload", "ragg (>= 1.4.0)", "rlang (>= 1.1.5)", "knitr", "testthat (>= 3.0.0)", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-0645-5666>), <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "fastmap": {"Package": "fastmap", "Version": "1.2.0", "Source": "Repository", "Title": "Fast Data Structures", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(given = \"Tessil\", role = \"cph\", comment = \"hopscotch_map library\") )", "Description": "Fast implementation of data structures, including a key-value store, stack, and queue. Environments are commonly used as key-value stores in R, but every time a new key is used, it is added to R's global symbol table, causing a small amount of memory leakage. This can be problematic in cases where many different keys are used. Fastmap avoids this memory leak issue by implementing the map using data structures in C++.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "Suggests": ["testthat (>= 2.1.1)"], "URL": "https://r-lib.github.io/fastmap/, https://github.com/r-lib/fastmap", "BugReports": "https://github.com/r-lib/fastmap/issues", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd], Tessil [cph] (hopscotch_map library)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "filelock": {"Package": "filelock", "Version": "1.0.3", "Source": "Repository", "Title": "Portable File Locking", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Place an exclusive or shared lock on a file. It uses 'LockFile' on Windows and 'fcntl' locks on Unix-like systems.", "License": "MIT + file LICENSE", "URL": "https://r-lib.github.io/filelock/, https://github.com/r-lib/filelock", "BugReports": "https://github.com/r-lib/filelock/issues", "Depends": ["R (>= 3.4)"], "Suggests": ["callr (>= 2.0.0)", "covr", "testthat (>= 3.0.0)"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "formatR": {"Package": "formatR", "Version": "1.14", "Source": "Repository", "Type": "Package", "Title": "Format R Code Automatically", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person() )", "Description": "Provides a function tidy_source() to format R source code. Spaces and indent will be added to the code automatically, and comments will be preserved under certain conditions, so that R code will be more human-readable and tidy. There is also a Shiny app as a user interface in this package (see tidy_app()).", "Depends": ["R (>= 3.2.3)"], "Suggests": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "shiny", "testit", "rmarkdown", "knitr"], "License": "GPL", "URL": "https://github.com/yihui/formatR", "BugReports": "https://github.com/yihui/formatR/issues", "VignetteBuilder": "knitr", "RoxygenNote": "7.2.3", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>), <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "fs": {"Package": "fs", "Version": "1.6.6", "Source": "Repository", "Title": "Cross-Platform File System Operations Based on 'libuv'", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"libuv project contributors\", role = \"cph\", comment = \"libuv library\"), person(\"Joyent, Inc. and other Node contributors\", role = \"cph\", comment = \"libuv library\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A cross-platform interface to file system operations, built on top of the 'libuv' C library.", "License": "MIT + file LICENSE", "URL": "https://fs.r-lib.org, https://github.com/r-lib/fs", "BugReports": "https://github.com/r-lib/fs/issues", "Depends": ["R (>= 3.6)"], "Imports": ["methods"], "Suggests": ["covr", "crayon", "knitr", "pillar (>= 1.0.0)", "rmarkdown", "spelling", "testthat (>= 3.0.0)", "tibble (>= 1.1.0)", "vctrs (>= 0.3.0)", "withr"], "VignetteBuilder": "knitr", "ByteCompile": "true", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Copyright": "file COPYRIGHTS", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.2.3", "SystemRequirements": "GNU make", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre], libuv project contributors [cph] (libuv library), Joyent, Inc. and other Node contributors [cph] (libuv library), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "futile.logger": {"Package": "futile.logger", "Version": "1.4.3", "Source": "Repository", "Type": "Package", "Title": "A Logging Utility for R", "Date": "2016-07-10", "Author": "<PERSON>", "Maintainer": "<PERSON> <<EMAIL>>", "Depends": ["R (>= 3.0.0)"], "Imports": ["utils", "lambda.r (>= 1.1.0)", "futile.options"], "Suggests": ["testthat", "jsonlite"], "Description": "Provides a simple yet powerful logging utility. Based loosely on log4j, futile.logger takes advantage of R idioms to make logging a convenient and easy to use replacement for cat and print statements.", "License": "LGPL-3", "LazyLoad": "yes", "NeedsCompilation": "no", "ByteCompile": "yes", "Collate": "'options.R' 'appender.R' 'constants.R' 'layout.R' 'logger.R' 'scat.R' 'futile.logger-package.R'", "RoxygenNote": "5.0.1", "Repository": "CRAN"}, "futile.options": {"Package": "futile.options", "Version": "1.0.1", "Source": "Repository", "Type": "Package", "Title": "Futile Options Management", "Date": "2018-04-20", "Author": "<PERSON>", "Maintainer": "<PERSON> <<EMAIL>>", "Depends": ["R (>= 2.8.0)"], "Description": "A scoped options management framework. Used in other packages.", "License": "LGPL-3", "LazyLoad": "yes", "NeedsCompilation": "no", "Repository": "CRAN"}, "generics": {"Package": "generics", "Version": "0.1.4", "Source": "Repository", "Title": "Common S3 Generics not Provided by Base R Methods Related to Model Fitting", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"https://ror.org/03wc8by49\")) )", "Description": "In order to reduce potential package dependencies and conflicts, generics provides a number of commonly used S3 generics.", "License": "MIT + file LICENSE", "URL": "https://generics.r-lib.org, https://github.com/r-lib/generics", "BugReports": "https://github.com/r-lib/generics/issues", "Depends": ["R (>= 3.6)"], "Imports": ["methods"], "Suggests": ["covr", "pkgload", "testthat (>= 3.0.0)", "tibble", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut], <PERSON> [aut], Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "glue": {"Package": "glue", "Version": "1.8.0", "Source": "Repository", "Title": "Interpreted String Literals", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2739-7082\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-6983-2759\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "An implementation of interpreted string literals, inspired by Python's Literal String Interpolation <https://www.python.org/dev/peps/pep-0498/> and Docstrings <https://www.python.org/dev/peps/pep-0257/> and <PERSON>'s Triple-Quoted String Literals <https://docs.julialang.org/en/v1.3/manual/strings/#Triple-Quoted-String-Literals-1>.", "License": "MIT + file LICENSE", "URL": "https://glue.tidyverse.org/, https://github.com/tidyverse/glue", "BugReports": "https://github.com/tidyverse/glue/issues", "Depends": ["R (>= 3.6)"], "Imports": ["methods"], "Suggests": ["crayon", "DBI (>= 1.2.0)", "dplyr", "knitr", "magrit<PERSON>", "rlang", "rmarkdown", "RSQLite", "testthat (>= 3.2.0)", "vctrs (>= 0.3.0)", "waldo (>= 0.5.3)", "withr"], "VignetteBuilder": "knitr", "ByteCompile": "true", "Config/Needs/website": "bench, forcats, ggbeeswarm, ggplot2, <PERSON><PERSON>utils, rprintf, tidyr, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0002-2739-7082>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-6983-2759>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "here": {"Package": "here", "Version": "1.0.1", "Source": "Repository", "Title": "A Simpler Way to Find Your Files", "Date": "2020-12-13", "Authors@R": "c(person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-6983-2759\")))", "Description": "Constructs paths to your project's files. Declare the relative path of a file within your project with 'i_am()'. Use the 'here()' function as a drop-in replacement for 'file.path()', it will always locate the files relative to your project root.", "License": "MIT + file LICENSE", "URL": "https://here.r-lib.org/, https://github.com/r-lib/here", "BugReports": "https://github.com/r-lib/here/issues", "Imports": ["rprojroot (>= 2.0.2)"], "Suggests": ["conflicted", "covr", "fs", "knitr", "palmerpenguins", "plyr", "readr", "rlang", "rmarkdown", "testthat", "uuid", "withr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.1.1.9000", "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-1416-3412>), <PERSON> [ctb] (<https://orcid.org/0000-0002-6983-2759>)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "highr": {"Package": "highr", "Version": "0.11", "Source": "Repository", "Type": "Package", "Title": "Syntax Highlighting for R Source Code", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\") )", "Description": "Provides syntax highlighting for R source code. Currently it supports LaTeX and HTML output. Source code of other languages is supported via <PERSON>'s highlight package (<https://gitlab.com/saalen/highlight>).", "Depends": ["R (>= 3.3.0)"], "Imports": ["xfun (>= 0.18)"], "Suggests": ["knitr", "markdown", "testit"], "License": "GPL", "URL": "https://github.com/yihui/highr", "BugReports": "https://github.com/yihui/highr/issues", "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>), <PERSON><PERSON><PERSON> [aut], <PERSON> [ctb], <PERSON><PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "hms": {"Package": "hms", "Version": "1.1.3", "Source": "Repository", "Title": "Pretty Time of Day", "Date": "2023-03-21", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(\"R Consortium\", role = \"fnd\"), person(\"RStudio\", role = \"fnd\") )", "Description": "Implements an S3 class for storing and formatting time-of-day values, based on the 'difftime' class.", "Imports": ["lifecycle", "methods", "pkgconfig", "rlang (>= 1.0.2)", "vctrs (>= 0.3.8)"], "Suggests": ["crayon", "lubridate", "pillar (>= 1.1.0)", "testthat (>= 3.0.0)"], "License": "MIT + file LICENSE", "Encoding": "UTF-8", "URL": "https://hms.tidyverse.org/, https://github.com/tidyverse/hms", "BugReports": "https://github.com/tidyverse/hms/issues", "RoxygenNote": "7.2.3", "Config/testthat/edition": "3", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "false", "Config/Needs/website": "tidyverse/tidytemplate", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-1416-3412>), R Consortium [fnd], RStudio [fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "htmltools": {"Package": "htmltools", "Version": "*******", "Source": "Repository", "Type": "Package", "Title": "Tools for HTML", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0001-9986-114X\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-1576-2126\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Tools for HTML generation and output.", "License": "GPL (>= 2)", "URL": "https://github.com/rstudio/htmltools, https://rstudio.github.io/htmltools/", "BugReports": "https://github.com/rstudio/htmltools/issues", "Depends": ["R (>= 2.14.1)"], "Imports": ["base64enc", "digest", "fastmap (>= 1.1.0)", "grDevices", "rlang (>= 1.0.0)", "utils"], "Suggests": ["Cairo", "markdown", "ragg", "shiny", "testthat", "withr"], "Enhances": ["knitr"], "Config/Needs/check": "knitr", "Config/Needs/website": "rstudio/quillt, bench", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "Collate": "'colors.R' 'fill.R' 'html_dependency.R' 'html_escape.R' 'html_print.R' 'htmltools-package.R' 'images.R' 'known_tags.R' 'selector.R' 'staticimports.R' 'tag_query.R' 'utils.R' 'tags.R' 'template.R'", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-9986-114X>), <PERSON> [aut] (<https://orcid.org/0000-0002-1576-2126>), <PERSON><PERSON> [aut], <PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "httr": {"Package": "httr", "Version": "1.4.7", "Source": "Repository", "Title": "Tools for Working with URLs and HTTP", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Useful tools for working with HTTP organised by HTTP verbs (GET(), POST(), etc). Configuration functions make it easy to control additional request components (authenticate(), add_headers() and so on).", "License": "MIT + file LICENSE", "URL": "https://httr.r-lib.org/, https://github.com/r-lib/httr", "BugReports": "https://github.com/r-lib/httr/issues", "Depends": ["R (>= 3.5)"], "Imports": ["curl (>= 5.0.2)", "jsonlite", "mime", "openssl (>= 0.8)", "R6"], "Suggests": ["covr", "httpuv", "jpeg", "knitr", "png", "readr", "rmarkdown", "testthat (>= 0.8.0)", "xml2"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON>, <PERSON><PERSON> [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "httr2": {"Package": "httr2", "Version": "1.2.1", "Source": "Repository", "Title": "Perform HTTP Requests and Process the Responses", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\") )", "Description": "Tools for creating and modifying HTTP requests, then performing them and processing the results. 'httr2' is a modern re-imagining of 'httr' that uses a pipe-based interface and solves more of the problems that API wrapping packages face.", "License": "MIT + file LICENSE", "URL": "https://httr2.r-lib.org, https://github.com/r-lib/httr2", "BugReports": "https://github.com/r-lib/httr2/issues", "Depends": ["R (>= 4.1)"], "Imports": ["cli (>= 3.0.0)", "curl (>= 6.4.0)", "glue", "lifecycle", "magrit<PERSON>", "openssl", "R6", "rapp<PERSON>s", "rlang (>= 1.1.0)", "vctrs (>= 0.6.3)", "withr"], "Suggests": ["askpass", "bench", "clipr", "covr", "docopt", "httpuv", "jose", "jsonlite", "knitr", "later (>= 1.4.0)", "nanonext", "paws.common", "promises", "rmarkdown", "testthat (>= 3.1.8)", "tibble", "webfakes (>= 1.4.0)", "xml2"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "resp-stream, req-perform", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "jsonlite": {"Package": "jsonlite", "Version": "2.0.0", "Source": "Repository", "Title": "A Simple and Robust JSON Parser and Generator for R", "License": "MIT + file LICENSE", "Depends": ["methods"], "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"cph\", comment=\"author of bundled libyajl\"))", "URL": "https://jeroen.r-universe.dev/jsonlite https://arxiv.org/abs/1403.2805", "BugReports": "https://github.com/jeroen/jsonlite/issues", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "VignetteBuilder": "knitr, R.rsp", "Description": "A reasonably fast JSON parser and generator, optimized for statistical  data and the web. Offers simple, flexible tools for working with JSON in R, and is particularly powerful for building pipelines and interacting with a web API.  The implementation is based on the mapping described in the vignette (Ooms, 2014). In addition to converting JSON data from/to R objects, 'jsonlite' contains  functions to stream, validate, and prettify JSON data. The unit tests included  with the package verify that all edge cases are encoded and decoded consistently  for use with dynamic data in systems and applications.", "Suggests": ["httr", "vctrs", "testthat", "knitr", "rmarkdown", "R.rsp", "sf"], "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>), <PERSON> [ctb], <PERSON> [cph] (author of bundled libyajl)", "Repository": "CRAN"}, "knitr": {"Package": "knitr", "Version": "1.50", "Source": "Repository", "Type": "Package", "Title": "A General-Purpose Package for Dynamic Report Generation in R", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\", URL = \"https://yihui.org\")), person(\"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"the CSS files under inst/themes/ were derived from the Highlight package http://www.andre-simon.de\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-8335-495X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>t\", \"<PERSON><PERSON>rov\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>vie<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>-<PERSON>\", role = \"ctb\"), person(\"David\", \"Robinson\", role = \"ctb\"), person(\"Doug\", \"Hemken\", role = \"ctb\"), person(\"Duncan\", \"Murdoch\", role = \"ctb\"), person(\"Elio\", \"Campitelli\", role = \"ctb\"), person(\"Ellis\", \"Hughes\", role = \"ctb\"), person(\"Emily\", \"Riederer\", role = \"ctb\"), person(\"Fabian\", \"Hirschmann\", role = \"ctb\"), person(\"Fitch\", \"Simeon\", role = \"ctb\"), person(\"Forest\", \"Fang\", role = \"ctb\"), person(c(\"Frank\", \"E\", \"Harrell\", \"Jr\"), role = \"ctb\", comment = \"the Sweavel package at inst/misc/Sweavel.sty\"), person(\"Garrick\", \"Aden-Buie\", role = \"ctb\"), person(\"Gregoire\", \"Detrez\", role = \"ctb\"), person(\"Hadley\", \"Wickham\", role = \"ctb\"), person(\"Hao\", \"Zhu\", role = \"ctb\"), person(\"Heewon\", \"Jeon\", role = \"ctb\"), person(\"Henrik\", \"Bengtsson\", role = \"ctb\"), person(\"Hiroaki\", \"Yutani\", role = \"ctb\"), person(\"Ian\", \"Lyttle\", role = \"ctb\"), person(\"Hodges\", \"Daniel\", role = \"ctb\"), person(\"Jacob\", \"Bien\", role = \"ctb\"), person(\"Jake\", \"Burkhead\", role = \"ctb\"), person(\"James\", \"Manton\", role = \"ctb\"), person(\"Jared\", \"Lander\", role = \"ctb\"), person(\"Jason\", \"Punyon\", role = \"ctb\"), person(\"Javier\", \"Luraschi\", role = \"ctb\"), person(\"Jeff\", \"Arnold\", role = \"ctb\"), person(\"Jenny\", \"Bryan\", role = \"ctb\"), person(\"Jeremy\", \"Ashkenas\", role = c(\"ctb\", \"cph\"), comment = \"the CSS file at inst/misc/docco-classic.css\"), person(\"Jeremy\", \"Stephens\", role = \"ctb\"), person(\"Jim\", \"Hester\", role = \"ctb\"), person(\"Joe\", \"Cheng\", role = \"ctb\"), person(\"Johannes\", \"Ranke\", role = \"ctb\"), person(\"John\", \"Honaker\", role = \"ctb\"), person(\"John\", \"Muschelli\", role = \"ctb\"), person(\"Jonathan\", \"Keane\", role = \"ctb\"), person(\"JJ\", \"Allaire\", role = \"ctb\"), person(\"Johan\", \"Toloe\", role = \"ctb\"), person(\"Jonathan\", \"Sidi\", role = \"ctb\"), person(\"Joseph\", \"Larmarange\", role = \"ctb\"), person(\"Julien\", \"Barnier\", role = \"ctb\"), person(\"Kaiyin\", \"Zhong\", role = \"ctb\"), person(\"Kamil\", \"Slowikowski\", role = \"ctb\"), person(\"Karl\", \"Forner\", role = \"ctb\"), person(c(\"Kevin\", \"K.\"), \"Smith\", role = \"ctb\"), person(\"Kirill\", \"Mueller\", role = \"ctb\"), person(\"Kohske\", \"Takahashi\", role = \"ctb\"), person(\"Lorenz\", \"Walthert\", role = \"ctb\"), person(\"Lucas\", \"Gallindo\", role = \"ctb\"), person(\"Marius\", \"Hofert\", role = \"ctb\"), person(\"Martin\", \"Modrák\", role = \"ctb\"), person(\"Michael\", \"Chirico\", role = \"ctb\"), person(\"Michael\", \"Friendly\", role = \"ctb\"), person(\"Michal\", \"Bojanowski\", role = \"ctb\"), person(\"Michel\", \"Kuhlmann\", role = \"ctb\"), person(\"Miller\", \"Patrick\", role = \"ctb\"), person(\"Nacho\", \"Caballero\", role = \"ctb\"), person(\"Nick\", \"Salkowski\", role = \"ctb\"), person(\"Niels Richard\", \"Hansen\", role = \"ctb\"), person(\"Noam\", \"Ross\", role = \"ctb\"), person(\"Obada\", \"Mahdi\", role = \"ctb\"), person(\"Pavel N.\", \"Krivitsky\", role = \"ctb\", comment=c(ORCID = \"0000-0002-9101-3362\")), person(\"Pedro\", \"Faria\", role = \"ctb\"), person(\"Qiang\", \"Li\", role = \"ctb\"), person(\"Ramnath\", \"Vaidyanathan\", role = \"ctb\"), person(\"Richard\", \"Cotton\", role = \"ctb\"), person(\"Robert\", \"Krzyzanowski\", role = \"ctb\"), person(\"Rodrigo\", \"Copetti\", role = \"ctb\"), person(\"Romain\", \"Francois\", role = \"ctb\"), person(\"Ruaridh\", \"Williamson\", role = \"ctb\"), person(\"Sagiru\", \"Mati\", role = \"ctb\", comment = c(ORCID = \"0000-0003-1413-3974\")), person(\"Scott\", \"Kostyshak\", role = \"ctb\"), person(\"Sebastian\", \"Meyer\", role = \"ctb\"), person(\"Sietse\", \"Brouwer\", role = \"ctb\"), person(c(\"Simon\", \"de\"), \"Bernard\", role = \"ctb\"), person(\"Sylvain\", \"Rousseau\", role = \"ctb\"), person(\"Taiyun\", \"Wei\", role = \"ctb\"), person(\"Thibaut\", \"Assus\", role = \"ctb\"), person(\"Thibaut\", \"Lamadon\", role = \"ctb\"), person(\"Thomas\", \"Leeper\", role = \"ctb\"), person(\"Tim\", \"Mastny\", role = \"ctb\"), person(\"Tom\", \"Torsney-Weir\", role = \"ctb\"), person(\"Trevor\", \"Davis\", role = \"ctb\"), person(\"Viktoras\", \"Veitas\", role = \"ctb\"), person(\"Weicheng\", \"Zhu\", role = \"ctb\"), person(\"Wush\", \"Wu\", role = \"ctb\"), person(\"Zachary\", \"Foster\", role = \"ctb\"), person(\"Zhian N.\", \"Kamvar\", role = \"ctb\", comment = c(ORCID = \"0000-0003-1458-7108\")), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Provides a general-purpose tool for dynamic report generation in R using Literate Programming techniques.", "Depends": ["R (>= 3.6.0)"], "Imports": ["evaluate (>= 0.15)", "highr (>= 0.11)", "methods", "tools", "xfun (>= 0.51)", "yaml (>= 2.1.19)"], "Suggests": ["bslib", "codetools", "DBI (>= 0.4-1)", "digest", "formatR", "gifski", "gridSVG", "htmlwidgets (>= 0.7)", "jpeg", "JuliaCall (>= 0.11.1)", "magick", "litedown", "markdown (>= 1.3)", "png", "ragg", "reticulate (>= 1.4)", "rgl (>= 0.95.1201)", "rlang", "rmarkdown", "sass", "showtext", "styler (>= 1.2.0)", "targets (>= 0.6.0)", "testit", "tibble", "tikzDevice (>= 0.10)", "tinytex (>= 0.56)", "webshot", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "svglite"], "License": "GPL", "URL": "https://yihui.org/knitr/", "BugReports": "https://github.com/yihui/knitr/issues", "Encoding": "UTF-8", "VignetteBuilder": "litedown, knitr", "SystemRequirements": "Package vignettes based on R Markdown v2 or reStructuredText require Pandoc (http://pandoc.org). The function rst2pdf() requires rst2pdf (https://github.com/rst2pdf/rst2pdf).", "Collate": "'block.R' 'cache.R' 'citation.R' 'hooks-html.R' 'plot.R' 'utils.R' 'defaults.R' 'concordance.R' 'engine.R' 'highlight.R' 'themes.R' 'header.R' 'hooks-asciidoc.R' 'hooks-chunk.R' 'hooks-extra.R' 'hooks-latex.R' 'hooks-md.R' 'hooks-rst.R' 'hooks-textile.R' 'hooks.R' 'output.R' 'package.R' 'pandoc.R' 'params.R' 'parser.R' 'pattern.R' 'rocco.R' 'spin.R' 'table.R' 'template.R' 'utils-conversion.R' 'utils-rd2html.R' 'utils-string.R' 'utils-sweave.R' 'utils-upload.R' 'utils-vignettes.R' 'zzz.R'", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>, https://yihui.org), <PERSON><PERSON><PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] (the CSS files under inst/themes/ were derived from the Highlight package http://www.andre-simon.de), <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-8335-495X>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] (the Sweavel package at inst/mi<PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>.s<PERSON>), <PERSON><PERSON><PERSON> <PERSON>-<PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON><PERSON>], <PERSON> <PERSON> [c<PERSON>b], <PERSON><PERSON> <PERSON> [c<PERSON>b], <PERSON><PERSON><PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON><PERSON><PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON><PERSON><PERSON> [ctb], James Manton [ctb], Jared Lander [ctb], Jason Punyon [ctb], Javier Luraschi [ctb], Jeff Arnold [ctb], Jenny Bryan [ctb], Jeremy Ashkenas [ctb, cph] (the CSS file at inst/misc/docco-classic.css), Jeremy Stephens [ctb], Jim Hester [ctb], Joe Cheng [ctb], Johannes Ranke [ctb], John Honaker [ctb], John Muschelli [ctb], Jonathan Keane [ctb], JJ Allaire [ctb], Johan Toloe [ctb], Jonathan Sidi [ctb], Joseph Larmarange [ctb], Julien Barnier [ctb], Kaiyin Zhong [ctb], Kamil Slowikowski [ctb], Karl Forner [ctb], Kevin K. Smith [ctb], Kirill Mueller [ctb], Kohske Takahashi [ctb], Lorenz Walthert [ctb], Lucas Gallindo [ctb], Marius Hofert [ctb], Martin Modrák [ctb], Michael Chirico [ctb], Michael Friendly [ctb], Michal Bojanowski [ctb], Michel Kuhlmann [ctb], Miller Patrick [ctb], Nacho Caballero [ctb], Nick Salkowski [ctb], Niels Richard Hansen [ctb], Noam Ross [ctb], Obada Mahdi [ctb], Pavel N. Krivitsky [ctb] (<https://orcid.org/0000-0002-9101-3362>), Pedro Faria [ctb], Qiang Li [ctb], Ramnath Vaidyanathan [ctb], Richard Cotton [ctb], Robert Krzyzanowski [ctb], Rodrigo Copetti [ctb], Romain Francois [ctb], Ruaridh Williamson [ctb], Sagiru Mati [ctb] (<https://orcid.org/0000-0003-1413-3974>), Scott Kostyshak [ctb], Sebastian Meyer [ctb], Sietse Brouwer [ctb], Simon de Bernard [ctb], Sylvain Rousseau [ctb], Taiyun Wei [ctb], Thibaut Assus [ctb], Thibaut Lamadon [ctb], Thomas Leeper [ctb], Tim Mastny [ctb], Tom Torsney-Weir [ctb], Trevor Davis [ctb], Viktoras Veitas [ctb], Weicheng Zhu [ctb], Wush Wu [ctb], Zachary Foster [ctb], Zhian N. Kamvar [ctb] (<https://orcid.org/0000-0003-1458-7108>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "lambda.r": {"Package": "lambda.r", "Version": "1.2.4", "Source": "Repository", "Type": "Package", "Title": "Modeling Data with Functional Programming", "Date": "2019-09-15", "Depends": ["R (>= 3.0.0)"], "Imports": ["formatR"], "Suggests": ["testit"], "Author": "<PERSON>", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "A language extension to efficiently write functional programs in R. Syntax extensions include multi-part function definitions, pattern matching, guard statements, built-in (optional) type safety.", "License": "LGPL-3", "LazyLoad": "yes", "NeedsCompilation": "no", "Repository": "CRAN"}, "languageserver": {"Package": "languageserver", "Version": "0.3.16", "Source": "Repository", "Type": "Package", "Title": "Language Server Protocol", "Date": "2023-08-17", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(given = \"Kun\", family = \"Ren\", role = \"ctb\", email = \"<EMAIL>\"))", "Description": "An implementation of the Language Server Protocol for R. The Language Server protocol is used by an editor client to integrate features like auto completion. See <https://microsoft.github.io/language-server-protocol/> for details.", "License": "MIT + file LICENSE", "URL": "https://github.com/REditorSupport/languageserver/", "Depends": ["R (>= 3.4.0)"], "Imports": ["callr (>= 3.0.0)", "collections (>= 0.3.0)", "fs (>= 1.3.1)", "jsonlite (>= 1.6)", "lintr (>= 3.0.0)", "parallel", "R6 (>= 2.4.1)", "roxygen2 (>= 7.0.0)", "stringi (>= 1.1.7)", "styler (>= 1.5.1)", "tools", "utils", "xml2 (>= 1.2.2)", "xmlparsedata (>= 1.0.3)"], "Suggests": ["covr (>= 3.4.0)", "magrittr (>= 1.5)", "mockery (>= 0.4.2)", "pacman", "processx (>= 3.4.1)", "purrr (>= 0.3.3)", "testthat (>= 2.1.0)", "withr (>= 2.3.0)", "rmarkdown (>= 2.0)"], "ByteCompile": "yes", "Encoding": "UTF-8", "NeedsCompilation": "yes", "RoxygenNote": "7.2.1", "Config/testthat/edition": "3", "Author": "<PERSON> [aut, cre], <PERSON><PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "lazyeval": {"Package": "lazyeval", "Version": "0.2.2", "Source": "Repository", "Title": "Lazy (Non-Standard) Evaluation", "Description": "An alternative approach to non-standard evaluation using formulas. Provides a full implementation of LISP style 'quasiquotation', making it easier to generate code with other code.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", ,\"<EMAIL>\", c(\"aut\", \"cre\")), person(\"<PERSON>tu<PERSON>\", role = \"cph\") )", "License": "GPL-3", "LazyData": "true", "Depends": ["R (>= 3.1.0)"], "Suggests": ["knitr", "rmarkdown (>= 0.2.65)", "testthat", "covr"], "VignetteBuilder": "knitr", "RoxygenNote": "6.1.1", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON> [cph]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "lifecycle": {"Package": "lifecycle", "Version": "1.0.4", "Source": "Repository", "Title": "Manage the Life Cycle of your Package Functions", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Manage the life cycle of your exported functions with shared conventions, documentation badges, and user-friendly deprecation warnings.", "License": "MIT + file LICENSE", "URL": "https://lifecycle.r-lib.org/, https://github.com/r-lib/lifecycle", "BugReports": "https://github.com/r-lib/lifecycle/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cli (>= 3.4.0)", "glue", "rlang (>= 1.1.0)"], "Suggests": ["covr", "crayon", "knitr", "lintr", "rmarkdown", "testthat (>= 3.0.1)", "tibble", "tidyverse", "tools", "vctrs", "withr"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate, usethis", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.1", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON> [aut] (<https://orcid.org/0000-0003-4757-117X>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "lintr": {"Package": "lintr", "Version": "3.2.0", "Source": "Repository", "Title": "A 'Linter' for R Code", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", , role = \"aut\"), person(\"<PERSON>lorent\", \"<PERSON><PERSON>\", role = \"aut\", comment = \"fangly\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"michael<PERSON><PERSON><EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Ku<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"Rosen<PERSON>\", role = \"aut\", comment = \"AshesITR\"), person(\"<PERSON><PERSON>jeet\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-1995-6531\", Twitter = \"@patilindrajeets\")) )", "Description": "Checks adherence to a given style, syntax errors and possible semantic issues.  Supports on the fly checking of R code edited with 'RStudio IDE', 'Emacs', 'Vim', 'Sublime Text', 'Atom' and 'Visual Studio Code'.", "License": "MIT + file LICENSE", "URL": "https://lintr.r-lib.org, https://github.com/r-lib/lintr", "BugReports": "https://github.com/r-lib/lintr/issues", "Depends": ["R (>= 4.0)"], "Imports": ["backports (>= 1.4.0)", "cli (>= 3.4.0)", "codetools", "digest", "glue", "knitr", "rex", "stats", "utils", "xml2 (>= 1.0.0)", "xmlparsedata (>= 1.0.5)"], "Suggests": ["bookdown", "cyclocomp", "jsonlite", "patrick (>= 0.2.0)", "rlang", "rmarkdown", "rstudioa<PERSON> (>= 0.2)", "testthat (>= 3.2.1)", "tibble", "tufte", "withr (>= 2.5.0)"], "Enhances": ["data.table"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/Needs/development": "pkgload, cli, testthat, patrick", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "Collate": "'make_linter_from_xpath.R' 'xp_utils.R' 'utils.R' 'AAA.R' 'T_and_F_symbol_linter.R' 'absolute_path_linter.R' 'actions.R' 'addins.R' 'any_duplicated_linter.R' 'any_is_na_linter.R' 'assignment_linter.R' 'backport_linter.R' 'boolean_arithmetic_linter.R' 'brace_linter.R' 'cache.R' 'class_equals_linter.R' 'commas_linter.R' 'commented_code_linter.R' 'comparison_negation_linter.R' 'condition_call_linter.R' 'condition_message_linter.R' 'conjunct_test_linter.R' 'consecutive_assertion_linter.R' 'consecutive_mutate_linter.R' 'cyclocomp_linter.R' 'declared_functions.R' 'deprecated.R' 'duplicate_argument_linter.R' 'empty_assignment_linter.R' 'equals_na_linter.R' 'exclude.R' 'expect_comparison_linter.R' 'expect_identical_linter.R' 'expect_length_linter.R' 'expect_lint.R' 'expect_named_linter.R' 'expect_not_linter.R' 'expect_null_linter.R' 'expect_s3_class_linter.R' 'expect_s4_class_linter.R' 'expect_true_false_linter.R' 'expect_type_linter.R' 'extract.R' 'fixed_regex_linter.R' 'for_loop_index_linter.R' 'function_argument_linter.R' 'function_left_parentheses_linter.R' 'function_return_linter.R' 'get_source_expressions.R' 'ids_with_token.R' 'if_not_else_linter.R' 'if_switch_linter.R' 'ifelse_censor_linter.R' 'implicit_assignment_linter.R' 'implicit_integer_linter.R' 'indentation_linter.R' 'infix_spaces_linter.R' 'inner_combine_linter.R' 'is_lint_level.R' 'is_numeric_linter.R' 'keyword_quote_linter.R' 'length_levels_linter.R' 'length_test_linter.R' 'lengths_linter.R' 'library_call_linter.R' 'line_length_linter.R' 'lint.R' 'linter_tag_docs.R' 'linter_tags.R' 'lintr-deprecated.R' 'lintr-package.R' 'list_comparison_linter.R' 'literal_coercion_linter.R' 'make_linter_from_regex.R' 'matrix_apply_linter.R' 'methods.R' 'missing_argument_linter.R' 'missing_package_linter.R' 'namespace.R' 'namespace_linter.R' 'nested_ifelse_linter.R' 'nested_pipe_linter.R' 'nonportable_path_linter.R' 'shared_constants.R' 'nrow_subset_linter.R' 'numeric_leading_zero_linter.R' 'nzchar_linter.R' 'object_length_linter.R' 'object_name_linter.R' 'object_overwrite_linter.R' 'object_usage_linter.R' 'one_call_pipe_linter.R' 'outer_negation_linter.R' 'package_hooks_linter.R' 'paren_body_linter.R' 'paste_linter.R' 'path_utils.R' 'pipe_call_linter.R' 'pipe_consistency_linter.R' 'pipe_continuation_linter.R' 'pipe_return_linter.R' 'print_linter.R' 'quotes_linter.R' 'redundant_equals_linter.R' 'redundant_ifelse_linter.R' 'regex_subset_linter.R' 'rep_len_linter.R' 'repeat_linter.R' 'return_linter.R' 'routine_registration_linter.R' 'sample_int_linter.R' 'scalar_in_linter.R' 'semicolon_linter.R' 'seq_linter.R' 'settings.R' 'settings_utils.R' 'sort_linter.R' 'source_utils.R' 'spaces_inside_linter.R' 'spaces_left_parentheses_linter.R' 'sprintf_linter.R' 'stopifnot_all_linter.R' 'string_boundary_linter.R' 'strings_as_factors_linter.R' 'system_file_linter.R' 'terminal_close_linter.R' 'todo_comment_linter.R' 'trailing_blank_lines_linter.R' 'trailing_whitespace_linter.R' 'tree_utils.R' 'undesirable_function_linter.R' 'undesirable_operator_linter.R' 'unnecessary_concatenation_linter.R' 'unnecessary_lambda_linter.R' 'unnecessary_nesting_linter.R' 'unnecessary_placeholder_linter.R' 'unreachable_code_linter.R' 'unused_import_linter.R' 'use_lintr.R' 'vector_logic_linter.R' 'which_grepl_linter.R' 'whitespace_linter.R' 'with.R' 'with_id.R' 'xml_nodes_to_lints.R' 'xml_utils.R' 'yoda_test_linter.R' 'zzz.R'", "Language": "en-US", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON><PERSON><PERSON> [aut] (fangly), <PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut] (AshesITR), <PERSON><PERSON><PERSON><PERSON> [aut] (<https://orcid.org/0000-0003-1995-6531>, @patilindrajeets)", "Maintainer": "<PERSON> <michael<PERSON><EMAIL>>", "Repository": "CRAN"}, "magrittr": {"Package": "magrit<PERSON>", "Version": "2.0.3", "Source": "Repository", "Type": "Package", "Title": "A Forward-Pipe Operator for R", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cph\"), comment = \"Original author and creator of magrittr\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@rstudio.com\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"cre\"), person(\"RStudio\", role = c(\"cph\", \"fnd\")) )", "Description": "Provides a mechanism for chaining commands with a new forward-pipe operator, %>%. This operator will forward a value, or the result of an expression, into the next function call/expression. There is flexible support for the type of right-hand side expressions. For more information, see package vignette.  To quote <PERSON>, \"Ceci n'est pas un pipe.\"", "License": "MIT + file LICENSE", "URL": "https://magrittr.tidyverse.org, https://github.com/tidyverse/magrittr", "BugReports": "https://github.com/tidyverse/magrittr/issues", "Depends": ["R (>= 3.4.0)"], "Suggests": ["covr", "knitr", "rlang", "rmarkdown", "testthat"], "VignetteBuilder": "knitr", "ByteCompile": "Yes", "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.1.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cph] (Original author and creator of magrittr), <PERSON> [aut], <PERSON> [cre], <PERSON><PERSON><PERSON> [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "matrixStats": {"Package": "matrixStats", "Version": "1.5.0", "Source": "Repository", "Depends": ["R (>= 3.4.0)"], "Suggests": ["utils", "base64enc", "ggplot2", "knitr", "markdown", "microbenchmark", "R.devices", "R.rsp"], "VignetteBuilder": "R.rsp", "Title": "Functions that Apply to Rows and Columns of Matrices (and to Vectors)", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\", \"cph\"), email=\"<EMAIL>\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>da Bravo\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"))", "Author": "<PERSON> [aut, cre, cph], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "High-performing functions operating on rows and columns of matrices, e.g. col / rowMedians(), col / rowRanks(), and col / rowSds().  Functions optimized per data type and for subsetted calculations such that both memory usage and processing time is minimized.  There are also optimized vector-based methods, e.g. binMeans(), madDiff() and weightedMedian().", "License": "Artistic-2.0", "LazyLoad": "TRUE", "NeedsCompilation": "yes", "ByteCompile": "TRUE", "URL": "https://github.com/HenrikBengtsson/matrixStats", "BugReports": "https://github.com/HenrikBengtsson/matrixStats/issues", "RoxygenNote": "7.3.2", "Repository": "CRAN"}, "memoise": {"Package": "memoise", "Version": "2.0.1", "Source": "Repository", "Title": "'Memoisation' of Functions", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = \"aut\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(given = \"Kirill\", family = \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\"))", "Description": "Cache the results of a function so that when you call it again with the same arguments it returns the previously computed value.", "License": "MIT + file LICENSE", "URL": "https://memoise.r-lib.org, https://github.com/r-lib/memoise", "BugReports": "https://github.com/r-lib/memoise/issues", "Imports": ["rlang (>= 0.4.10)", "cachem"], "Suggests": ["digest", "aws.s3", "covr", "googleAuthR", "googleCloudStorageR", "httr", "testthat"], "Encoding": "UTF-8", "RoxygenNote": "7.1.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "mime": {"Package": "mime", "Version": "0.13", "Source": "Repository", "Type": "Package", "Title": "Map Filenames to MIME Types", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\", URL = \"https://yihui.org\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\") )", "Description": "Guesses the MIME type from a filename extension using the data derived from /etc/mime.types in UNIX-type systems.", "Imports": ["tools"], "License": "GPL", "URL": "https://github.com/yihui/mime", "BugReports": "https://github.com/yihui/mime/issues", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>, https://yihui.org), <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "openssl": {"Package": "openssl", "Version": "2.3.3", "Source": "Repository", "Type": "Package", "Title": "Toolkit for Encryption, Signatures and Certificates Based on OpenSSL", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"))", "Description": "Bindings to OpenSSL libssl and libcrypto, plus custom SSH key parsers. Supports RSA, DSA and EC curves P-256, P-384, P-521, and curve25519. Cryptographic signatures can either be created and verified manually or via x509 certificates.  AES can be used in cbc, ctr or gcm mode for symmetric encryption; RSA for asymmetric (public key) encryption or EC for <PERSON><PERSON><PERSON>. High-level envelope functions  combine RSA and AES for encrypting arbitrary sized data. Other utilities include key generators, hash functions (md5, sha1, sha256, etc), base64 encoder, a secure random number generator, and 'bignum' math methods for manually performing crypto  calculations on large multibyte integers.", "License": "MIT + file LICENSE", "URL": "https://jeroen.r-universe.dev/openssl", "BugReports": "https://github.com/jeroen/openssl/issues", "SystemRequirements": "OpenSSL >= 1.0.2", "VignetteBuilder": "knitr", "Imports": ["askpass"], "Suggests": ["curl", "testthat (>= 2.1.0)", "digest", "knitr", "rmarkdown", "jsonlite", "jose", "sodium"], "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-4035-0289>), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "pbdZMQ": {"Package": "pbdZMQ", "Version": "0.3-14", "Source": "Repository", "Date": "2025-04-12", "Title": "Programming with Big Data -- Interface to 'ZeroMQ'", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"aut\", comment = \"file transfer\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"aut\", comment = \"Mac OSX\"), person(\"Whit\", \"<PERSON>\", role = \"ctb\", comment = \"some functions are modified from the rzmq package for backwards compatibility\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = \"C code of shellexec, and Solaris\"), person(\"R Core team\", role = \"ctb\", comment = \"some functions and headers are copied or modified from the R source code\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"<PERSON>ora\"), person(\"Elliott Sales\", \"de Andrade\", role = \"ctb\", comment = \"sprintf\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"windows conf\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"Mac OSX conf\"), person(\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = \"add serialize version\"), person(\"<PERSON><PERSON>en\", \"Ooms\", role = \"ctb\", comment = \"clang++ on MacOS ARM64\"), person(\"ZeroMQ authors\", role = c(\"aut\", \"cph\"), comment = \"source files in 'src/zmq_src/'\") )", "Depends": ["R (>= 3.5.0)"], "LazyLoad": "yes", "Copyright": "See files AUTHORS, COPYING, and COPYING.LESSER in 'inst/zmq_copyright/' for the 'ZeroMQ' source files in 'src/zmq_src/' which are under GPL-3.", "Description": "'ZeroMQ' is a well-known library for high-performance asynchronous messaging in scalable, distributed applications.  This package provides high level R wrapper functions to easily utilize 'ZeroMQ'. We mainly focus on interactive client/server programming frameworks. For convenience, a minimal 'ZeroMQ' library (4.2.2) is shipped with 'pbdZMQ', which can be used if no system installation of 'ZeroMQ' is available.  A few wrapper functions compatible with 'rzmq' are also provided.", "SystemRequirements": "Linux, Mac OSX, and Windows, or 'ZeroMQ' library >= 4.0.4. Solaris 10 needs 'ZeroMQ' library 4.0.7 and 'OpenCSW'.", "StagedInstall": "TRUE", "License": "GPL-3", "URL": "https://pbdr.org/", "BugReports": "https://github.com/snoweye/pbdZMQ/issues", "NeedsCompilation": "yes", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "RoxygenNote": "7.2.3", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut] (file transfer), <PERSON> [aut] (Mac OSX), <PERSON><PERSON> [ctb] (some functions are modified from the rzmq package for backwards compatibility), <PERSON> [ctb] (C code of shellexec, and Solaris), R Core team [ctb] (some functions and headers are copied or modified from the R source code), <PERSON> [ctb] (Fedora), <PERSON> [ctb] (sprintf), <PERSON> [ctb] (windows conf), <PERSON> [ctb] (Mac OSX conf), <PERSON>agi<PERSON><PERSON> [ctb] (add serialize version), <PERSON><PERSON><PERSON> [ctb] (clang++ on MacOS ARM64), ZeroMQ authors [aut, cph] (source files in 'src/zmq_src/')", "Repository": "CRAN"}, "pillar": {"Package": "pillar", "Version": "1.11.0", "Source": "Repository", "Title": "Coloured Formatting for Columns", "Authors@R": "c(person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"<PERSON>\", family = \"Wickham\", role = \"aut\"), person(given = \"RStudio\", role = \"cph\"))", "Description": "Provides 'pillar' and 'colonnade' generics designed for formatting columns of data using the full range of colours provided by modern terminals.", "License": "MIT + file LICENSE", "URL": "https://pillar.r-lib.org/, https://github.com/r-lib/pillar", "BugReports": "https://github.com/r-lib/pillar/issues", "Imports": ["cli (>= 2.3.0)", "glue", "lifecycle", "rlang (>= 1.0.2)", "utf8 (>= 1.1.0)", "utils", "vctrs (>= 0.5.0)"], "Suggests": ["bit64", "DBI", "debugme", "DiagrammeR", "dplyr", "formattable", "ggplot2", "knitr", "lubridate", "nanotime", "nycflights13", "palmerpenguins", "rmarkdown", "scales", "stringi", "survival", "testthat (>= 3.1.1)", "tibble", "units (>= 0.7.2)", "vdiffr", "withr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "format_multi_fuzz, format_multi_fuzz_2, format_multi, ctl_colonnade, ctl_colonnade_1, ctl_colonnade_2", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "true", "Config/gha/extra-packages": "units=?ignore-before-r=4.3.0", "Config/Needs/website": "tidyverse/tidytemplate", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>), <PERSON> [aut], <PERSON>tudio [cph]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "pkgbuild": {"Package": "pkgbuild", "Version": "1.4.8", "Source": "Repository", "Title": "Find Tools Needed to Build R Packages", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "Provides functions used to build R packages. Locates compilers needed to build R packages on various platforms and ensures the PATH is configured appropriately so R can use them.", "License": "MIT + file LICENSE", "URL": "https://github.com/r-lib/pkgbuild, https://pkgbuild.r-lib.org", "BugReports": "https://github.com/r-lib/pkgbuild/issues", "Depends": ["R (>= 3.5)"], "Imports": ["callr (>= 3.2.0)", "cli (>= 3.4.0)", "desc", "processx", "R6"], "Suggests": ["covr", "cpp11", "knitr", "Rcpp", "rmarkdown", "testthat (>= 3.2.0)", "withr (>= 2.3.0)"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/usethis/last-upkeep": "2025-04-30", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre], Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "pkgconfig": {"Package": "pkgconfig", "Version": "2.0.3", "Source": "Repository", "Title": "Private Configuration for 'R' Packages", "Author": "<PERSON><PERSON><PERSON>", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Description": "Set configuration options on a per-package basis. Options set by a given package only apply to that package, other packages are unaffected.", "License": "MIT + file LICENSE", "LazyData": "true", "Imports": ["utils"], "Suggests": ["covr", "testthat", "disposables (>= 1.0.3)"], "URL": "https://github.com/r-lib/pkgconfig#readme", "BugReports": "https://github.com/r-lib/pkgconfig/issues", "Encoding": "UTF-8", "NeedsCompilation": "no", "Repository": "CRAN"}, "pkgload": {"Package": "pkgload", "Version": "1.4.0", "Source": "Repository", "Title": "Simulate Package Installation and Attach", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"R Core team\", role = \"ctb\", comment = \"Some namespace and vignette code extracted from base R\") )", "Description": "Simulates the process of installing a package and then attaching it. This is a key part of the 'devtools' package as it allows you to rapidly iterate while developing a package.", "License": "GPL-3", "URL": "https://github.com/r-lib/pkgload, https://pkgload.r-lib.org", "BugReports": "https://github.com/r-lib/pkgload/issues", "Depends": ["R (>= 3.4.0)"], "Imports": ["cli (>= 3.3.0)", "desc", "fs", "glue", "lifecycle", "methods", "pkgbuild", "processx", "rlang (>= 1.1.1)", "rprojroot", "utils", "withr (>= 2.4.3)"], "Suggests": ["bitops", "jsonlite", "mathjaxr", "pak", "Rcpp", "remotes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testthat (>= *******)", "usethis"], "Config/Needs/website": "tidyverse/tidytemplate, ggplot2", "Config/testthat/edition": "3", "Config/testthat/parallel": "TRUE", "Config/testthat/start-first": "dll", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre], Posit Software, PBC [cph, fnd], R Core team [ctb] (Some namespace and vignette code extracted from base R)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "plogr": {"Package": "plogr", "Version": "0.2.0", "Source": "Repository", "Title": "The 'plog' C++ Logging Library", "Date": "2018-03-24", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"cph\", comment = \"Author of the bundled plog library\"))", "Description": "A simple header-only logging library for C++. Add 'LinkingTo: plogr' to 'DESCRIPTION', and '#include <plogr.h>' in your C++ modules to use it.", "Suggests": ["Rcpp"], "License": "MIT + file LICENSE", "Encoding": "UTF-8", "LazyData": "true", "URL": "https://github.com/krlmlr/plogr#readme", "BugReports": "https://github.com/krlmlr/plogr/issues", "RoxygenNote": "6.0.1.9000", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre], <PERSON> [cph] (Author of the bundled plog library)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "png": {"Package": "png", "Version": "0.1-8", "Source": "Repository", "Title": "Read and write PNG images", "Author": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Maintainer": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Depends": ["R (>= 2.9.0)"], "Description": "This package provides an easy and simple way to read, write and display bitmap images stored in the PNG format. It can read and write both files and in-memory raw vectors.", "License": "GPL-2 | GPL-3", "SystemRequirements": "libpng", "URL": "http://www.rforge.net/png/", "NeedsCompilation": "yes", "Repository": "CRAN"}, "prettyunits": {"Package": "prettyunits", "Version": "1.2.0", "Source": "Repository", "Title": "Pretty, Human Readable Formatting of Quantities", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", email=\"<EMAIL>\", role=c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", email=\"<EMAIL>\", role=c(\"ctb\"), comment=c(ORCID=\"0000-0002-5759-428X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email=\"<EMAIL>\", role=c(\"ctb\")) )", "Description": "Pretty, human readable formatting of quantities. Time intervals: '1337000' -> '15d 11h 23m 20s'. Vague time intervals: '2674000' -> 'about a month ago'. Bytes: '1337' -> '1.34 kB'. Rounding: '99' with 3 significant digits -> '99.0' p-values: '0.00001' -> '<0.0001'. Colors: '#FF0000' -> 'red'. Quantities: '1239437' -> '1.24 M'.", "License": "MIT + file LICENSE", "URL": "https://github.com/r-lib/prettyunits", "BugReports": "https://github.com/r-lib/prettyunits/issues", "Depends": ["R(>= 2.10)"], "Suggests": ["codetools", "covr", "testthat"], "RoxygenNote": "7.2.3", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [ctb] (<https://orcid.org/0000-0002-5759-428X>), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "processx": {"Package": "processx", "Version": "3.8.6", "Source": "Repository", "Title": "Execute and Control System Processes", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\"), comment = c(ORCID = \"0000-0001-7098-9676\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"Ascent Digital Services\", role = c(\"cph\", \"fnd\")) )", "Description": "Tools to run system processes in the background.  It can check if a background process is running; wait on a background process to finish; get the exit status of finished processes; kill background processes. It can read the standard output and error of the processes, using non-blocking connections. 'processx' can poll a process for standard output or error, with a timeout. It can also poll several processes at once.", "License": "MIT + file LICENSE", "URL": "https://processx.r-lib.org, https://github.com/r-lib/processx", "BugReports": "https://github.com/r-lib/processx/issues", "Depends": ["R (>= 3.4.0)"], "Imports": ["ps (>= 1.2.0)", "R6", "utils"], "Suggests": ["callr (>= 3.7.3)", "cli (>= 3.3.0)", "codetools", "covr", "curl", "debugme", "parallel", "rlang (>= 1.0.2)", "testthat (>= 3.0.0)", "webfakes", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.1.9000", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre, cph] (<https://orcid.org/0000-0001-7098-9676>), <PERSON> [aut], Posit Software, PBC [cph, fnd], Ascent Digital Services [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "progress": {"Package": "progress", "Version": "1.2.3", "Source": "Repository", "Title": "Terminal Progress Bars", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Configurable Progress bars, they may include percentage, elapsed time, and/or the estimated completion time. They work in terminals, in 'Emacs' 'ESS', 'RStudio', 'Windows' 'Rgui' and the 'macOS' 'R.app'. The package also provides a 'C++' 'API', that works with or without 'Rcpp'.", "License": "MIT + file LICENSE", "URL": "https://github.com/r-lib/progress#readme, http://r-lib.github.io/progress/", "BugReports": "https://github.com/r-lib/progress/issues", "Depends": ["R (>= 3.6)"], "Imports": ["crayon", "hms", "prettyunits", "R6"], "Suggests": ["Rcpp", "testthat (>= 3.0.0)", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "ps": {"Package": "ps", "Version": "1.9.1", "Source": "Repository", "Title": "List, Query, Manipulate System Processes", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>'\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "List, query and manipulate all system processes, on 'Windows', 'Linux' and 'macOS'.", "License": "MIT + file LICENSE", "URL": "https://github.com/r-lib/ps, https://ps.r-lib.org/", "BugReports": "https://github.com/r-lib/ps/issues", "Depends": ["R (>= 3.4)"], "Imports": ["utils"], "Suggests": ["callr", "covr", "curl", "pillar", "pingr", "processx (>= 3.1.0)", "R6", "rlang", "testthat (>= 3.0.0)", "webfakes", "withr"], "Biarch": "true", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON><PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "purrr": {"Package": "purrr", "Version": "1.1.0", "Source": "Repository", "Title": "Functional Programming Tools", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"https://ror.org/03wc8by49\")) )", "Description": "A complete and consistent functional programming toolkit for R.", "License": "MIT + file LICENSE", "URL": "https://purrr.tidyverse.org/, https://github.com/tidyverse/purrr", "BugReports": "https://github.com/tidyverse/purrr/issues", "Depends": ["R (>= 4.1)"], "Imports": ["cli (>= 3.6.1)", "lifecycle (>= 1.0.3)", "magrittr (>= 1.5.0)", "rlang (>= 1.1.1)", "vctrs (>= 0.6.3)"], "Suggests": ["carrier (>= 0.2.0)", "covr", "dplyr (>= 0.7.8)", "httr", "knitr", "lubridate", "mirai (>= 2.4.0)", "rmarkdown", "testthat (>= 3.0.0)", "tibble", "tidyselect"], "LinkingTo": ["cli"], "VignetteBuilder": "knitr", "Biarch": "true", "Config/build/compilation-database": "true", "Config/Needs/website": "tidyverse/tidytemplate, tidyr", "Config/testthat/edition": "3", "Config/testthat/parallel": "TRUE", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut], Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "randomForest": {"Package": "randomForest", "Version": "4.7-1.2", "Source": "Repository", "Title": "Breiman and Cutlers Random Forests for Classification and Regression", "Date": "2022-01-24", "Depends": ["R (>= 4.1.0)", "stats"], "Suggests": ["RColorBrewer", "MASS"], "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", comment = \"Fortran original\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = \"Fortran original\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = \"R port\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = \"R port\"))", "Description": "Classification and regression based on a forest of trees using random inputs, based on <PERSON><PERSON><PERSON> (2001) <DOI:10.1023/A:1010933404324>.", "License": "GPL (>= 2)", "URL": "https://www.stat.berkeley.edu/~breiman/RandomForests/", "NeedsCompilation": "yes", "Repository": "CRAN", "Author": "<PERSON> [aut] (Fortran original), <PERSON> [aut] (Fortran original), <PERSON> [aut, cre] (R port), <PERSON> [aut] (R port)", "Maintainer": "<PERSON> <<EMAIL>>"}, "rappdirs": {"Package": "rapp<PERSON>s", "Version": "0.3.3", "Source": "Repository", "Type": "Package", "Title": "Application Directories: Determine Where to Save Data, Caches, and Logs", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = c(\"trl\", \"cre\", \"cph\"), email = \"<EMAIL>\"), person(given = \"RStudio\", role = \"cph\"), person(given = \"<PERSON><PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"aut\"), person(given = \"ActiveState\", role = \"cph\", comment = \"R/appdir.r, R/cache.r, R/data.r, R/log.r translated from appdirs\"), person(given = \"<PERSON>\", family = \"Petrisor\", role = \"ctb\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"trl\", \"aut\")), person(given = \"<PERSON><PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"ctb\"))", "Description": "An easy way to determine which directories on the users computer you should use to save data, caches and logs. A port of Python's 'Appdirs' (<https://github.com/ActiveState/appdirs>) to R.", "License": "MIT + file LICENSE", "URL": "https://rappdirs.r-lib.org, https://github.com/r-lib/rappdirs", "BugReports": "https://github.com/r-lib/rappdirs/issues", "Depends": ["R (>= 3.2)"], "Suggests": ["roxygen2", "testthat (>= 3.0.0)", "covr", "withr"], "Copyright": "Original python appdirs module copyright (c) 2010 ActiveState Software Inc. R port copyright Hadley <PERSON>, RStudio. See file LICENSE for details.", "Encoding": "UTF-8", "RoxygenNote": "7.1.1", "Config/testthat/edition": "3", "NeedsCompilation": "yes", "Author": "<PERSON> [trl, cre, cph], <PERSON><PERSON><PERSON> [cph], <PERSON><PERSON><PERSON> [aut], <PERSON> [aut], ActiveState [cph] (R/appdir.r, R/cache.r, R/data.r, R/log.r translated from appdirs), <PERSON> [ctb], <PERSON> [trl, aut], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "readr": {"Package": "readr", "Version": "2.1.5", "Source": "Repository", "Title": "Read Rectangular Text Data", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-6983-2759\")), person(\"<PERSON>\", \"<PERSON>rows\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"https://github.com/mandreyel/\", role = \"cph\", comment = \"mio library\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"grisu3 implementation\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"grisu3 implementation\") )", "Description": "The goal of 'readr' is to provide a fast and friendly way to read rectangular data (like 'csv', 'tsv', and 'fwf').  It is designed to flexibly parse many types of data found in the wild, while still cleanly failing when data unexpectedly changes.", "License": "MIT + file LICENSE", "URL": "https://readr.tidyverse.org, https://github.com/tidyverse/readr", "BugReports": "https://github.com/tidyverse/readr/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cli (>= 3.2.0)", "clipr", "crayon", "hms (>= 0.4.1)", "lifecycle (>= 0.2.0)", "methods", "R6", "rlang", "tibble", "utils", "vroom (>= 1.6.0)"], "Suggests": ["covr", "curl", "datasets", "knitr", "rmarkdown", "spelling", "stringi", "testthat (>= 3.2.0)", "tzdb (>= 0.1.1)", "waldo", "withr", "xml2"], "LinkingTo": ["cpp11", "tzdb (>= 0.1.1)"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "false", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.2.3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON> [aut, cre] (<https://orcid.org/0000-0002-6983-2759>), <PERSON> [ctb], Posit Software, PBC [cph, fnd], https://github.com/mandreyel/ [cph] (mio library), <PERSON><PERSON> [ctb, cph] (grisu3 implementation), <PERSON><PERSON><PERSON> [ctb, cph] (grisu3 implementation)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "renv": {"Package": "renv", "Version": "1.1.4", "Source": "Repository", "Type": "Package", "Title": "Project Environments", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-2880-7407\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A dependency management toolkit for R. Using 'renv', you can create and manage project-local R libraries, save the state of these libraries to a 'lockfile', and later restore your library as required. Together, these tools can help make your projects more isolated, portable, and reproducible.", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/renv/, https://github.com/rstudio/renv", "BugReports": "https://github.com/rstudio/renv/issues", "Imports": ["utils"], "Suggests": ["BiocManager", "cli", "compiler", "covr", "cpp11", "devtools", "gitcreds", "jsonlite", "jsonvalidate", "knitr", "miniUI", "modules", "packrat", "pak", "R6", "remotes", "reticulate", "rmarkdown", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shiny", "testthat", "uuid", "waldo", "yaml", "webfakes"], "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "bioconductor,python,install,restore,snapshot,retrieve,remotes", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-2880-7407>), <PERSON> [aut] (<https://orcid.org/0000-0003-4757-117X>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "repr": {"Package": "repr", "Version": "1.1.7", "Source": "Repository", "Title": "Serializable Representations", "Authors@R": "c( person('<PERSON>', '<PERSON><PERSON>', email = '<EMAIL>', role = c('aut', 'cre'), comment = c(ORCID = \"0000-0002-0369-2888\")), person('<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', email = '<EMAIL>', role = 'aut'), person('<PERSON>', '<PERSON><PERSON><PERSON>', email = '<EMAIL>', role = 'aut'), person('abielr', role = 'ctb'), person('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', role = 'ctb'), person('<PERSON>', '<PERSON><PERSON>', role = 'ctb'), person('karldw', role = 'ctb'), person('<PERSON>', '<PERSON>', role = 'ctb'), person('<PERSON>', '<PERSON>ever<PERSON>', role = 'ctb') )", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "String and binary representations of objects for several formats / mime types.", "URL": "https://github.com/IRkernel/repr/", "BugReports": "https://github.com/IRkernel/repr/issues/", "Depends": ["R (>= 3.0.1)"], "Imports": ["utils", "grDevices", "htmltools", "jsonlite", "pillar (>= 1.4.0)", "base64enc"], "Suggests": ["methods", "highr", "Cairo", "stringr", "testthat (>= 3.0.0)", "leaflet"], "Enhances": ["data.table", "tibble", "htmlwidgets", "vegalite", "plotly", "geo<PERSON><PERSON><PERSON>"], "Config/testthat/edition": "3", "License": "GPL (>= 3)", "Encoding": "UTF-8", "Collate": "'generics.r' 'options.r' 'package.r' 'repr_datatable.r' 'repr_datetime.r' 'utils.r' 'repr_list.r' 'repr_vector.r' 'repr_factor.r' 'repr_function.r' 'repr_help_files_with_topic.r' 'repr_htmlwidget.r' 'repr_matrix_df.r' 'repr_packageIQR.r' 'repr_plotly.r' 'repr_recordedplot.r' 'repr_spatial.r' 'repr_ts.r' 'repr_vega.r' 'zzz_onload.r'", "RoxygenNote": "7.3.1", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-0369-2888>), <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb]", "Repository": "CRAN"}, "restfulr": {"Package": "restfulr", "Version": "0.0.16", "Source": "Repository", "Type": "Package", "Title": "R Interface to RESTful Web Services", "Authors@R": "person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\")", "Description": "Models a RESTful service as if it were a nested R list.", "License": "Artistic-2.0", "Imports": ["XML", "<PERSON><PERSON><PERSON>", "r<PERSON><PERSON>", "S4Vectors (>= 0.13.15)", "yaml"], "Depends": ["R (>= 3.4.0)", "methods"], "Suggests": ["getPass", "rsolr", "RUnit"], "Collate": "CRUDProtocol-class.R CacheInfo-class.R Credentials-class.R HTTP-class.R Media-class.R MediaCache-class.R RestUri-class.R RestContainer-class.R test_restfulr_package.R utils.R", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "reticulate": {"Package": "reticulate", "Version": "1.43.0", "Source": "Repository", "Type": "Package", "Title": "Interface to 'Python'", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cre\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\"), person(\"RStudio\", role = c(\"cph\", \"fnd\")), person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cph\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-5243-233X\")), person(\"<PERSON>\", \"Eddelbuettel\", role = c(\"ctb\", \"cph\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"ctb\", \"cph\"), email = \"<EMAIL>\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"<PERSON>Thread library, http://tinythreadpp.bitsnbites.eu/\") )", "Description": "Interface to 'Python' modules, classes, and functions. When calling into 'Python', R data types are automatically converted to their equivalent 'Python' types. When values are returned from 'Python' to R they are converted back to R types. Compatible with all versions of 'Python' >= 2.7.", "License": "Apache License 2.0", "URL": "https://rstudio.github.io/reticulate/, https://github.com/rstudio/reticulate", "BugReports": "https://github.com/rstudio/reticulate/issues", "SystemRequirements": "Python (>= 2.7.0)", "Encoding": "UTF-8", "Depends": ["R (>= 3.5)"], "Imports": ["Matrix", "Rcpp (>= 1.0.7)", "RcppTOML", "graphics", "here", "jsonlite", "methods", "png", "rapp<PERSON>s", "utils", "rlang", "withr"], "Suggests": ["callr", "knitr", "glue", "cli", "rmarkdown", "pillar", "testthat"], "LinkingTo": ["Rcpp"], "RoxygenNote": "7.3.2", "VignetteBuilder": "knitr", "Config/build/compilation-database": "true", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [ctb, cre], <PERSON> [aut], <PERSON><PERSON> [aut], <PERSON><PERSON><PERSON> [cph, fnd], <PERSON> [aut, cph] (ORCID: <https://orcid.org/0000-0001-5243-233X>), <PERSON> [ctb, cph], <PERSON> [ctb, cph], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb, cph], <PERSON> [ctb, cph] (TinyThread library, http://tinythreadpp.bitsnbites.eu/)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rex": {"Package": "rex", "Version": "1.2.1", "Source": "Repository", "Type": "Package", "Title": "Friendly Regular Expressions", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"kev<PERSON><PERSON>@gmail.com\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"r<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com\", role = \"aut\") )", "Description": "A friendly interface for the construction of regular expressions.", "License": "MIT + file LICENSE", "URL": "https://github.com/kevinushey/rex", "BugReports": "https://github.com/kevinushey/rex/issues", "Imports": ["lazyeval"], "Suggests": ["covr", "dplyr", "ggplot2", "Hmisc", "knitr", "magrit<PERSON>", "rmarkdown", "roxygen2", "rvest", "stringr", "testthat"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.1.2", "Collate": "'aaa.R' 'utils.R' 'escape.R' 'capture.R' 'character_class.R' 'counts.R' 'lookarounds.R' 'match.R' 'or.R' 'rex-mode.R' 'rex.R' 'shortcuts.R' 'wildcards.R' 'zzz.R'", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut]", "Maintainer": "<PERSON> <kev<PERSON><PERSON>@gmail.com>", "Repository": "CRAN"}, "rjson": {"Package": "r<PERSON><PERSON>", "Version": "0.2.23", "Source": "Repository", "Title": "JSON for R", "Author": "<PERSON>-<PERSON><PERSON> [aut, cre]", "Authors@R": "person(given = \"<PERSON>\", family = \"Couture-Beil\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\")", "Maintainer": "<PERSON>-<PERSON><PERSON> <<EMAIL>>", "Depends": ["R (>= 4.0.0)"], "Description": "Converts R object into JSON objects and vice-versa.", "URL": "https://github.com/alexcb/rjson", "License": "GPL-2", "Repository": "CRAN", "NeedsCompilation": "yes"}, "rlang": {"Package": "rlang", "Version": "1.1.6", "Source": "Repository", "Title": "Functions for Base Types and Core R and 'Tidyverse' Features", "Description": "A toolbox for working with base types, core R features like the condition system, and core 'Tidyverse' features like tidy evaluation.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", ,\"<EMAIL>\", c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", ,\"<EMAIL>\", \"aut\"), person(given = \"mikefc\", email = \"<EMAIL>\", role = \"cph\", comment = \"Hash implementation based on <PERSON>'s xxhashlite\"), person(given = \"Yann\", family = \"Collet\", role = \"cph\", comment = \"Author of the embedded xxHash library\"), person(given = \"Posit, PBC\", role = c(\"cph\", \"fnd\")) )", "License": "MIT + file LICENSE", "ByteCompile": "true", "Biarch": "true", "Depends": ["R (>= 3.5.0)"], "Imports": ["utils"], "Suggests": ["cli (>= 3.1.0)", "covr", "crayon", "desc", "fs", "glue", "knitr", "magrit<PERSON>", "methods", "pillar", "pkgload", "rmarkdown", "stats", "testthat (>= 3.2.0)", "tibble", "usethis", "vctrs (>= 0.2.3)", "withr"], "Enhances": ["winch"], "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "URL": "https://rlang.r-lib.org, https://github.com/r-lib/rlang", "BugReports": "https://github.com/r-lib/rlang/issues", "Config/build/compilation-database": "true", "Config/testthat/edition": "3", "Config/Needs/website": "dplyr, tidyverse/tidytemplate", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut], mikef<PERSON> [cph] (Hash implementation based on <PERSON>'s xxhashlite), <PERSON><PERSON> [cph] (Author of the embedded xxHash library), Posit, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "roxygen2": {"Package": "roxygen2", "Version": "7.3.2", "Source": "Repository", "Title": "In-Line Documentation for R", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\", \"cph\"), comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cph\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cph\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Generate your Rd documentation, 'NAMESPACE' file, and collation field using specially formatted comments. Writing documentation in-line with code makes it easier to keep your documentation up-to-date as your requirements change. 'roxygen2' is inspired by the 'Doxygen' system for C++.", "License": "MIT + file LICENSE", "URL": "https://roxygen2.r-lib.org/, https://github.com/r-lib/roxygen2", "BugReports": "https://github.com/r-lib/roxygen2/issues", "Depends": ["R (>= 3.6)"], "Imports": ["brew", "cli (>= 3.3.0)", "commonmark", "desc (>= 1.2.0)", "knitr", "methods", "pkgload (>= 1.0.2)", "purrr (>= 1.0.0)", "R6 (>= 2.1.2)", "rlang (>= 1.0.6)", "stringi", "stringr (>= 1.0.0)", "utils", "withr", "xml2"], "Suggests": ["covr", "<PERSON>.<PERSON>S3", "<PERSON>.oo", "rmarkdown (>= 2.16)", "testthat (>= 3.1.2)", "yaml"], "LinkingTo": ["cpp11"], "VignetteBuilder": "knitr", "Config/Needs/development": "testthat", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "TRUE", "Encoding": "UTF-8", "Language": "en-GB", "RoxygenNote": "7.3.1.9000", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre, cph] (<https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut, cph], <PERSON><PERSON><PERSON> [aut], <PERSON> [aut, cph], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rprojroot": {"Package": "rprojroot", "Version": "2.1.0", "Source": "Repository", "Title": "Finding Files in Project Subdirectories", "Authors@R": "person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\"))", "Description": "Robust, reliable and flexible paths to files below a project root. The 'root' of a project is defined as a directory that matches a certain criterion, e.g., it contains a certain regular file.", "License": "MIT + file LICENSE", "URL": "https://rprojroot.r-lib.org/, https://github.com/r-lib/rprojroot", "BugReports": "https://github.com/r-lib/rprojroot/issues", "Depends": ["R (>= 3.0.0)"], "Suggests": ["covr", "knitr", "lifecycle", "rlang", "rmarkdown", "testthat (>= 3.2.0)", "withr"], "VignetteBuilder": "knitr", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "true", "Config/Needs/website": "tidyverse/tidytemplate", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rtracklayer": {"Package": "rtracklayer", "Version": "1.69.1", "Source": "Bioconductor", "Title": "R interface to genome annotation files and the UCSC genome browser", "Author": "<PERSON>, <PERSON>, <PERSON>", "Depends": ["R (>= 3.5)", "methods", "GenomicRanges (>= 1.37.2)"], "Imports": ["XML (>= 1.98-0)", "BiocGenerics (>= 0.35.3)", "S4Vectors (>= 0.23.18)", "IRanges (>= 2.13.13)", "XVector (>= 0.19.7)", "Seqinfo", "Biostrings (>= 2.77.2)", "curl", "httr", "Rsamtools (>= 1.31.2)", "GenomicAlignments (>= 1.15.6)", "BiocIO", "tools", "restfulr (>= 0.0.13)"], "Suggests": ["GenomeInfoDb", "BSgenome (>= 1.33.4)", "humanStemCell", "microRNA (>= 1.1.1)", "genefilter", "limma", "org.Hs.eg.db", "hgu133plus2.db", "GenomicFeatures", "BSgenome.Hsapiens.UCSC.hg19", "TxDb.Hsapiens.UCSC.hg19.knownGene", "RUnit"], "LinkingTo": ["S4Vectors", "IRanges", "XVector"], "Description": "Extensible framework for interacting with multiple genome  browsers (currently UCSC built-in) and manipulating  annotation tracks in various formats (currently GFF, BED, bedGraph, BED15, WIG, BigWig and 2bit built-in). The user may export/import tracks to/from the supported browsers, as well as query and modify the browser state, such as the current viewport.", "Maintainer": "<PERSON> <<EMAIL>>", "License": "Artistic-2.0 + file LICENSE", "Collate": "io.R web.R ranges.R trackDb.R browser.R ucsc.R readGFF.R gff.R bed.R wig.R utils.R bigWig.R bigBed.R chain.R quickload.R trackhub.R twobit.R fasta.R tabix.R bam.R trackTable.R index.R test_rtracklayer_package.R ncbi.R igv.R zzz.R", "biocViews": "Annotation,Visualization,DataImport", "git_url": "https://git.bioconductor.org/packages/rtracklayer", "git_branch": "devel", "git_last_commit": "7e8f402", "git_last_commit_date": "2025-06-20", "Repository": "Bioconductor 3.22", "NeedsCompilation": "yes"}, "snow": {"Package": "snow", "Version": "0.4-4", "Source": "Repository", "Title": "Simple Network of Workstations", "Author": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>", "Description": "Support for simple parallel computing in R.", "Maintainer": "<PERSON> <<EMAIL>>", "Suggests": ["<PERSON><PERSON><PERSON><PERSON>"], "Enhances": ["Rmpi"], "License": "GPL", "Depends": ["R (>= 2.13.1)", "utils"], "NeedsCompilation": "no", "Repository": "CRAN"}, "stringi": {"Package": "stringi", "Version": "1.8.7", "Source": "Repository", "Date": "2025-03-27", "Title": "Fast and Portable Character String Processing Facilities", "Description": "A collection of character string/text/natural language processing tools for pattern searching (e.g., with 'Java'-like regular expressions or the 'Unicode' collation algorithm), random string generation, case mapping, string transliteration, concatenation, sorting, padding, wrapping, Unicode normalisation, date-time formatting and parsing, and many more. They are fast, consistent, convenient, and - thanks to 'ICU' (International Components for Unicode) - portable across all locales and platforms. Documentation about 'stringi' is provided via its website at <https://stringi.gagolewski.com/> and the paper by <PERSON><PERSON><PERSON><PERSON> (2022, <doi:10.18637/jss.v103.i02>).", "URL": "https://stringi.gagolewski.com/, https://github.com/gagolews/stringi, https://icu.unicode.org/", "BugReports": "https://github.com/gagolews/stringi/issues", "SystemRequirements": "ICU4C (>= 61, optional)", "Type": "Package", "Depends": ["R (>= 3.4)"], "Imports": ["tools", "utils", "stats"], "Biarch": "TRUE", "License": "file LICENSE", "Authors@R": "c(person(given = \"<PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0637-6028\")), person(given = \"<PERSON><PERSON>\", family = \"Tartanus\", role = \"ctb\"), person(\"Unicode, Inc. and others\", role=\"ctb\", comment = \"ICU4C source code, Unicode Character Database\") )", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre, cph] (<https://orcid.org/0000-0003-0637-6028>), <PERSON><PERSON> [ctb], Unicode, Inc. and others [ctb] (ICU4C source code, Unicode Character Database)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "License_is_FOSS": "yes", "Repository": "CRAN"}, "stringr": {"Package": "stringr", "Version": "1.5.1", "Source": "Repository", "Title": "Simple, Consistent Wrappers for Common String Operations", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A consistent, simple and easy to use set of wrappers around the fantastic 'stringi' package. All function and argument names (and positions) are consistent, all functions deal with \"NA\"'s and zero length vectors in the same way, and the output from one function is easy to feed into the input of another.", "License": "MIT + file LICENSE", "URL": "https://stringr.tidyverse.org, https://github.com/tidyverse/stringr", "BugReports": "https://github.com/tidyverse/stringr/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cli", "glue (>= 1.6.1)", "lifecycle (>= 1.0.3)", "magrit<PERSON>", "rlang (>= 1.0.0)", "stringi (>= 1.5.3)", "vctrs (>= 0.4.0)"], "Suggests": ["covr", "dplyr", "gt", "htmltools", "htmlwidgets", "knitr", "rmarkdown", "testthat (>= 3.0.0)", "tibble"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre, cph], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "styler": {"Package": "styler", "Version": "1.10.3", "Source": "Repository", "Type": "Package", "Title": "Non-Invasive Pretty Printing of R Code", "Authors@R": "c(person(given = \"<PERSON><PERSON>\", family = \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"<PERSON>ren<PERSON>\", family = \"<PERSON>her<PERSON>\", role = c(\"cre\", \"aut\"), email = \"<EMAIL>\"), person(given = \"Indrajeet\", family = \"Patil\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-1995-6531\", Twitter = \"@patilindrajeets\")))", "Description": "Pretty-prints R code without changing the user's formatting intent.", "License": "MIT + file LICENSE", "URL": "https://github.com/r-lib/styler, https://styler.r-lib.org", "BugReports": "https://github.com/r-lib/styler/issues", "Depends": ["R (>= 3.6.0)"], "Imports": ["cli (>= 3.1.1)", "magrittr (>= 2.0.0)", "purrr (>= 0.2.3)", "R.cache (>= 0.15.0)", "rlang (>= 1.0.0)", "rprojroot (>= 1.1)", "tools", "vctrs (>= 0.4.1)", "withr (>= 2.3.0)"], "Suggests": ["data.tree (>= 0.1.6)", "digest", "here", "knitr", "prettycode", "rmarkdown", "roxygen2", "rstudioa<PERSON> (>= 0.7)", "tibble (>= 1.4.2)", "testthat (>= 3.0.0)"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Collate": "'addins.R' 'communicate.R' 'compat-dplyr.R' 'compat-tidyr.R' 'detect-alignment-utils.R' 'detect-alignment.R' 'environments.R' 'expr-is.R' 'indent.R' 'initialize.R' 'io.R' 'nest.R' 'nested-to-tree.R' 'parse.R' 'reindent.R' 'token-define.R' 'relevel.R' 'roxygen-examples-add-remove.R' 'roxygen-examples-find.R' 'roxygen-examples-parse.R' 'roxygen-examples.R' 'rules-indention.R' 'rules-line-breaks.R' 'rules-spaces.R' 'rules-tokens.R' 'serialize.R' 'set-assert-args.R' 'style-guides.R' 'styler-package.R' 'stylerignore.R' 'testing-mocks.R' 'testing-public-api.R' 'ui-caching.R' 'testing.R' 'token-create.R' 'transform-block.R' 'transform-code.R' 'transform-files.R' 'ui-styling.R' 'unindent.R' 'utils-cache.R' 'utils-files.R' 'utils-navigate-nest.R' 'utils-strings.R' 'utils.R' 'vertical.R' 'visit.R' 'zzz.R'", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-1416-3412>), <PERSON><PERSON><PERSON> [cre, aut], <PERSON><PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0003-1995-6531>, @patilindrajeets)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "sys": {"Package": "sys", "Version": "3.4.3", "Source": "Repository", "Type": "Package", "Title": "Powerful and Reliable Tools for Running System Commands in R", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"),  email = \"jeroeno<PERSON>@gmail.com\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"ctb\"))", "Description": "Drop-in replacements for the base system2() function with fine control and consistent behavior across platforms. Supports clean interruption, timeout,  background tasks, and streaming STDIN / STDOUT / STDERR over binary or text  connections. Arguments on Windows automatically get encoded and quoted to work  on different locales.", "License": "MIT + file LICENSE", "URL": "https://jeroen.r-universe.dev/sys", "BugReports": "https://github.com/jeroen/sys/issues", "Encoding": "UTF-8", "RoxygenNote": "7.1.1", "Suggests": ["unix (>= 1.4)", "spelling", "testthat"], "Language": "en-US", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>), <PERSON><PERSON><PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "tibble": {"Package": "tibble", "Version": "3.3.0", "Source": "Repository", "Title": "Simple Data Frames", "Authors@R": "c(person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"<PERSON>\", family = \"Wickham\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>in\", family = \"<PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"RStudio\", role = c(\"cph\", \"fnd\")))", "Description": "Provides a 'tbl_df' class (the 'tibble') with stricter checking and better formatting than the traditional data frame.", "License": "MIT + file LICENSE", "URL": "https://tibble.tidyverse.org/, https://github.com/tidyverse/tibble", "BugReports": "https://github.com/tidyverse/tibble/issues", "Depends": ["R (>= 3.4.0)"], "Imports": ["cli", "lifecycle (>= 1.0.0)", "magrit<PERSON>", "methods", "pillar (>= 1.8.1)", "pkgconfig", "rlang (>= 1.0.2)", "utils", "vctrs (>= 0.5.0)"], "Suggests": ["bench", "bit64", "blob", "brio", "callr", "DiagrammeR", "dplyr", "evaluate", "formattable", "ggplot2", "here", "hms", "htmltools", "knitr", "lubridate", "nycflights13", "pkgload", "purrr", "rmarkdown", "stringi", "testthat (>= 3.0.2)", "tidyr", "withr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "vignette-formats, as_tibble, add, invariants", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "true", "Config/autostyle/rmd": "false", "Config/Needs/website": "tidyverse/tidytemplate", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>), <PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "tidyr": {"Package": "tidyr", "Version": "1.3.1", "Source": "Repository", "Title": "Tidy Messy Data", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Tools to help to create tidy data, where each column is a variable, each row is an observation, and each cell contains a single value.  'tidyr' contains tools for changing the shape (pivoting) and hierarchy (nesting and 'unnesting') of a dataset, turning deeply nested lists into rectangular data frames ('rectangling'), and extracting values out of string columns. It also includes tools for working with missing values (both implicit and explicit).", "License": "MIT + file LICENSE", "URL": "https://tidyr.tidyverse.org, https://github.com/tidyverse/tidyr", "BugReports": "https://github.com/tidyverse/tidyr/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cli (>= 3.4.1)", "dplyr (>= 1.0.10)", "glue", "lifecycle (>= 1.0.3)", "magrit<PERSON>", "purrr (>= 1.0.1)", "rlang (>= 1.1.1)", "stringr (>= 1.5.0)", "tibble (>= 2.1.1)", "tidyselect (>= 1.2.0)", "utils", "vctrs (>= 0.5.2)"], "Suggests": ["covr", "data.table", "knitr", "readr", "repurrrsive (>= 1.1.0)", "rmarkdown", "testthat (>= 3.0.0)"], "LinkingTo": ["cpp11 (>= 0.4.0)"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.3.0", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "tidyselect": {"Package": "tidyselect", "Version": "1.2.1", "Source": "Repository", "Title": "Select from a Set of Strings", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A backend for the selecting functions of the 'tidyverse'.  It makes it easy to implement select-like functions in your own packages in a way that is consistent with other 'tidyverse' interfaces for selection.", "License": "MIT + file LICENSE", "URL": "https://tidyselect.r-lib.org, https://github.com/r-lib/tidyselect", "BugReports": "https://github.com/r-lib/tidyselect/issues", "Depends": ["R (>= 3.4)"], "Imports": ["cli (>= 3.3.0)", "glue (>= 1.3.0)", "lifecycle (>= 1.0.3)", "rlang (>= 1.0.4)", "vctrs (>= 0.5.2)", "withr"], "Suggests": ["covr", "crayon", "dplyr", "knitr", "magrit<PERSON>", "rmarkdown", "stringr", "testthat (>= 3.1.1)", "tibble (>= 2.1.3)"], "VignetteBuilder": "knitr", "ByteCompile": "true", "Config/testthat/edition": "3", "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.3.0.9000", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "txdbmaker": {"Package": "txdbmaker", "Version": "1.5.6", "Source": "Bioconductor", "Title": "Tools for making TxDb objects from genomic annotations", "Description": "A set of tools for making TxDb objects from genomic annotations from various sources (e.g. UCSC, Ensembl, and GFF files). These tools allow the user to download the genomic locations of transcripts, exons, and CDS, for a given assembly, and to import them in a TxDb object. TxDb objects are implemented in the GenomicFeatures package, together with flexible methods for extracting the desired features in convenient formats.", "biocViews": "Infrastructure, DataImport, Annotation, GenomeAnnotation, GenomeAssembly, Genetics, Sequencing", "URL": "https://bioconductor.org/packages/txdbmaker", "BugReports": "https://github.com/Bioconductor/txdbmaker/issues", "License": "Artistic-2.0", "Encoding": "UTF-8", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\"))", "Depends": ["BiocGenerics", "S4Vectors", "Seqinfo", "GenomicRanges (>= 1.61.1)", "GenomicFeatures (>= 1.61.4)"], "Imports": ["methods", "utils", "stats", "tools", "httr", "r<PERSON><PERSON>", "DBI", "RSQLite (>= 2.0)", "IRanges", "UCSC.utils", "GenomeInfoDb", "AnnotationDbi", "Biobase", "BiocIO", "rtracklayer", "biomaRt (>= 2.59.1)"], "Suggests": ["RMariaDB", "ensembldb", "GenomeInfoDbData", "RUnit", "BiocStyle", "knitr"], "VignetteBuilder": "knitr", "Collate": "utils.R Ensembl-utils.R findCompatibleMarts.R TxDb-schema.R TxDb-CREATE-TABLE-helpers.R makeTxDb.R makeTxDbFromUCSC.R makeTxDbFromBiomart.R makeTxDbFromEnsembl.R makeTxDbFromGRanges.R makeTxDbFromGFF.R makeFeatureDbFromUCSC.R makeTxDbPackage.R zzz.R", "git_url": "https://git.bioconductor.org/packages/txdbmaker", "git_branch": "devel", "git_last_commit": "c61f229", "git_last_commit_date": "2025-06-23", "Repository": "Bioconductor 3.22", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON><PERSON> [aut], <PERSON><PERSON> [aut], <PERSON><PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>"}, "tzdb": {"Package": "tzdb", "Version": "0.5.0", "Source": "Repository", "Title": "Time Zone Database Information", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"Hinnant\", role = \"cph\", comment = \"Author of the included date library\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Provides an up-to-date copy of the Internet Assigned Numbers Authority (IANA) Time Zone Database. It is updated periodically to reflect changes made by political bodies to time zone boundaries, UTC offsets, and daylight saving time rules. Additionally, this package provides a C++ interface for working with the 'date' library. 'date' provides comprehensive support for working with dates and date-times, which this package exposes to make it easier for other R packages to utilize. Headers are provided for calendar specific calculations, along with a limited interface for time zone manipulations.", "License": "MIT + file LICENSE", "URL": "https://tzdb.r-lib.org, https://github.com/r-lib/tzdb", "BugReports": "https://github.com/r-lib/tzdb/issues", "Depends": ["R (>= 4.0.0)"], "Suggests": ["covr", "testthat (>= 3.0.0)"], "LinkingTo": ["cpp11 (>= 0.5.2)"], "Biarch": "yes", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [cph] (Author of the included date library), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "utf8": {"Package": "utf8", "Version": "1.2.6", "Source": "Repository", "Title": "Unicode Text Processing", "Authors@R": "c(person(given = c(\"<PERSON>\", \"<PERSON><PERSON>\"), family = \"<PERSON>\", role = c(\"aut\", \"cph\")), person(given = \"Kirill\", family = \"M\\u00fcller\", role = \"cre\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"Unicode, Inc.\", role = c(\"cph\", \"dtc\"), comment = \"Unicode Character Database\"))", "Description": "Process and print 'UTF-8' encoded international text (Unicode). Input, validate, normalize, encode, format, and display.", "License": "Apache License (== 2.0) | file LICENSE", "URL": "https://krlmlr.github.io/utf8/, https://github.com/krlmlr/utf8", "BugReports": "https://github.com/krlmlr/utf8/issues", "Depends": ["R (>= 2.10)"], "Suggests": ["cli", "covr", "knitr", "rlang", "rmarkdown", "testthat (>= 3.0.0)", "withr"], "VignetteBuilder": "knitr, rmarkdown", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cph], <PERSON><PERSON> [cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>), Unicode, Inc. [cph, dtc] (Unicode Character Database)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "uuid": {"Package": "uuid", "Version": "1.2-1", "Source": "Repository", "Title": "Tools for Generating and Handling of UUIDs", "Author": "<PERSON> [aut, cre, cph] (https://urbanek.org, <https://orcid.org/0000-0003-2297-1732>), <PERSON> [aut, cph] (libuuid)", "Maintainer": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON>\", role=c(\"aut\",\"cre\",\"cph\"), email=\"<EMAIL>\", comment=c(\"https://urbanek.org\", ORCID=\"0000-0003-2297-1732\")), person(\"<PERSON>\",\"Ts'o\", email=\"<EMAIL>\", role=c(\"aut\",\"cph\"), comment=\"libuuid\"))", "Depends": ["R (>= 2.9.0)"], "Description": "Tools for generating and handling of UUIDs (Universally Unique Identifiers).", "License": "MIT + file LICENSE", "URL": "https://www.rforge.net/uuid", "BugReports": "https://github.com/s-u/uuid/issues", "NeedsCompilation": "yes", "Repository": "CRAN"}, "vctrs": {"Package": "vctrs", "Version": "0.6.5", "Source": "Repository", "Title": "Vector Helpers", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"data.table team\", role = \"cph\", comment = \"Radix sort based on data.table's forder() and their contribution to R's order()\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Defines new notions of prototype and size that are used to provide tools for consistent and well-founded type-coercion and size-recycling, and are in turn connected to ideas of type- and size-stability useful for analysing function interfaces.", "License": "MIT + file LICENSE", "URL": "https://vctrs.r-lib.org/, https://github.com/r-lib/vctrs", "BugReports": "https://github.com/r-lib/vctrs/issues", "Depends": ["R (>= 3.5.0)"], "Imports": ["cli (>= 3.4.0)", "glue", "lifecycle (>= 1.0.3)", "rlang (>= 1.1.0)"], "Suggests": ["bit64", "covr", "crayon", "dplyr (>= 0.8.5)", "generics", "knitr", "pillar (>= 1.4.4)", "pkgdown (>= 2.0.1)", "rmarkdown", "testthat (>= 3.0.0)", "tibble (>= 3.1.3)", "waldo (>= 0.2.0)", "withr", "xml2", "zeallot"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "Language": "en-GB", "RoxygenNote": "7.2.3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre], data.table team [cph] (Radix sort based on data.table's forder() and their contribution to R's order()), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "vroom": {"Package": "vroom", "Version": "1.6.5", "Source": "Repository", "Title": "Read and Write Rectangular Text Data Quickly", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2739-7082\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-6983-2759\")), person(\"<PERSON>\", \"Bearrows\", role = \"ctb\"), person(\"https://github.com/mandreyel/\", role = \"cph\", comment = \"mio library\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"cph\", comment = \"grisu3 implementation\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"cph\", comment = \"grisu3 implementation\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "The goal of 'vroom' is to read and write data (like 'csv', 'tsv' and 'fwf') quickly. When reading it uses a quick initial indexing step, then reads the values lazily , so only the data you actually use needs to be read.  The writer formats the data in parallel and writes to disk asynchronously from formatting.", "License": "MIT + file LICENSE", "URL": "https://vroom.r-lib.org, https://github.com/tidyverse/vroom", "BugReports": "https://github.com/tidyverse/vroom/issues", "Depends": ["R (>= 3.6)"], "Imports": ["bit64", "cli (>= 3.2.0)", "crayon", "glue", "hms", "lifecycle (>= 1.0.3)", "methods", "rlang (>= 0.4.2)", "stats", "tibble (>= 2.0.0)", "tidyselect", "tzdb (>= 0.1.1)", "vctrs (>= 0.2.0)", "withr"], "Suggests": ["archive", "bench (>= 1.1.0)", "covr", "curl", "dplyr", "forcats", "fs", "ggplot2", "knitr", "patchwork", "prettyunits", "purrr", "rmarkdown", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scales", "spelling", "testthat (>= 2.1.0)", "tidyr", "utils", "waldo", "xml2"], "LinkingTo": ["cpp11 (>= 0.2.0)", "progress (>= 1.2.1)", "tzdb (>= 0.1.1)"], "VignetteBuilder": "knitr", "Config/Needs/website": "nycflights13, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "false", "Copyright": "file COPYRIGHTS", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.2.3.9000", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0002-2739-7082>), <PERSON> [aut] (<https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-6983-2759>), <PERSON> [ctb], https://github.com/mandreyel/ [cph] (mio library), <PERSON><PERSON> [cph] (grisu3 implementation), <PERSON><PERSON><PERSON> [cph] (grisu3 implementation), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "withr": {"Package": "withr", "Version": "3.0.2", "Source": "Repository", "Title": "Run Code 'With' Temporarily Modified Global State", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"kev<PERSON><PERSON>@gmail.com\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A set of functions to run code 'with' safely and temporarily modified global state. Many of these functions were originally a part of the 'devtools' package, this provides a simple package with limited dependencies to provide access to these functions.", "License": "MIT + file LICENSE", "URL": "https://withr.r-lib.org, https://github.com/r-lib/withr#readme", "BugReports": "https://github.com/r-lib/withr/issues", "Depends": ["R (>= 3.6.0)"], "Imports": ["graphics", "grDevices"], "Suggests": ["callr", "DBI", "knitr", "methods", "rlang", "rmarkdown (>= 2.12)", "RSQLite", "testthat (>= 3.0.0)"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "Collate": "'aaa.R' 'collate.R' 'connection.R' 'db.R' 'defer-exit.R' 'standalone-defer.R' 'defer.R' 'devices.R' 'local_.R' 'with_.R' 'dir.R' 'env.R' 'file.R' 'language.R' 'libpaths.R' 'locale.R' 'makevars.R' 'namespace.R' 'options.R' 'par.R' 'path.R' 'rng.R' 'seed.R' 'wrap.R' 'sink.R' 'tempfile.R' 'timezone.R' 'torture.R' 'utils.R' 'with.R'", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], <PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "xfun": {"Package": "xfun", "Version": "0.53", "Source": "Repository", "Type": "Package", "Title": "Supporting Functions for Packages Maintained by '<PERSON><PERSON>'", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\", URL = \"https://yihui.org\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"Dai<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-5329-5987\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person() )", "Description": "Miscellaneous functions commonly used in other packages maintained by '<PERSON><PERSON>'.", "Depends": ["R (>= 3.2.0)"], "Imports": ["grDevices", "stats", "tools"], "Suggests": ["testit", "parallel", "codetools", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tinytex (>= 0.30)", "mime", "litedown (>= 0.6)", "commonmark", "knitr (>= 1.50)", "remotes", "pak", "curl", "xml2", "jsonlite", "magick", "yaml", "data.table", "qs"], "License": "MIT + file LICENSE", "URL": "https://github.com/yihui/xfun", "BugReports": "https://github.com/yihui/xfun/issues", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "VignetteBuilder": "litedown", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre, cph] (ORCID: <https://orcid.org/0000-0003-0645-5666>, URL: https://yihui.org), <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb] (ORCID: <https://orcid.org/0000-0002-5329-5987>), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "xml2": {"Package": "xml2", "Version": "1.4.0", "Source": "Repository", "Title": "Parse XML", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"O<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"R Foundation\", role = \"ctb\", comment = \"Copy of R-project homepage cached as example\") )", "Description": "Bindings to 'libxml2' for working with XML data using a simple,  consistent interface based on 'XPath' expressions. Also supports XML schema validation; for 'XSLT' transformations see the 'xslt' package.", "License": "MIT + file LICENSE", "URL": "https://xml2.r-lib.org, https://r-lib.r-universe.dev/xml2", "BugReports": "https://github.com/r-lib/xml2/issues", "Depends": ["R (>= 3.6.0)"], "Imports": ["cli", "methods", "rlang (>= 1.1.0)"], "Suggests": ["covr", "curl", "httr", "knitr", "magrit<PERSON>", "mockery", "rmarkdown", "testthat (>= 3.2.0)", "xslt"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "SystemRequirements": "libxml2: libxml2-dev (deb), libxml2-devel (rpm)", "Collate": "'S4.R' 'as_list.R' 'xml_parse.R' 'as_xml_document.R' 'classes.R' 'format.R' 'import-standalone-obj-type.R' 'import-standalone-purrr.R' 'import-standalone-types-check.R' 'init.R' 'nodeset_apply.R' 'paths.R' 'utils.R' 'xml2-package.R' 'xml_attr.R' 'xml_children.R' 'xml_document.R' 'xml_find.R' 'xml_missing.R' 'xml_modify.R' 'xml_name.R' 'xml_namespaces.R' 'xml_node.R' 'xml_nodeset.R' 'xml_path.R' 'xml_schema.R' 'xml_serialize.R' 'xml_structure.R' 'xml_text.R' 'xml_type.R' 'xml_url.R' 'xml_write.R' 'zzz.R'", "Config/testthat/edition": "3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre], Posit Software, PBC [cph, fnd], R Foundation [ctb] (Copy of R-project homepage cached as example)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "xmlparsedata": {"Package": "xmlparsedata", "Version": "1.0.5", "Source": "Repository", "Title": "Parse Data of 'R' Code as an 'XML' Tree", "Author": "<PERSON><PERSON><PERSON>", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Description": "Convert the output of 'utils::getParseData()' to an 'XML' tree, that one can search via 'XPath', and easier to manipulate in general.", "License": "MIT + file LICENSE", "LazyData": "true", "URL": "https://github.com/r-lib/xmlparsedata#readme", "BugReports": "https://github.com/r-lib/xmlparsedata/issues", "RoxygenNote": "6.0.1", "Suggests": ["covr", "testthat", "xml2"], "Depends": ["R (>= 3.0.0)"], "Encoding": "UTF-8", "NeedsCompilation": "no", "Repository": "CRAN"}, "yaml": {"Package": "yaml", "Version": "2.3.10", "Source": "Repository", "Type": "Package", "Title": "Methods to Convert R Data to YAML and Back", "Date": "2024-07-22", "Suggests": ["RUnit"], "Author": "<PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "License": "BSD_3_clause + file LICENSE", "Description": "Implements the 'libyaml' 'YAML' 1.1 parser and emitter (<https://pyyaml.org/wiki/LibYAML>) for R.", "URL": "https://github.com/vubiostat/r-yaml/", "BugReports": "https://github.com/vubiostat/r-yaml/issues", "NeedsCompilation": "yes", "Repository": "CRAN"}}}