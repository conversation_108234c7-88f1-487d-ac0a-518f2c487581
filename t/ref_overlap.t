#!/usr/bin/perl

use strict;
use warnings;
use Test::More tests => 6;
use FindBin;
use lib "$FindBin::Bin/../";
use crisprpro;

# Test 1: No overlap
my $p_f1 = [0, 1, 2, 100, 200, "data1"];
my $p_s1 = [
    [0, 1, 2, 300, 400, "data2"],
    [0, 1, 2, 50, 90, "data3"]
];
is(ref_overlap($p_f1, $p_s1), undef, "No overlap returns undef");

# Test 2: Exact match
my $p_f2 = [0, 1, 2, 100, 200, "data1"];
my $p_s2 = [
    [0, 1, 2, 300, 400, "data2"],
    [0, 1, 2, 100, 200, "data3"]
];
is_deeply(ref_overlap($p_f2, $p_s2), $p_f2, "Exact match returns original reference");

# Test 3: Partial overlap
my $p_f3 = [0, 1, 2, 100, 200, "data1"];
my $p_s3 = [
    [0, 1, 2, 50, 150, "data2"]
];
my $expected3 = [0, 1, 2, 50, 150, "data1"];
is_deeply(ref_overlap($p_f3, $p_s3), $expected3, "Partial overlap returns modified reference");

# Test 4: Multiple overlaps, should return first match
my $p_f4 = [0, 1, 2, 100, 200, "data1"];
my $p_s4 = [
    [0, 1, 2, 50, 150, "data2"],
    [0, 1, 2, 120, 180, "data3"]
];
my $expected4 = [0, 1, 2, 50, 150, "data1"];
is_deeply(ref_overlap($p_f4, $p_s4), $expected4, "Multiple overlaps returns first match");

# Test 5: Overlap at start
my $p_f5 = [0, 1, 2, 100, 200, "data1"];
my $p_s5 = [
    [0, 1, 2, 50, 120, "data2"]
];
my $expected5 = [0, 1, 2, 50, 120, "data1"];
is_deeply(ref_overlap($p_f5, $p_s5), $expected5, "Overlap at start returns correct boundaries");

# Test 6: Overlap at end
my $p_f6 = [0, 1, 2, 100, 200, "data1"];
my $p_s6 = [
    [0, 1, 2, 180, 250, "data2"]
];
my $expected6 = [0, 1, 2, 100, 200, "data1"];
is_deeply(ref_overlap($p_f6, $p_s6), $expected6, "Overlap at end returns correct boundaries");
