#!/usr/bin/perl
# revised by <PERSON><PERSON><PERSON><PERSON> (xian<PERSON><PERSON>@well.ox.ac.uk)

# Copyright (c) 2011 <PERSON> (<EMAIL>)
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
# THE SOFTWARE.
#
# ALSO, IT WOULD BE NICE IF YOU LET ME KNOW YOU USED IT.

package crisprpro;
require Exporter;
our @ISA = qw(Exporter);
our @EXPORT =
  qw(matchpam ref_overlap reverseComplement ccggfragcheck2 ReadBlatSecondbest OutputSpacers getTemperature getGC ReadBlatbest);

use strict;
use Data::Dumper;
use List::Util qw[min max];
use FindBin;
use lib "$FindBin::Bin/.";
use fastapro;
use strict;

use Getopt::Std;

sub matchpam {
    my $p_sequ    = $_[0];
    my $p_pampair = $_[1];
    my $winmin    = $_[2];
    my $winmax    = $_[3];

    my $sequ = $$p_sequ;
    my @pairpam;

    #for my $lseq( @$p_pampair ) {
    for ( my $i = 0 ; $i < scalar(@$p_pampair) ; $i++ ) {
        my $lseq = @$p_pampair[$i];
        my @left;
        my @right;

        # get all occurence of string at left end into array @left
        my $gg = index( $sequ, $lseq->[0] );
        while ( $gg >= 0 ) {
            push( @left, $gg );
            $gg = index( $sequ, $lseq->[0], $gg + 1 );
        }

        # get all occurence of string at right end into array @right
        $gg = index( $sequ, $lseq->[1] );
        while ( $gg >= 0 ) {
            push( @right, $gg );
            $gg = index( $sequ, $lseq->[1], $gg + 1 );
        }

        for my $tx (@left) {
            for my $ty (@right) {
                next if ( $ty - $tx < $winmin );
                last if ( $ty - $tx > $winmax );

              #my @tmp=($i, $tx, $ty);  #first one ($i) indicate the orientation
                my @tmp =
                  ( 0, $tx, $ty );    #first one ($i) indicate the orientation
                push( @pairpam, \@tmp );
            }
        }
    }
    return \@pairpam;
}

sub ref_overlap {
    my $p_f = $_[0];
    my $p_s = $_[1];

    for my $g (@$p_s) {
        next        if ( $g->[3] > $p_f->[4] || $g->[4] < $p_f->[3] );
        return $p_f if ( $g->[3] == $p_f->[3] && $g->[4] == $p_f->[4] );
        my @tmp = @$p_f;
        $tmp[3] = min( $g->[3], $p_f->[3] );
        $tmp[4] = min( $g->[4], $p_f->[4] );
        return \@tmp;
    }
    return undef;
}

###FUNCTION TO REVERSE COMPLEMENT A SEQUENCE
#!!! do not use it inside a sentsence, use it in assignement.
sub reverseComplement {
    $_ = shift;
    tr/ATGC/TACG/;
    return ( reverse() );
}

sub getGC {
    $_ = shift;
    my $a = length($_);
    return -1 if ( $a == 0 );
    s/[ATat]//g;
    my $b = length($_);
    return $b / $a;
}

sub getTemperature {
    $_ = shift;
    my $a = length($_);
    return -1 if ( $a == 0 );
    s/[ATat]//g;
    my $b = length($_);
    return 4 * $b + 2 * ( $a - $b );
}

sub ccggfragcheck2 {
    my $p_seq     = $_[0];
    my $p_frag    = $_[1];
    my $p_proh    = $_[2];
    my $spacelen  = $_[3];
    my $hangleft  = $_[4];
    my $hangright = $_[5];
    my $linkseq   = $_[6];

    my $s1_r = substr( $$p_seq, $p_frag->[1] + $hangleft, $spacelen );
    my $s2_r =
      substr( $$p_seq, $p_frag->[2] + $hangright - $spacelen, $spacelen );

    my $spaceer1;
    if ( $p_frag->[0] == 0 ) {
        $spaceer1 = reverseComplement($s1_r);
    }
    else {
        $spaceer1 = $s1_r;
    }

    my $spaceer2;
    if ( $p_frag->[0] == 0 ) {
        $spaceer2 = $s2_r;
    }
    else {
        $spaceer2 = reverseComplement($s2_r);
    }

# to remove the prohabited sequences, we need to added the flanking sequences to avoild prohabited sequences formed at the boundary
#my @spacers= ("GGCG".$spaceer1."GTTT", "TGTG".$spaceer2."GTTT");
    my @spacers = (
        $linkseq->[0] . $spaceer1 . $linkseq->[1],
        $linkseq->[2] . $spaceer2 . $linkseq->[3]
    );
    for my $ss (@spacers) {
        if ( $ss =~ /[^ATGCatgc]/ ) { return 0; }
        for my $p (@$p_proh) {
            my $tv = index( $ss, $p );
            if ( $tv >= 0 ) { return 0 }
        }
    }
    my $s1 = &getGC($spaceer1);
    if ( $s1 < 0.5 || $s1 > 0.8 ) { return 0 }
    my $s2 = &getGC($spaceer2);
    if ( $s2 < 0.5 || $s2 > 0.8 ) { return 0 }
    return 1;
}

# order, 0/1 return the best match; 2 return the second best match
sub ReadBlatbest {
    my ( $file, $order ) = @_;
    my $mat;    ##record the best blast entry
    my $secondbest;
    open( DENOVO, $file ) || die "Could not open $file\n";
    while (<DENOVO>) {
        chomp;
        my @a = split;
        if ( $a[0] > 0 ) {
            my (
                $match, $mismatch, $rep,    $N,     $QgapC, $lenN,
                $TgapC, $TgapN,    $strand, $QName, $Qsize, $Qstart,
                $Qend,  $TName,    $TSize,  $TStart
            ) = @a;
            my $penalty =
              ( $Qsize - $match ) + $mismatch + 1.5 * ( $QgapC + $TgapC );

            if ( !exists $mat->{$QName} ) {
                $mat->{$QName} =
                  [ $penalty, $match, $mismatch, $Qsize, $TName ];
                next;
            }

            next
              if ( $TName eq $mat->{$QName}->[4]
                && $TStart == $mat->{$QName}->[5] )
              ;    ## remove replicate records if exists.
            if ( $penalty < $mat->{$QName}->[0] ) {
                $secondbest->{$QName} =
                  $mat->{$QName};    ##recorded best becomes the second best.
                $mat->{$QName} =
                  [ $penalty, $match, $mismatch, $Qsize, $TName, $TStart ]
                  ;                  ## current one becomes the best
                next;
            }

            if ( !exists $secondbest->{$QName} ) {
                $secondbest->{$QName} =
                  [ $penalty, $match, $mismatch, $Qsize, $TName ];
                next;
            }
            next
              if ( $TName eq $secondbest->{$QName}->[4]
                && $TStart == $secondbest->{$QName}->[5] )
              ;    ## remove replicate records if exists.
            if ( $penalty < $secondbest->{$QName}->[0] ) {
                $secondbest->{$QName} =
                  [ $penalty, $match, $mismatch, $Qsize, $TName, $TStart ]
                  ;    ## current one becomes the best
            }
        }
    }
    if ( ( !defined $order ) || $order == 1 ) {
        return $mat;
    }
    if ( $order == 2 ) {
        return $secondbest;
    }
    return $mat;
}

sub ReadBlatSecondbest {
    my ($file) = @_;
    return ReadBlatbest( $file, 2 );
}

sub OutputSpacers {
    my ( $hindiv, $p_pampair ) = @_;

    print {$hindiv} "<p> Number of fragments satisfying our requirements: ",
      scalar(@$p_pampair), "</p>\n";
    return if ( scalar(@$p_pampair) == 0 );
    print {$hindiv} "<p>Designs are ranked based on two criteria:</p>";
    print {$hindiv}
      "<p>&nbsp &nbsp &nbsp &nbsp 1) Distance between nick sites;";
    print {$hindiv}
      "<p>&nbsp &nbsp &nbsp &nbsp 2) Relative position to the gene body.</p>";
    print {$hindiv} "<table border=\"1\"><tr>";
    print {$hindiv}
"<td>rank</td> <td>id</td> <td>CHROM</td> <td>PAM start</td> <td>PAM end</td> <td>PAM Out</td> <td>Spacer 1</td> <td>Spacer 2</td> <td>Score</td>";
    print {$hindiv} "</tr>";

    my $k = 1;

    #my @tmp=  sort {
    #	$b->[1] == $a->[1] ?
    #	    $b->[3] cmp $a->[3] :
    #	    $b->[1] <=> $a->[1]
    #   }  @$p_pampair ;

    #for my $z (@tmp) {
    for my $z (@$p_pampair) {
        print {$hindiv} "<tr>";
        print {$hindiv} "<td>", $k++,    "</td>";
        print {$hindiv} "<td>", $z->[2], "</td>";
        print {$hindiv} "<td>", $z->[0], "</td>";
        print {$hindiv} "<td>", $z->[3], "</td>";
        print {$hindiv} "<td>", $z->[4], "</td>";
        print {$hindiv} "<td>", $z->[5], "</td>";

        #print {$hindiv} "<td>", $z->[6], "</td>" ;
        print {$hindiv} "<td>", $z->[7], "</td>";

        #print {$hindiv} "<td>", $z->[8], "</td>" ;
        print {$hindiv} "<td>", $z->[9], "</td>";
        print {$hindiv} "<td>", $z->[1], "</td>";
        print {$hindiv} "</tr>";
    }
    print {$hindiv} "</table>";
}
