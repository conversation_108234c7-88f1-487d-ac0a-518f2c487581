{"cells": [{"cell_type": "markdown", "id": "511f6cf3", "metadata": {}, "source": ["# CRISPR KO\n", "\n", "## Nuclease\n", "\n", "`crisprprimer` package provides [`crisprprimer.nuclease.Nuclease`](reference/crisprprimer/nuclease/) like following."]}, {"cell_type": "code", "execution_count": 1, "id": "224d8b32", "metadata": {}, "outputs": [{"data": {"text/plain": ["Nuclease(pam=Seq('NGG'), cut_sites=(-3, -3), spacer_range=(-20, 0))"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from crisprprimer.nuclease import SpCas9\n", "\n", "SpCas9"]}, {"cell_type": "markdown", "id": "b86ea5df", "metadata": {}, "source": ["<div class=\"admonition note\">\n", "    <p class=\"admonition-title\">Note</p>\n", "    <p>\n", "        All coordinate internally used except exporting to human-readable format is UCSC BED-like 0-started for start position and 1-started for end position.\n", "    </p>\n", "</div>\n", "\n", "`cut_sites` is a two-item tuple indicates the Double Stranded Break position relative to\n", "PAM start. `spacer_range` is a two-item tuple indicates the relative position of spacer's start and end. The default spacers length is 20 nucleotides.\n", "\n", "## Target DNA\n", "\n", "As an example, we will design gRNAs that knockout the rice gene [LOC_Os11g30910](https://rice.uga.edu/cgi-bin/ORF_infopage.cgi?orf=LOC_Os11g30910), also named OsSOT1. To realize this we need to find all protospacer located in the coding region (CDS) of LOC_Os11g30910.\n", "\n", "To do so, we need to load rice gene annotations. BioV is a package can easily access various biological data directly from internet.\n", "\n", "At first, we should prepare data. Let's start from gene annotation."]}, {"cell_type": "code", "execution_count": 2, "id": "94c9d10b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>seqid</th>\n", "      <th>source</th>\n", "      <th>type</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>score</th>\n", "      <th>strand</th>\n", "      <th>phase</th>\n", "      <th>ID</th>\n", "      <th>Name</th>\n", "      <th>Note</th>\n", "      <th>Parent</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Chr1</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>gene</td>\n", "      <td>2902</td>\n", "      <td>10817</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os01g01010</td>\n", "      <td>LOC_Os01g01010</td>\n", "      <td>TBC domain containing protein, expressed</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Chr1</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>mRNA</td>\n", "      <td>2902</td>\n", "      <td>10817</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os01g01010.1</td>\n", "      <td>LOC_Os01g01010.1</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os01g01010</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Chr1</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>exon</td>\n", "      <td>2902</td>\n", "      <td>3268</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os01g01010.1:exon_1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os01g01010.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Chr1</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>exon</td>\n", "      <td>3353</td>\n", "      <td>3616</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os01g01010.1:exon_2</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os01g01010.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Chr1</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>exon</td>\n", "      <td>4356</td>\n", "      <td>4455</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os01g01010.1:exon_3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os01g01010.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>813783</th>\n", "      <td>ChrSy</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>exon</td>\n", "      <td>585816</td>\n", "      <td>586166</td>\n", "      <td>17.88</td>\n", "      <td>-</td>\n", "      <td>NaN</td>\n", "      <td>ChrSy.fgenesh.exon.326</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>ChrSy.fgenesh.mRNA.88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>813784</th>\n", "      <td>ChrSy</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>gene</td>\n", "      <td>589675</td>\n", "      <td>589999</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>ChrSy.fgenesh.gene.89</td>\n", "      <td>ChrSy.fgenesh.gene.89</td>\n", "      <td>expressed protein</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>813785</th>\n", "      <td>ChrSy</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>mRNA</td>\n", "      <td>589675</td>\n", "      <td>589999</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>ChrSy.fgenesh.mRNA.89</td>\n", "      <td>ChrSy.fgenesh.mRNA.89</td>\n", "      <td>NaN</td>\n", "      <td>ChrSy.fgenesh.gene.89</td>\n", "    </tr>\n", "    <tr>\n", "      <th>813786</th>\n", "      <td>ChrSy</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>CDS</td>\n", "      <td>589675</td>\n", "      <td>589999</td>\n", "      <td>11.35</td>\n", "      <td>+</td>\n", "      <td>0.0</td>\n", "      <td>ChrSy.fgenesh.CDS.327</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>ChrSy.fgenesh.mRNA.89</td>\n", "    </tr>\n", "    <tr>\n", "      <th>813787</th>\n", "      <td>ChrSy</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>exon</td>\n", "      <td>589675</td>\n", "      <td>589999</td>\n", "      <td>11.35</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>ChrSy.fgenesh.exon.327</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>ChrSy.fgenesh.mRNA.89</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>813788 rows × 12 columns</p>\n", "</div>"], "text/plain": ["        seqid      source  type   start     end  score strand  phase  \\\n", "0        Chr1  MSU_osa1r7  gene    2902   10817    NaN      +    NaN   \n", "1        Chr1  MSU_osa1r7  mRNA    2902   10817    NaN      +    NaN   \n", "2        Chr1  MSU_osa1r7  exon    2902    3268    NaN      +    NaN   \n", "3        Chr1  MSU_osa1r7  exon    3353    3616    NaN      +    NaN   \n", "4        Chr1  MSU_osa1r7  exon    4356    4455    NaN      +    NaN   \n", "...       ...         ...   ...     ...     ...    ...    ...    ...   \n", "813783  ChrSy  MSU_osa1r7  exon  585816  586166  17.88      -    NaN   \n", "813784  ChrSy  MSU_osa1r7  gene  589675  589999    NaN      +    NaN   \n", "813785  ChrSy  MSU_osa1r7  mRNA  589675  589999    NaN      +    NaN   \n", "813786  ChrSy  MSU_osa1r7   CDS  589675  589999  11.35      +    0.0   \n", "813787  ChrSy  MSU_osa1r7  exon  589675  589999  11.35      +    NaN   \n", "\n", "                             ID                   Name  \\\n", "0                LOC_Os01g01010         LOC_Os01g01010   \n", "1              LOC_Os01g01010.1       LOC_Os01g01010.1   \n", "2       LOC_Os01g01010.1:exon_1                    NaN   \n", "3       LOC_Os01g01010.1:exon_2                    NaN   \n", "4       LOC_Os01g01010.1:exon_3                    NaN   \n", "...                         ...                    ...   \n", "813783   ChrSy.fgenesh.exon.326                    NaN   \n", "813784    ChrSy.fgenesh.gene.89  ChrSy.fgenesh.gene.89   \n", "813785    ChrSy.fgenesh.mRNA.89  ChrSy.fgenesh.mRNA.89   \n", "813786    ChrSy.fgenesh.CDS.327                    NaN   \n", "813787   ChrSy.fgenesh.exon.327                    NaN   \n", "\n", "                                            Note                 Parent  \n", "0       TBC domain containing protein, expressed                    NaN  \n", "1                                            NaN         LOC_Os01g01010  \n", "2                                            NaN       LOC_Os01g01010.1  \n", "3                                            NaN       LOC_Os01g01010.1  \n", "4                                            NaN       LOC_Os01g01010.1  \n", "...                                          ...                    ...  \n", "813783                                       NaN  ChrSy.fgenesh.mRNA.88  \n", "813784                         expressed protein                    NaN  \n", "813785                                       NaN  ChrSy.fgenesh.gene.89  \n", "813786                                       NaN  ChrSy.fgenesh.mRNA.89  \n", "813787                                       NaN  ChrSy.fgenesh.mRNA.89  \n", "\n", "[813788 rows x 12 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from biov import read_gff3\n", "\n", "gff = read_gff3(\"https://rice.uga.edu/osa1r7_download/osa1_r7.all_models.gff3.gz\")\n", "gff"]}, {"cell_type": "markdown", "id": "af9e042e", "metadata": {}, "source": ["There are four types of records in this gene annotation, `gene`, `mRNA`, `exon` and `CDS`, If we should get all mRNAs of this gene and then get all CDSs of those mRNAs."]}, {"cell_type": "code", "execution_count": 3, "id": "5c14aa6c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>seqid</th>\n", "      <th>source</th>\n", "      <th>type</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>score</th>\n", "      <th>strand</th>\n", "      <th>phase</th>\n", "      <th>ID</th>\n", "      <th>Name</th>\n", "      <th>Note</th>\n", "      <th>Parent</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>737837</th>\n", "      <td>Chr11</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>mRNA</td>\n", "      <td>17984963</td>\n", "      <td>17986719</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os11g30910.1</td>\n", "      <td>LOC_Os11g30910.1</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os11g30910</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        seqid      source  type     start       end  score strand  phase  \\\n", "737837  Chr11  MSU_osa1r7  mRNA  17984963  17986719    NaN      +    NaN   \n", "\n", "                      ID              Name Note          Parent  \n", "737837  LOC_Os11g30910.1  LOC_Os11g30910.1  NaN  LOC_Os11g30910  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["mRNAs = gff.query(\"Parent == 'LOC_Os11g30910' and type == 'mRNA'\")\n", "mRNAs"]}, {"cell_type": "markdown", "id": "59b767e5", "metadata": {}, "source": ["As we can see, there is only one mRNA for this gene."]}, {"cell_type": "code", "execution_count": 4, "id": "6eebcc03", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>seqid</th>\n", "      <th>source</th>\n", "      <th>type</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>score</th>\n", "      <th>strand</th>\n", "      <th>phase</th>\n", "      <th>ID</th>\n", "      <th>Name</th>\n", "      <th>Note</th>\n", "      <th>Parent</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>737840</th>\n", "      <td>Chr11</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>CDS</td>\n", "      <td>17985010</td>\n", "      <td>17986198</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os11g30910.1:cds_1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os11g30910.1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        seqid      source type     start       end  score strand  phase  \\\n", "737840  Chr11  MSU_osa1r7  CDS  17985010  17986198    NaN      +    NaN   \n", "\n", "                            ID Name Note            Parent  \n", "737840  LOC_Os11g30910.1:cds_1  NaN  NaN  LOC_Os11g30910.1  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["CDSs = gff.query(\"Parent in @mRNAs['ID'] and type == 'CDS'\")\n", "CDSs"]}, {"cell_type": "markdown", "id": "df391c85", "metadata": {}, "source": ["There are also only one CDS for this mRNA.\n", "\n", "We also need to read the reference sequence. Let's fetch it from remote and inspect the\n", "number of sequence."]}, {"cell_type": "code", "execution_count": 5, "id": "78b3f1c0", "metadata": {}, "outputs": [{"data": {"text/plain": ["12"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from biov import read_fasta\n", "\n", "seqs = read_fasta(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\")\n", "len(seqs)"]}, {"cell_type": "markdown", "id": "9f2d0be6", "metadata": {}, "source": ["<PERSON> has 12 chromosomes, the result make sense.\n", "\n", "Since we already have reference sequence and corresponding CDS region, we can simply\n", "slice reference to get our target sequence data."]}, {"cell_type": "code", "execution_count": 6, "id": "03a6204d", "metadata": {}, "outputs": [{"data": {"text/plain": ["1338"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["CDS = CDSs.iloc[0]\n", "start, end = CDS[[\"start\", \"end\"]]\n", "extra_bases = (\n", "    60  # typically edit window size\n", "    + 2 * (len(SpCas9.pam) + abs(SpCas9.cut_sites[0]))  # two times sum of length of PAM and distance between cut sits and PAM\n", "    + 3  # extra one codon\n", ")\n", "seq = seqs[CDS[\"seqid\"]].seq[start - extra_bases:end +  extra_bases]\n", "len(seq)"]}, {"cell_type": "markdown", "id": "140785e8", "metadata": {}, "source": ["Now we have target sequence with 1338 bp length.\n", "\n", "## Find out target sequence unique among genome\n", "\n", "Some genes might have various copies across genome, like <PERSON><PERSON>' EL5 gene family, it appears six times on Chr2.\n", "\n", "We need to make sure our target LOC_Os11g30910.1:cds_1"]}, {"cell_type": "code", "execution_count": 7, "id": "7edb81d9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 1338 bases in 1 sequences\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>matches</th>\n", "      <th>misMatches</th>\n", "      <th>repMatches</th>\n", "      <th>nCount</th>\n", "      <th>qNumInsert</th>\n", "      <th>qBaseInsert</th>\n", "      <th>tNumInsert</th>\n", "      <th>tBaseInsert</th>\n", "      <th>strand</th>\n", "      <th>qName</th>\n", "      <th>...</th>\n", "      <th>qStart</th>\n", "      <th>qEnd</th>\n", "      <th>tName</th>\n", "      <th>tSize</th>\n", "      <th>tStart</th>\n", "      <th>tEnd</th>\n", "      <th>blockCount</th>\n", "      <th>blockSizes</th>\n", "      <th>qStarts</th>\n", "      <th>tStarts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>55</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>+</td>\n", "      <td>AGTCCAGAGAG_1338</td>\n", "      <td>...</td>\n", "      <td>1283</td>\n", "      <td>1338</td>\n", "      <td>Chr9</td>\n", "      <td>23012720</td>\n", "      <td>4173696</td>\n", "      <td>4173753</td>\n", "      <td>3</td>\n", "      <td>9,9,37,</td>\n", "      <td>1283,1292,1301,</td>\n", "      <td>4173696,4173706,4173716,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>55</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>+</td>\n", "      <td>AGTCCAGAGAG_1338</td>\n", "      <td>...</td>\n", "      <td>1283</td>\n", "      <td>1338</td>\n", "      <td>Chr9</td>\n", "      <td>23012720</td>\n", "      <td>5405694</td>\n", "      <td>5405751</td>\n", "      <td>3</td>\n", "      <td>9,9,37,</td>\n", "      <td>1283,1292,1301,</td>\n", "      <td>5405694,5405704,5405714,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>53</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>AGTCCAGAGAG_1338</td>\n", "      <td>...</td>\n", "      <td>1283</td>\n", "      <td>1338</td>\n", "      <td>Chr9</td>\n", "      <td>23012720</td>\n", "      <td>15951467</td>\n", "      <td>15951521</td>\n", "      <td>2</td>\n", "      <td>25,29,</td>\n", "      <td>1283,1309,</td>\n", "      <td>15951467,15951492,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>55</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>34</td>\n", "      <td>+</td>\n", "      <td>AGTCCAGAGAG_1338</td>\n", "      <td>...</td>\n", "      <td>1283</td>\n", "      <td>1338</td>\n", "      <td>Chr9</td>\n", "      <td>23012720</td>\n", "      <td>5132959</td>\n", "      <td>5133048</td>\n", "      <td>3</td>\n", "      <td>9,28,18,</td>\n", "      <td>1283,1292,1320,</td>\n", "      <td>5132959,5132969,5133030,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>52</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>+</td>\n", "      <td>AGTCCAGAGAG_1338</td>\n", "      <td>...</td>\n", "      <td>1286</td>\n", "      <td>1338</td>\n", "      <td>Chr9</td>\n", "      <td>23012720</td>\n", "      <td>9562312</td>\n", "      <td>9562366</td>\n", "      <td>3</td>\n", "      <td>6,9,37,</td>\n", "      <td>1286,1292,1301,</td>\n", "      <td>9562312,9562319,9562329,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>316</th>\n", "      <td>36</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>AGTCCAGAGAG_1338</td>\n", "      <td>...</td>\n", "      <td>1301</td>\n", "      <td>1338</td>\n", "      <td>Chr1</td>\n", "      <td>43270923</td>\n", "      <td>34173653</td>\n", "      <td>34173690</td>\n", "      <td>1</td>\n", "      <td>37,</td>\n", "      <td>0,</td>\n", "      <td>34173653,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>317</th>\n", "      <td>36</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>AGTCCAGAGAG_1338</td>\n", "      <td>...</td>\n", "      <td>1301</td>\n", "      <td>1338</td>\n", "      <td>Chr1</td>\n", "      <td>43270923</td>\n", "      <td>34173060</td>\n", "      <td>34173097</td>\n", "      <td>1</td>\n", "      <td>37,</td>\n", "      <td>0,</td>\n", "      <td>34173060,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>318</th>\n", "      <td>32</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>AGTCCAGAGAG_1338</td>\n", "      <td>...</td>\n", "      <td>1306</td>\n", "      <td>1338</td>\n", "      <td>Chr1</td>\n", "      <td>43270923</td>\n", "      <td>31955498</td>\n", "      <td>31955530</td>\n", "      <td>1</td>\n", "      <td>32,</td>\n", "      <td>0,</td>\n", "      <td>31955498,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>319</th>\n", "      <td>36</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>10</td>\n", "      <td>-</td>\n", "      <td>AGTCCAGAGAG_1338</td>\n", "      <td>...</td>\n", "      <td>1301</td>\n", "      <td>1338</td>\n", "      <td>Chr1</td>\n", "      <td>43270923</td>\n", "      <td>34535727</td>\n", "      <td>34535774</td>\n", "      <td>2</td>\n", "      <td>7,30,</td>\n", "      <td>0,7,</td>\n", "      <td>34535727,34535744,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>320</th>\n", "      <td>34</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>AGTCCAGAGAG_1338</td>\n", "      <td>...</td>\n", "      <td>1302</td>\n", "      <td>1338</td>\n", "      <td>Chr1</td>\n", "      <td>43270923</td>\n", "      <td>8091176</td>\n", "      <td>8091211</td>\n", "      <td>2</td>\n", "      <td>29,6,</td>\n", "      <td>0,30,</td>\n", "      <td>8091176,8091205,</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>321 rows × 21 columns</p>\n", "</div>"], "text/plain": ["     matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", "0         55           0           0       0           0            0   \n", "1         55           0           0       0           0            0   \n", "2         53           1           0       0           1            1   \n", "3         55           0           0       0           0            0   \n", "4         52           0           0       0           0            0   \n", "..       ...         ...         ...     ...         ...          ...   \n", "316       36           1           0       0           0            0   \n", "317       36           1           0       0           0            0   \n", "318       32           0           0       0           0            0   \n", "319       36           1           0       0           0            0   \n", "320       34           1           0       0           1            1   \n", "\n", "     tNumInsert  tBaseInsert strand             qName  ...  qStart  qEnd  \\\n", "0             2            2      +  AGTCCAGAGAG_1338  ...    1283  1338   \n", "1             2            2      +  AGTCCAGAGAG_1338  ...    1283  1338   \n", "2             0            0      +  AGTCCAGAGAG_1338  ...    1283  1338   \n", "3             2           34      +  AGTCCAGAGAG_1338  ...    1283  1338   \n", "4             2            2      +  AGTCCAGAGAG_1338  ...    1286  1338   \n", "..          ...          ...    ...               ...  ...     ...   ...   \n", "316           0            0      -  AGTCCAGAGAG_1338  ...    1301  1338   \n", "317           0            0      -  AGTCCAGAGAG_1338  ...    1301  1338   \n", "318           0            0      -  AGTCCAGAGAG_1338  ...    1306  1338   \n", "319           1           10      -  AGTCCAGAGAG_1338  ...    1301  1338   \n", "320           0            0      -  AGTCCAGAGAG_1338  ...    1302  1338   \n", "\n", "     tName     tSize    tStart      tEnd  blockCount  blockSizes  \\\n", "0     Chr9  23012720   4173696   4173753           3     9,9,37,   \n", "1     Chr9  23012720   5405694   5405751           3     9,9,37,   \n", "2     Chr9  23012720  15951467  15951521           2      25,29,   \n", "3     Chr9  23012720   5132959   5133048           3    9,28,18,   \n", "4     Chr9  23012720   9562312   9562366           3     6,9,37,   \n", "..     ...       ...       ...       ...         ...         ...   \n", "316   Chr1  43270923  34173653  34173690           1         37,   \n", "317   Chr1  43270923  34173060  34173097           1         37,   \n", "318   Chr1  43270923  31955498  31955530           1         32,   \n", "319   Chr1  43270923  34535727  34535774           2       7,30,   \n", "320   Chr1  43270923   8091176   8091211           2       29,6,   \n", "\n", "             qStarts                   tStarts  \n", "0    1283,1292,1301,  4173696,4173706,4173716,  \n", "1    1283,1292,1301,  5405694,5405704,5405714,  \n", "2         1283,1309,        15951467,15951492,  \n", "3    1283,1292,1320,  5132959,5132969,5133030,  \n", "4    1286,1292,1301,  9562312,9562319,9562329,  \n", "..               ...                       ...  \n", "316               0,                 34173653,  \n", "317               0,                 34173060,  \n", "318               0,                 31955498,  \n", "319             0,7,        34535727,34535744,  \n", "320            0,30,          8091176,8091205,  \n", "\n", "[321 rows x 21 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from biov.executables import blat\n", "\n", "blat(\n", "    \"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\",\n", "    str(seq),\n", ")"]}, {"cell_type": "markdown", "id": "e12505b3", "metadata": {}, "source": ["321 rows is returned, a little bit long, but it is not a problem, as we can see, most of\n", "them have matches no more than 60 bp, and only one of them has 1338 bp match, which is our target sequence.\n", "\n", "Let's check this by raising the minScore to 100."]}, {"cell_type": "code", "execution_count": 8, "id": "5eeb52c5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 1338 bases in 1 sequences\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>matches</th>\n", "      <th>misMatches</th>\n", "      <th>repMatches</th>\n", "      <th>nCount</th>\n", "      <th>qNumInsert</th>\n", "      <th>qBaseInsert</th>\n", "      <th>tNumInsert</th>\n", "      <th>tBaseInsert</th>\n", "      <th>strand</th>\n", "      <th>qName</th>\n", "      <th>...</th>\n", "      <th>qStart</th>\n", "      <th>qEnd</th>\n", "      <th>tName</th>\n", "      <th>tSize</th>\n", "      <th>tStart</th>\n", "      <th>tEnd</th>\n", "      <th>blockCount</th>\n", "      <th>blockSizes</th>\n", "      <th>qStarts</th>\n", "      <th>tStarts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1338</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>AGTCCAGAGAG_1338</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1338</td>\n", "      <td>Chr11</td>\n", "      <td>29021106</td>\n", "      <td>17984935</td>\n", "      <td>17986273</td>\n", "      <td>1</td>\n", "      <td>1338,</td>\n", "      <td>0,</td>\n", "      <td>17984935,</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 21 columns</p>\n", "</div>"], "text/plain": ["   matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", "0     1338           0           0       0           0            0   \n", "\n", "   tNumInsert  tBaseInsert strand             qName  ...  qStart  qEnd  tName  \\\n", "0           0            0      +  AGTCCAGAGAG_1338  ...       0  1338  Chr11   \n", "\n", "      tSize    tStart      tEnd  blockCount  blockSizes qStarts    tStarts  \n", "0  29021106  17984935  17986273           1       1338,      0,  17984935,  \n", "\n", "[1 rows x 21 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["blat(\n", "    \"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\",\n", "    str(seq),\n", "    minScore=100,\n", ")"]}, {"cell_type": "markdown", "id": "5ed7352e", "metadata": {}, "source": ["Now, only one match is returned, and the match length is 1338 bp, which is our target sequence.\n", "\n", "## Designing spacer sequences\n", "\n", "The next thing we need to do is finding spacers on the target sequence.\n", "\n", "`SpCas9` has a method named `find_spacers_on` which take `Bio.Seq.Seq` object as input.\n", "It will find spacers on both positive and negative strands. Remember that `seq` object\n", "does not have offset information, so we need to add CDS's `start` as offset to get\n", "correct coordinates."]}, {"cell_type": "code", "execution_count": 9, "id": "6b6905e1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>17984939</td>\n", "      <td>17984959</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>CAGAGAGGCAGAGGCCATTA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>17984983</td>\n", "      <td>17985003</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>AGAACTCAATCGCTATAAAA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>17984987</td>\n", "      <td>17985007</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CTCAATCGCTATAAAATGGA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>17984993</td>\n", "      <td>17985013</td>\n", "      <td>AGG</td>\n", "      <td>+</td>\n", "      <td>CGCTATAAAATGGACGGATG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>17985048</td>\n", "      <td>17985068</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>CACGATACTAGCTAGCCCGA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>248</th>\n", "      <td>17986169</td>\n", "      <td>17986189</td>\n", "      <td>AGG</td>\n", "      <td>-</td>\n", "      <td>GTCGCCGGCAGCGGCGGCAA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>249</th>\n", "      <td>17986175</td>\n", "      <td>17986195</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TTCGGAGTCGCCGGCAGCGG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>250</th>\n", "      <td>17986178</td>\n", "      <td>17986198</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TCATTCGGAGTCGCCGGCAG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>251</th>\n", "      <td>17986184</td>\n", "      <td>17986204</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GCGATCTCATTCGGAGTCGC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>252</th>\n", "      <td>17986193</td>\n", "      <td>17986213</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TTGACACATGCGATCTCATT</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>253 rows × 5 columns</p>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer\n", "0    17984939  17984959  TGG      +  CAGAGAGGCAGAGGCCATTA\n", "1    17984983  17985003  TGG      +  AGAACTCAATCGCTATAAAA\n", "2    17984987  17985007  CGG      +  CTCAATCGCTATAAAATGGA\n", "3    17984993  17985013  AGG      +  CGCTATAAAATGGACGGATG\n", "4    17985048  17985068  TGG      +  CACGATACTAGCTAGCCCGA\n", "..        ...       ...  ...    ...                   ...\n", "248  17986169  17986189  AGG      -  GTCGCCGGCAGCGGCGGCAA\n", "249  17986175  17986195  CGG      -  TTCGGAGTCGCCGGCAGCGG\n", "250  17986178  17986198  CGG      -  TCATTCGGAGTCGCCGGCAG\n", "251  17986184  17986204  CGG      -  GCGATCTCATTCGGAGTCGC\n", "252  17986193  17986213  CGG      -  TTGACACATGCGATCTCATT\n", "\n", "[253 rows x 5 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["spacers = SpCas9.find_spacers_on(seq)\n", "spacers[[\"start\", \"end\"]] += start - extra_bases\n", "spacers"]}, {"cell_type": "markdown", "id": "fe89416f", "metadata": {}, "source": ["It works, now we have 253 spacers designed.\n", "\n", "## GC fraction\n", "\n", "GC content should between 20% and 80% make the gRNAs stable. We can calculate all spacers' GC content via `Bio.SeqUtils.gc_fraction`"]}, {"cell_type": "code", "execution_count": 10, "id": "a977cbe5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>gc_fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>17984939</td>\n", "      <td>17984959</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>CAGAGAGGCAGAGGCCATTA</td>\n", "      <td>0.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>17984983</td>\n", "      <td>17985003</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>AGAACTCAATCGCTATAAAA</td>\n", "      <td>0.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>17984987</td>\n", "      <td>17985007</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CTCAATCGCTATAAAATGGA</td>\n", "      <td>0.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>17984993</td>\n", "      <td>17985013</td>\n", "      <td>AGG</td>\n", "      <td>+</td>\n", "      <td>CGCTATAAAATGGACGGATG</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>17985048</td>\n", "      <td>17985068</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>CACGATACTAGCTAGCCCGA</td>\n", "      <td>0.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>248</th>\n", "      <td>17986169</td>\n", "      <td>17986189</td>\n", "      <td>AGG</td>\n", "      <td>-</td>\n", "      <td>GTCGCCGGCAGCGGCGGCAA</td>\n", "      <td>0.80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>249</th>\n", "      <td>17986175</td>\n", "      <td>17986195</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TTCGGAGTCGCCGGCAGCGG</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>250</th>\n", "      <td>17986178</td>\n", "      <td>17986198</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TCATTCGGAGTCGCCGGCAG</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>251</th>\n", "      <td>17986184</td>\n", "      <td>17986204</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GCGATCTCATTCGGAGTCGC</td>\n", "      <td>0.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>252</th>\n", "      <td>17986193</td>\n", "      <td>17986213</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TTGACACATGCGATCTCATT</td>\n", "      <td>0.40</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>221 rows × 6 columns</p>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer  gc_fraction\n", "0    17984939  17984959  TGG      +  CAGAGAGGCAGAGGCCATTA         0.55\n", "1    17984983  17985003  TGG      +  AGAACTCAATCGCTATAAAA         0.30\n", "2    17984987  17985007  CGG      +  CTCAATCGCTATAAAATGGA         0.35\n", "3    17984993  17985013  AGG      +  CGCTATAAAATGGACGGATG         0.45\n", "4    17985048  17985068  TGG      +  CACGATACTAGCTAGCCCGA         0.55\n", "..        ...       ...  ...    ...                   ...          ...\n", "248  17986169  17986189  AGG      -  GTCGCCGGCAGCGGCGGCAA         0.80\n", "249  17986175  17986195  CGG      -  TTCGGAGTCGCCGGCAGCGG         0.75\n", "250  17986178  17986198  CGG      -  TCATTCGGAGTCGCCGGCAG         0.65\n", "251  17986184  17986204  CGG      -  GCGATCTCATTCGGAGTCGC         0.60\n", "252  17986193  17986213  CGG      -  TTGACACATGCGATCTCATT         0.40\n", "\n", "[221 rows x 6 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from Bio.SeqUtils import gc_fraction\n", "\n", "spacers[\"gc_fraction\"] = spacers[\"spacer\"].apply(gc_fraction)\n", "spacers = spacers[(spacers[\"gc_fraction\"] >= 0.2) & (spacers[\"gc_fraction\"] <= 0.8)].copy()\n", "spacers"]}, {"cell_type": "markdown", "id": "cc7bc211", "metadata": {}, "source": ["After filtering, we have 221 spacers left.\n", "\n", "# Restriction enzymes & consecutive T nucleotides\n", "\n", "Restriction enzymes are usually involved in the gRNA library synthesis process. Removing gRNAs that contain specific restriction sites is often necessary. We provided the mapping `RESTRICTION_ENZYMES` from name to sequence.\n", "\n", "By default, BsaI and BbsI are used."]}, {"cell_type": "code", "execution_count": 11, "id": "b7f5302d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>gc_fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>17984939</td>\n", "      <td>17984959</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>CAGAGAGGCAGAGGCCATTA</td>\n", "      <td>0.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>17984983</td>\n", "      <td>17985003</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>AGAACTCAATCGCTATAAAA</td>\n", "      <td>0.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>17984987</td>\n", "      <td>17985007</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CTCAATCGCTATAAAATGGA</td>\n", "      <td>0.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>17984993</td>\n", "      <td>17985013</td>\n", "      <td>AGG</td>\n", "      <td>+</td>\n", "      <td>CGCTATAAAATGGACGGATG</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>17985048</td>\n", "      <td>17985068</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>CACGATACTAGCTAGCCCGA</td>\n", "      <td>0.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>248</th>\n", "      <td>17986169</td>\n", "      <td>17986189</td>\n", "      <td>AGG</td>\n", "      <td>-</td>\n", "      <td>GTCGCCGGCAGCGGCGGCAA</td>\n", "      <td>0.80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>249</th>\n", "      <td>17986175</td>\n", "      <td>17986195</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TTCGGAGTCGCCGGCAGCGG</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>250</th>\n", "      <td>17986178</td>\n", "      <td>17986198</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TCATTCGGAGTCGCCGGCAG</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>251</th>\n", "      <td>17986184</td>\n", "      <td>17986204</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GCGATCTCATTCGGAGTCGC</td>\n", "      <td>0.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>252</th>\n", "      <td>17986193</td>\n", "      <td>17986213</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TTGACACATGCGATCTCATT</td>\n", "      <td>0.40</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>218 rows × 6 columns</p>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer  gc_fraction\n", "0    17984939  17984959  TGG      +  CAGAGAGGCAGAGGCCATTA         0.55\n", "1    17984983  17985003  TGG      +  AGAACTCAATCGCTATAAAA         0.30\n", "2    17984987  17985007  CGG      +  CTCAATCGCTATAAAATGGA         0.35\n", "3    17984993  17985013  AGG      +  CGCTATAAAATGGACGGATG         0.45\n", "4    17985048  17985068  TGG      +  CACGATACTAGCTAGCCCGA         0.55\n", "..        ...       ...  ...    ...                   ...          ...\n", "248  17986169  17986189  AGG      -  GTCGCCGGCAGCGGCGGCAA         0.80\n", "249  17986175  17986195  CGG      -  TTCGGAGTCGCCGGCAGCGG         0.75\n", "250  17986178  17986198  CGG      -  TCATTCGGAGTCGCCGGCAG         0.65\n", "251  17986184  17986204  CGG      -  GCGATCTCATTCGGAGTCGC         0.60\n", "252  17986193  17986213  CGG      -  TTGACACATGCGATCTCATT         0.40\n", "\n", "[218 rows x 6 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["from crisprprimer import RESTRICTION_ENZYMES\n", "\n", "default_enzymes = (\"BsaI\", \"BbsI\")\n", "for e in default_enzymes:\n", "    contain_enzyme = spacers[\"spacer\"].str.contains(RESTRICTION_ENZYMES[e])\n", "    spacers = spacers[~contain_enzyme].copy()\n", "spacers"]}, {"cell_type": "markdown", "id": "19f5e561", "metadata": {}, "source": ["Comparing with previous step, we now have 218 spacers, three spacers was filtered out because of containing restriction enzymes.\n", "\n", "Four or more consecutive T nucleotides in the spacer sequence may act as a transcriptional termination signal for the U6 promoter. Let's do it."]}, {"cell_type": "code", "execution_count": 12, "id": "e4162e4a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>gc_fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>17984939</td>\n", "      <td>17984959</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>CAGAGAGGCAGAGGCCATTA</td>\n", "      <td>0.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>17984983</td>\n", "      <td>17985003</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>AGAACTCAATCGCTATAAAA</td>\n", "      <td>0.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>17984987</td>\n", "      <td>17985007</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CTCAATCGCTATAAAATGGA</td>\n", "      <td>0.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>17984993</td>\n", "      <td>17985013</td>\n", "      <td>AGG</td>\n", "      <td>+</td>\n", "      <td>CGCTATAAAATGGACGGATG</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>17985048</td>\n", "      <td>17985068</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>CACGATACTAGCTAGCCCGA</td>\n", "      <td>0.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>248</th>\n", "      <td>17986169</td>\n", "      <td>17986189</td>\n", "      <td>AGG</td>\n", "      <td>-</td>\n", "      <td>GTCGCCGGCAGCGGCGGCAA</td>\n", "      <td>0.80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>249</th>\n", "      <td>17986175</td>\n", "      <td>17986195</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TTCGGAGTCGCCGGCAGCGG</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>250</th>\n", "      <td>17986178</td>\n", "      <td>17986198</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TCATTCGGAGTCGCCGGCAG</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>251</th>\n", "      <td>17986184</td>\n", "      <td>17986204</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GCGATCTCATTCGGAGTCGC</td>\n", "      <td>0.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>252</th>\n", "      <td>17986193</td>\n", "      <td>17986213</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TTGACACATGCGATCTCATT</td>\n", "      <td>0.40</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>218 rows × 6 columns</p>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer  gc_fraction\n", "0    17984939  17984959  TGG      +  CAGAGAGGCAGAGGCCATTA         0.55\n", "1    17984983  17985003  TGG      +  AGAACTCAATCGCTATAAAA         0.30\n", "2    17984987  17985007  CGG      +  CTCAATCGCTATAAAATGGA         0.35\n", "3    17984993  17985013  AGG      +  CGCTATAAAATGGACGGATG         0.45\n", "4    17985048  17985068  TGG      +  CACGATACTAGCTAGCCCGA         0.55\n", "..        ...       ...  ...    ...                   ...          ...\n", "248  17986169  17986189  AGG      -  GTCGCCGGCAGCGGCGGCAA         0.80\n", "249  17986175  17986195  CGG      -  TTCGGAGTCGCCGGCAGCGG         0.75\n", "250  17986178  17986198  CGG      -  TCATTCGGAGTCGCCGGCAG         0.65\n", "251  17986184  17986204  CGG      -  GCGATCTCATTCGGAGTCGC         0.60\n", "252  17986193  17986213  CGG      -  TTGACACATGCGATCTCATT         0.40\n", "\n", "[218 rows x 6 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["polyT = spacers[\"spacer\"].str.contains(\"TTTT\")\n", "spacers = spacers[~polyT].copy()\n", "spacers"]}, {"cell_type": "markdown", "id": "79115595", "metadata": {}, "source": ["It still 218 spacers left, so no spacer was filtered out because of consecutive T nucleotides.\n", "\n", "## Cut CDS\n", "\n", "We want cuttings sites of our gRNA design located within CDS. `SpCas9` has attribute named cut_sites. It is relevant cut site position on both positive strand and negative strand. Because of SpCas9' Double Stranded Break at same position on both strands, and PAM placed at 3' prime side, we need to add the negative cut sites (-3) to spacers' end for getting each cut sites. Then cut_CDS is a boolean series to test cut_site not greater than the CDS's end and greater than start."]}, {"cell_type": "code", "execution_count": 13, "id": "e9d6f5f7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>gc_fraction</th>\n", "      <th>cut_site</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>17984993</td>\n", "      <td>17985013</td>\n", "      <td>AGG</td>\n", "      <td>+</td>\n", "      <td>CGCTATAAAATGGACGGATG</td>\n", "      <td>0.45</td>\n", "      <td>17985010</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>17985048</td>\n", "      <td>17985068</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>CACGATACTAGCTAGCCCGA</td>\n", "      <td>0.55</td>\n", "      <td>17985065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>17985076</td>\n", "      <td>17985096</td>\n", "      <td>AGG</td>\n", "      <td>+</td>\n", "      <td>ACCTCCAGCGTTCACCGCGA</td>\n", "      <td>0.65</td>\n", "      <td>17985093</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>17985079</td>\n", "      <td>17985099</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>TCCAGCGTTCACCGCGAAGG</td>\n", "      <td>0.65</td>\n", "      <td>17985096</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>17985093</td>\n", "      <td>17985113</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>CGAAGGCGGCAGCGCCGCCA</td>\n", "      <td>0.80</td>\n", "      <td>17985110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>246</th>\n", "      <td>17986108</td>\n", "      <td>17986128</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TCCAGCCGCGACGCCATCTC</td>\n", "      <td>0.70</td>\n", "      <td>17986125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>247</th>\n", "      <td>17986160</td>\n", "      <td>17986180</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>AGCGGCGGCAAAGGTGAACC</td>\n", "      <td>0.65</td>\n", "      <td>17986177</td>\n", "    </tr>\n", "    <tr>\n", "      <th>248</th>\n", "      <td>17986169</td>\n", "      <td>17986189</td>\n", "      <td>AGG</td>\n", "      <td>-</td>\n", "      <td>GTCGCCGGCAGCGGCGGCAA</td>\n", "      <td>0.80</td>\n", "      <td>17986186</td>\n", "    </tr>\n", "    <tr>\n", "      <th>249</th>\n", "      <td>17986175</td>\n", "      <td>17986195</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TTCGGAGTCGCCGGCAGCGG</td>\n", "      <td>0.75</td>\n", "      <td>17986192</td>\n", "    </tr>\n", "    <tr>\n", "      <th>250</th>\n", "      <td>17986178</td>\n", "      <td>17986198</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TCATTCGGAGTCGCCGGCAG</td>\n", "      <td>0.65</td>\n", "      <td>17986195</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>211 rows × 7 columns</p>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer  gc_fraction  \\\n", "3    17984993  17985013  AGG      +  CGCTATAAAATGGACGGATG         0.45   \n", "4    17985048  17985068  TGG      +  CACGATACTAGCTAGCCCGA         0.55   \n", "5    17985076  17985096  AGG      +  ACCTCCAGCGTTCACCGCGA         0.65   \n", "6    17985079  17985099  CGG      +  TCCAGCGTTCACCGCGAAGG         0.65   \n", "7    17985093  17985113  TGG      +  CGAAGGCGGCAGCGCCGCCA         0.80   \n", "..        ...       ...  ...    ...                   ...          ...   \n", "246  17986108  17986128  CGG      -  TCCAGCCGCGACGCCATCTC         0.70   \n", "247  17986160  17986180  CGG      -  AGCGGCGGCAAAGGTGAACC         0.65   \n", "248  17986169  17986189  AGG      -  GTCGCCGGCAGCGGCGGCAA         0.80   \n", "249  17986175  17986195  CGG      -  TTCGGAGTCGCCGGCAGCGG         0.75   \n", "250  17986178  17986198  CGG      -  TCATTCGGAGTCGCCGGCAG         0.65   \n", "\n", "     cut_site  \n", "3    17985010  \n", "4    17985065  \n", "5    17985093  \n", "6    17985096  \n", "7    17985110  \n", "..        ...  \n", "246  17986125  \n", "247  17986177  \n", "248  17986186  \n", "249  17986192  \n", "250  17986195  \n", "\n", "[211 rows x 7 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["spacers[\"cut_site\"] = cut_site = spacers[\"end\"] + min(SpCas9.cut_sites)\n", "cut_CDS = ((cut_site <= end) & (cut_site >= start))\n", "spacers = spacers[cut_CDS].copy()\n", "spacers"]}, {"cell_type": "markdown", "id": "f376ef0d", "metadata": {}, "source": ["In this step, we finally get 211 spacers left, which means 7 spacers were filtered out because of cut sites.\n", "\n", "## Off target search\n", "\n", "Default minMatch option is 2 for nucleotide, it is too strict for our cases, because two tiles has 2 * 11 = 22 bp length, which is larger than spacer length and only one bp less than spacer + PAM (20 < 22 < 20 + 3). So, we set this option to 1.\n", "\n", "Default minScore option is 30, which would not possible for our cases, because the score is calculated as matches minus misMatches and sort of gap penalty. CRISPR protospacer is only 20 bp, any gap would let spacer very hard to match, so score in our study is roughly equal to matches - misMatches, as spacer length is only 20 bp, score would not greater than 20. We finally choose minScore as 18 (20 - 2).\n", "\n", "We should firstly look up number of unique spacers."]}, {"cell_type": "code", "execution_count": 14, "id": "525c4118", "metadata": {}, "outputs": [{"data": {"text/plain": ["208"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["len(list(spacers[\"spacer\"].unique()))"]}, {"cell_type": "markdown", "id": "01cb5ed2", "metadata": {}, "source": ["There are 208 unique spacers. We can figure out which ones are appearing more than once."]}, {"cell_type": "code", "execution_count": 15, "id": "f00b0c0f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>gc_fraction</th>\n", "      <th>cut_site</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>186</th>\n", "      <td>17985467</td>\n", "      <td>17985487</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>AACCGCGCGCGGCGACGGGA</td>\n", "      <td>0.8</td>\n", "      <td>17985484</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>17985471</td>\n", "      <td>17985491</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>GAGCAACCGCGCGCGGCGAC</td>\n", "      <td>0.8</td>\n", "      <td>17985488</td>\n", "    </tr>\n", "    <tr>\n", "      <th>189</th>\n", "      <td>17985472</td>\n", "      <td>17985492</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>CGAGCAACCGCGCGCGGCGA</td>\n", "      <td>0.8</td>\n", "      <td>17985489</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>17985514</td>\n", "      <td>17985534</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>AACCGCGCGCGGCGACGGGA</td>\n", "      <td>0.8</td>\n", "      <td>17985531</td>\n", "    </tr>\n", "    <tr>\n", "      <th>196</th>\n", "      <td>17985518</td>\n", "      <td>17985538</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>GAGCAACCGCGCGCGGCGAC</td>\n", "      <td>0.8</td>\n", "      <td>17985535</td>\n", "    </tr>\n", "    <tr>\n", "      <th>197</th>\n", "      <td>17985519</td>\n", "      <td>17985539</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>CGAGCAACCGCGCGCGGCGA</td>\n", "      <td>0.8</td>\n", "      <td>17985536</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer  gc_fraction  \\\n", "186  17985467  17985487  GGG      -  AACCGCGCGCGGCGACGGGA          0.8   \n", "188  17985471  17985491  GGG      -  GAGCAACCGCGCGCGGCGAC          0.8   \n", "189  17985472  17985492  CGG      -  CGAGCAACCGCGCGCGGCGA          0.8   \n", "194  17985514  17985534  GGG      -  AACCGCGCGCGGCGACGGGA          0.8   \n", "196  17985518  17985538  GGG      -  GAGCAACCGCGCGCGGCGAC          0.8   \n", "197  17985519  17985539  CGG      -  CGAGCAACCGCGCGCGGCGA          0.8   \n", "\n", "     cut_site  \n", "186  17985484  \n", "188  17985488  \n", "189  17985489  \n", "194  17985531  \n", "196  17985535  \n", "197  17985536  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["not_unique = spacers[\"spacer\"].value_counts()[spacers[\"spacer\"].value_counts() > 1].index\n", "spacers.query(\"spacer in @not_unique\")"]}, {"cell_type": "markdown", "id": "d8182368", "metadata": {}, "source": ["There are three spacers appear twice, they are AACCGCGCGCGGCGACGGGA, GAGCAACCGCGCGCGGCGAC and CGAGCAACCGCGCGCGGCGA. These three spacers are in the same region because they start is very close (17985472 - 17985467 = 5 bp).\n", "\n", "We can do blat search one by one. We choose -stepSize=5 (-titleSize=11 is default so we would not need set it) and -fine to make sure we can find all the matches (as BLAT FAQ suggests)."]}, {"cell_type": "code", "execution_count": 16, "id": "38c9bcd3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>matches</th>\n", "      <th>misMatches</th>\n", "      <th>repMatches</th>\n", "      <th>nCount</th>\n", "      <th>qNumInsert</th>\n", "      <th>qBaseInsert</th>\n", "      <th>tNumInsert</th>\n", "      <th>tBaseInsert</th>\n", "      <th>strand</th>\n", "      <th>qName</th>\n", "      <th>...</th>\n", "      <th>qStart</th>\n", "      <th>qEnd</th>\n", "      <th>tName</th>\n", "      <th>tSize</th>\n", "      <th>tStart</th>\n", "      <th>tEnd</th>\n", "      <th>blockCount</th>\n", "      <th>blockSizes</th>\n", "      <th>qStarts</th>\n", "      <th>tStarts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>+</td>\n", "      <td>AACCGCGCGCGGCGACGGGA</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr6</td>\n", "      <td>31248787</td>\n", "      <td>14630090</td>\n", "      <td>14630111</td>\n", "      <td>2</td>\n", "      <td>3,17,</td>\n", "      <td>0,3,</td>\n", "      <td>14630090,14630094,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>+</td>\n", "      <td>AACCGCGCGCGGCGACGGGA</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr8</td>\n", "      <td>28443022</td>\n", "      <td>24613158</td>\n", "      <td>24613179</td>\n", "      <td>2</td>\n", "      <td>3,17,</td>\n", "      <td>0,3,</td>\n", "      <td>24613158,24613162,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>AACCGCGCGCGGCGACGGGA</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr11</td>\n", "      <td>29021106</td>\n", "      <td>17985467</td>\n", "      <td>17985487</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>17985467,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>AACCGCGCGCGGCGACGGGA</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr11</td>\n", "      <td>29021106</td>\n", "      <td>17985514</td>\n", "      <td>17985534</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>17985514,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>-</td>\n", "      <td>AACCGCGCGCGGCGACGGGA</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr11</td>\n", "      <td>29021106</td>\n", "      <td>13908746</td>\n", "      <td>13908767</td>\n", "      <td>2</td>\n", "      <td>17,3,</td>\n", "      <td>0,17,</td>\n", "      <td>13908746,13908764,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>-</td>\n", "      <td>AACCGCGCGCGGCGACGGGA</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr3</td>\n", "      <td>36413819</td>\n", "      <td>6505423</td>\n", "      <td>6505444</td>\n", "      <td>2</td>\n", "      <td>17,3,</td>\n", "      <td>0,17,</td>\n", "      <td>6505423,6505441,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>19</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>AACCGCGCGCGGCGACGGGA</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr4</td>\n", "      <td>35502694</td>\n", "      <td>6881371</td>\n", "      <td>6881391</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>6881371,</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7 rows × 21 columns</p>\n", "</div>"], "text/plain": ["   matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", "0       20           0           0       0           0            0   \n", "1       20           0           0       0           0            0   \n", "2       20           0           0       0           0            0   \n", "3       20           0           0       0           0            0   \n", "4       20           0           0       0           0            0   \n", "5       20           0           0       0           0            0   \n", "6       19           1           0       0           0            0   \n", "\n", "   tNumInsert  tBaseInsert strand                 qName  ...  qStart  qEnd  \\\n", "0           1            1      +  AACCGCGCGCGGCGACGGGA  ...       0    20   \n", "1           1            1      +  AACCGCGCGCGGCGACGGGA  ...       0    20   \n", "2           0            0      -  AACCGCGCGCGGCGACGGGA  ...       0    20   \n", "3           0            0      -  AACCGCGCGCGGCGACGGGA  ...       0    20   \n", "4           1            1      -  AACCGCGCGCGGCGACGGGA  ...       0    20   \n", "5           1            1      -  AACCGCGCGCGGCGACGGGA  ...       0    20   \n", "6           0            0      -  AACCGCGCGCGGCGACGGGA  ...       0    20   \n", "\n", "   tName     tSize    tStart      tEnd  blockCount  blockSizes qStarts  \\\n", "0   Chr6  31248787  14630090  14630111           2       3,17,    0,3,   \n", "1   Chr8  28443022  24613158  24613179           2       3,17,    0,3,   \n", "2  Chr11  29021106  17985467  17985487           1         20,      0,   \n", "3  Chr11  29021106  17985514  17985534           1         20,      0,   \n", "4  Chr11  29021106  13908746  13908767           2       17,3,   0,17,   \n", "5   Chr3  36413819   6505423   6505444           2       17,3,   0,17,   \n", "6   Chr4  35502694   6881371   6881391           1         20,      0,   \n", "\n", "              tStarts  \n", "0  14630090,14630094,  \n", "1  24613158,24613162,  \n", "2           17985467,  \n", "3           17985514,  \n", "4  13908746,13908764,  \n", "5    6505423,6505441,  \n", "6            6881371,  \n", "\n", "[7 rows x 21 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["searches = blat(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\", \"AACCGCGCGCGGCGACGGGA\", minMatch=0, minScore=18, stepSize=5, fine=True)\n", "searches"]}, {"cell_type": "markdown", "id": "98457f14", "metadata": {}, "source": ["It found 7 matches, in these matches, there are two in spacers we previously designed. So we need calculate off-target score for the others.\n", "\n", "The left 5 matches are in two kinds, four matches has two blocks and one match has one block. All two-blocked matches length is 21 bp and two block sizes are 3 and 17, it means that one base insertion. So we consider the bigger block as major one. Let do CFD score calculation."]}, {"cell_type": "code", "execution_count": 17, "id": "e28b9ed5", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.392857143)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["from crisprprimer.score import cfd_score\n", "\n", "cfd_score(\"AACCGCGCGCGGCGACGGGA\", str(seqs[\"Chr6\"][14630090 + 1:14630111].seq), str(seqs[\"Chr6\"][14630111 + 1: 14630111 + 3].seq))"]}, {"cell_type": "markdown", "id": "cf08944f", "metadata": {}, "source": ["We often consider CFD score greater than 0.2 as off-target. So the AACCGCGCGCGGCGACGGGA spacer is not a good choice. Now we investigate the other two spacers."]}, {"cell_type": "code", "execution_count": 18, "id": "42a511bb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>matches</th>\n", "      <th>misMatches</th>\n", "      <th>repMatches</th>\n", "      <th>nCount</th>\n", "      <th>qNumInsert</th>\n", "      <th>qBaseInsert</th>\n", "      <th>tNumInsert</th>\n", "      <th>tBaseInsert</th>\n", "      <th>strand</th>\n", "      <th>qName</th>\n", "      <th>...</th>\n", "      <th>qStart</th>\n", "      <th>qEnd</th>\n", "      <th>tName</th>\n", "      <th>tSize</th>\n", "      <th>tStart</th>\n", "      <th>tEnd</th>\n", "      <th>blockCount</th>\n", "      <th>blockSizes</th>\n", "      <th>qStarts</th>\n", "      <th>tStarts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>GAGCAACCGCGCGCGGCGAC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr11</td>\n", "      <td>29021106</td>\n", "      <td>17985518</td>\n", "      <td>17985538</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>17985518,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>GAGCAACCGCGCGCGGCGAC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr11</td>\n", "      <td>29021106</td>\n", "      <td>17985471</td>\n", "      <td>17985491</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>17985471,</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2 rows × 21 columns</p>\n", "</div>"], "text/plain": ["   matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", "0       20           0           0       0           0            0   \n", "1       20           0           0       0           0            0   \n", "\n", "   tNumInsert  tBaseInsert strand                 qName  ...  qStart  qEnd  \\\n", "0           0            0      -  GAGCAACCGCGCGCGGCGAC  ...       0    20   \n", "1           0            0      -  GAGCAACCGCGCGCGGCGAC  ...       0    20   \n", "\n", "   tName     tSize    tStart      tEnd  blockCount  blockSizes qStarts  \\\n", "0  Chr11  29021106  17985518  17985538           1         20,      0,   \n", "1  Chr11  29021106  17985471  17985491           1         20,      0,   \n", "\n", "     tStarts  \n", "0  17985518,  \n", "1  17985471,  \n", "\n", "[2 rows x 21 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["blat(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\", \"GAGCAACCGCGCGCGGCGAC\", minMatch=0, minScore=18, stepSize=5, fine=True)"]}, {"cell_type": "markdown", "id": "a88c1c3e", "metadata": {}, "source": ["Spacer GAGCAACCGCGCGCGGCGAC got two matches, one is in the target region, the other is in the repeat region. It is good choice."]}, {"cell_type": "code", "execution_count": 19, "id": "5b34ee0c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>matches</th>\n", "      <th>misMatches</th>\n", "      <th>repMatches</th>\n", "      <th>nCount</th>\n", "      <th>qNumInsert</th>\n", "      <th>qBaseInsert</th>\n", "      <th>tNumInsert</th>\n", "      <th>tBaseInsert</th>\n", "      <th>strand</th>\n", "      <th>qName</th>\n", "      <th>...</th>\n", "      <th>qStart</th>\n", "      <th>qEnd</th>\n", "      <th>tName</th>\n", "      <th>tSize</th>\n", "      <th>tStart</th>\n", "      <th>tEnd</th>\n", "      <th>blockCount</th>\n", "      <th>blockSizes</th>\n", "      <th>qStarts</th>\n", "      <th>tStarts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>CGAGCAACCGCGCGCGGCGA</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr11</td>\n", "      <td>29021106</td>\n", "      <td>17985519</td>\n", "      <td>17985539</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>17985519,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>CGAGCAACCGCGCGCGGCGA</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr11</td>\n", "      <td>29021106</td>\n", "      <td>17985472</td>\n", "      <td>17985492</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>17985472,</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2 rows × 21 columns</p>\n", "</div>"], "text/plain": ["   matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", "0       20           0           0       0           0            0   \n", "1       20           0           0       0           0            0   \n", "\n", "   tNumInsert  tBaseInsert strand                 qName  ...  qStart  qEnd  \\\n", "0           0            0      -  CGAGCAACCGCGCGCGGCGA  ...       0    20   \n", "1           0            0      -  CGAGCAACCGCGCGCGGCGA  ...       0    20   \n", "\n", "   tName     tSize    tStart      tEnd  blockCount  blockSizes qStarts  \\\n", "0  Chr11  29021106  17985519  17985539           1         20,      0,   \n", "1  Chr11  29021106  17985472  17985492           1         20,      0,   \n", "\n", "     tStarts  \n", "0  17985519,  \n", "1  17985472,  \n", "\n", "[2 rows x 21 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["blat(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\", \"CGAGCAACCGCGCGCGGCGA\", minMatch=0, minScore=18, stepSize=5, fine=True)"]}, {"cell_type": "markdown", "id": "d54ee9a9", "metadata": {}, "source": ["Spacer CGAGCAACCGCGCGCGGCGA also has two matches, same as GAGCAACCGCGCGCGGCGAC.\n", "\n", "## Conclusion\n", "\n", "Since we finally got two spacers appear twice in our target region, they are considered as \"emphasized\". Therefore, I recommend GAGCAACCGCGCGCGGCGAC and CGAGCAACCGCGCGCGGCGA to knock out LOC_Os11g30910."]}], "metadata": {"jupytext": {"default_lexer": "ipython3", "formats": "ipynb,.pct.py:percent,.lgt.py:light,.spx.py:sphinx,md,Rmd,.pandoc.md:pandoc,.myst.md:myst"}, "kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 5}