import pytest

from crisprprimer.id_converter import convert


def test_id_converter():
    assert convert("LOC_Os12g16350.10") == "Os12g0264500"
    assert convert("LOC_Os02g13300") == "Os02g0226350"
    assert convert("LOC_Os10g17060.1") is None
    assert convert("Os01g0183666") is None
    assert convert("Os12g0264500") == [
        "LOC_Os12g16350.1",
        "LOC_Os12g16350.10",
        "LOC_Os12g16350.11",
        "LOC_Os12g16350.12",
        "LOC_Os12g16350.13",
        "LOC_Os12g16350.14",
        "LOC_Os12g16350.15",
        "LOC_Os12g16350.4",
        "LOC_Os12g16350.5",
        "LOC_Os12g16350.7",
        "LOC_Os12g16350.8",
        "LOC_Os12g16350.9",
    ]

    with pytest.raises(ValueError):
        convert("INVALID_ID")
