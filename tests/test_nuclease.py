import pytest
from Bio.Seq import Seq
from Bio.SeqRecord import <PERSON>qR<PERSON>ord
from Bio.SeqUtils import nt_search

from crisprprimer import Nuclease, Nucleases, get_pam


def test_nuclease_creation():
    # Test basic creation
    nuclease = Nuclease(cut_sites=-3, pam="NGG", spacer_range=(-20, 0))
    assert nuclease.pam == "NGG"
    assert nuclease.cut_sites == (-3, -3)
    assert nuclease.spacer_range == (-20, 0)
    assert nuclease.pam_side == "3prime"
    assert nuclease.spacer_length == 20


def test_nuclease_validation():
    # Test invalid PAM
    with pytest.raises(ValueError):
        Nuclease(cut_sites=-3, pam="", spacer_range=(-20, 0))

    # Test invalid spacer range
    with pytest.raises(ValueError):
        Nuclease(cut_sites=-3, pam="NGG", spacer_range=(20, 0))

    # Test single cut site conversion
    nuclease = Nuclease(cut_sites=5, pam="NGG", spacer_range=(0, 20))
    assert nuclease.cut_sites == (5, 5)


def test_nuclease_prototype():
    # Test 3' PAM prototype
    spcas9 = Nuclease(cut_sites=-3, pam="NGG", spacer_range=(-20, 0))
    assert spcas9.prototype == "N" * 20 + "NGG"

    # Test 5' PAM prototype
    cas12 = Nuclease(cut_sites=22, pam="TTTV", spacer_range=(4, 27))
    assert cas12.prototype == "TTTV" + "N" * 23


def test_find_spacers():
    # Test SpCas9 spacers
    spcas9 = Nuclease(cut_sites=-3, pam="NGG", spacer_range=(-20, 0))
    spacers = spcas9.find_spacers_on(Seq("A" * 20 + "GGG"))
    assert len(spacers) == 1
    spacer = spacers.iloc[0]
    assert spacer["start"] == 0
    assert spacer["end"] == 20
    assert spacer["pam"] == "GGG"
    assert spacer["spacer"] == "A" * 20
    assert spacer["strand"] == "+"

    # Test reverse strand
    spacers = spcas9.find_spacers_on(Seq("CCC" + "T" * 20))
    assert len(spacers) == 1
    spacer = spacers.iloc[0]
    assert spacer["strand"] == "-"
    assert spacer["spacer"] == "A" * 20  # Should be reverse complemented

    # Test no matches
    spacers = spcas9.find_spacers_on(Seq("A" * 30))
    assert len(spacers) == 0


def test_nucleases_enum():
    assert Nucleases.SpCas9.model.pam == "NGG"
    assert Nucleases.AsCas12a.model.pam == "TTTV"
    assert Nucleases.SpCas9.value == "SpCas9"


def test_get_pam():
    # Mock data for testing
    mock_row = {"tName": "chr1", "tStart": 80, "tEnd": 100, "strand": "+", "blockCount": 1}
    mock_seqs = {"chr1": SeqRecord(Seq("A" * 100 + "GGGCC" + "A" * 100))}

    # Test 3' PAM on positive strand
    pam = get_pam(mock_row, mock_seqs, Nucleases.SpCas9.model)
    assert len(nt_search(pam, "NGG")) == 2

    # Test 3' PAM on negative strand
    mock_row = {"tName": "chr1", "tStart": 106, "tEnd": 126, "strand": "-", "blockCount": 1}
    pam = get_pam(mock_row, mock_seqs, Nucleases.SpCas9.model)
    assert len(nt_search(pam, "NGG")) == 2

    mock_row = {"tName": "chr1", "tStart": 81, "tEnd": 104, "strand": "-", "blockCount": 1}
    pam = get_pam(mock_row, mock_seqs, Nucleases.AsCas12a.model)
    assert len(nt_search(pam, "TTTV")) == 2
