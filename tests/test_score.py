import pandas as pd
import pytest

from crisprprimer.score import cfd_score, distance_to_cds_start_score, length_score, pair_score


def test_cfd_score():
    # Test perfect match
    assert cfd_score("A" * 20, "A" * 20, "GG") == 1.0

    # Test mismatch
    assert cfd_score("A" * 20, "T" * 20, "GG") < 1.0

    # Test different PAM
    assert cfd_score("A" * 20, "A" * 20, "AG") < 1.0

    # Test invalid lengths
    with pytest.raises(AssertionError):
        cfd_score("A" * 19, "A" * 20, "GG")
    with pytest.raises(AssertionError):
        cfd_score("A" * 20, "A" * 19, "GG")
    with pytest.raises(AssertionError):
        cfd_score("A" * 20, "A" * 20, "G")


def test_distance_to_cds_start_score():
    # Create test CDS data
    cds = pd.DataFrame({"start": [100], "end": [200], "strand": ["+"]})

    # Test covering all CDS
    assert distance_to_cds_start_score(cds, 90, 210) == 1.0

    # Test partial overlap
    assert distance_to_cds_start_score(cds, 90, 150) == 0.5

    # Test near start
    assert distance_to_cds_start_score(cds, 90, 110) == 0.5

    # Test far from start
    assert distance_to_cds_start_score(cds, 150, 170) == 0.5

    # Test very short CDS
    short_cds = pd.DataFrame({"start": [100], "end": [105], "strand": ["+"]})
    assert distance_to_cds_start_score(short_cds, 100, 105) == 1.0


def test_length_score():
    # Test ideal length
    assert length_score(100, 166) == 1.0  # 60 + (3*2) = 66, 100+66=166, close enough

    # Test too short
    assert length_score(100, 103) == 0.0

    # Test slightly off
    assert length_score(100, 150) == 0.5

    # Test with different dist
    assert length_score(100, 168, dist=4) == 1.0  # 60 + (4*2) = 68, 100+68=168


def test_pair_score():
    cds = pd.DataFrame({"start": [100], "end": [200], "strand": ["+"]})

    row = pd.Series({"start": 90, "end": 170})

    # Test with mock data
    score = pair_score(row, cds, dist=3)
    assert 0 <= score <= 1.0
