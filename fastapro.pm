#! /usr/bin/perl

#author: xcg
# revised from lookseq's code
# added some functions

package fastapro;
require Exporter;
our @ISA = qw(Exporter);
our @EXPORT =
  qw(get_chromosome_part get_chromosome_list write_genome_part getNucEx);

use strict;
use POSIX qw(ceil floor);

#_________________________________________

# Read the sequence of a chromosome from a FASTA file
my @chromosomes;

sub get_chromosome_from_genome_file {
    my $genome_file = shift;
    my $chr         = shift;
    open GENOME, $genome_file or return "";
    my $s    = "";
    my $lchr = "";
    while (<GENOME>) {    # For each line of input file
        if ( $_ =~ /^>/ )
        { # If it starts with an ">", it indicates the end of a chromosome and the beginnig of a new one...
            return $s                   if $lchr eq $chr;
            push( @chromosomes, $lchr ) if $lchr ne '';

            $s = "";
            chomp;
            $lchr = substr( $_, 1 );
            $lchr =~ s/^\s+//;
            $lchr =~ s/\s+$//;
            $lchr =~ /^([\S+]+)/;
            $lchr = $1;

            #	    $lchr =~ s/[^A-Za-z0-9]/_/g ;
            $chr = $lchr if $chr eq "";

        }
        else {    # Otherwise, DNA
            chomp;
            $s .= $_;
        }
    }

    return $s                   if $lchr eq $chr;
    push( @chromosomes, $lchr ) if $lchr ne '';
    return "";
}

# Cut out the interesting part
sub get_chromosome_part {
    my ( $file, $chromosome, $from, $to ) = @_;
    my $sampath = "";

    if ( -e "$file.fai" ) {
        $chromosome =~ m/^([\S]+)/;
        my $ch = $1;
        my $cmd;
        if ( defined($to) ) {
            $cmd = "$sampath" . "samtools faidx $file $ch:$from-$to |";
        }
        elsif ( defined($from) ) {
            $cmd = "$sampath" . "samtools faidx $file $ch:$from |";
        }
        elsif ( defined($chromosome) ) {
            $cmd = "$sampath" . "samtools faidx $file $ch |";
        }

        open FILE, $cmd;
        my $s;
        while (<FILE>) {
            next if $_ =~ /^>/;
            chomp;
            $s .= $_;
        }
        close FILE;
        return $s;
    }

    # Default / fallback
    my $s = get_chromosome_from_genome_file( $file, $chromosome );
    return $s if $s eq '';
    if ( !defined($from) ) {
        $from = 1;
    }
    if ( !defined($to) ) {
        $to = length($s);
    }
    $s = substr $s, $from - 1, $to - $from + 1;
    return $s;
}

sub get_chromosome_list {
    my $reference_fa = shift;
    my $fai_file     = $reference_fa . ".fai";
    my @chlist;

    if ( -e "$fai_file" ) {    # Use SAMTOOLS .fa file

        open FILE, $fai_file or die("$fai_file: $!");
        while (<FILE>) {
            die "Unexpected format of $reference_fa: $_\n"
              if $_ !~ /^(\S+)\s+(\d+)\s+/;
            my ( $chrom, $length ) = ( $1, $2 );

            #	next if $chrom !~ /^(?:\d+|x|y)$/i ;
            #	$chromosomes{$chrom} = $length;
            push( @chlist, $1 );
        }
        close FILE;
        return @chlist;

    }
    else {    # Use "traditional" fasta
        get_chromosome_from_genome_file( $reference_fa, "____NO__Impossilbe_" );
        return @chromosomes;
    }
}

sub write_genome_part {
    my $CSIZE = 60;
    my ( $seq_fh, $id, $seqh ) = @_;
    my $seqstr = $$seqh;
    if ( $seq_fh == 0 ) {
        $seq_fh = *STDOUT;
    }
    print $seq_fh ">", $id, "\n";
    my $i;
    for ( $i = 0 ; $i <= floor( length($seqstr) / $CSIZE ) - 1 ; $i++ ) {
        print $seq_fh substr( $seqstr, $i * $CSIZE, $CSIZE ), "\n";
    }
    if ( length($seqstr) % $CSIZE != 0 ) {
        print $seq_fh substr( $seqstr, $i * $CSIZE ), "\n";
    }
}

my $oldfile;
my $oldchid;
my $myseqstr;

#using global variables:
sub getNucEx {
    my ( $chrall_file, $chid, $pos_t, $len_t ) = @_;
    $len_t = 1 if ( !defined($len_t) );
    return if ( $len_t <= 0 );

    if ( $chid ne $oldchid || $oldfile ne $chrall_file ) {
        $myseqstr = get_chromosome_part( $chrall_file, $chid );
        $oldfile  = $chrall_file;
        $oldchid  = $chid;
    }
    if ( $pos_t + $len_t - 1 > length($myseqstr) ) {
        return
          substr(
            $myseqstr . ( 'N' x ( $pos_t - length($myseqstr) + $len_t - 1 ) ),
            $pos_t - 1, $len_t );
    }
    return substr( $myseqstr, $pos_t - 1, $len_t );
}

1;
