{"AaaI": "CGGCCG", "AacLI": "GGATCC", "AaeI": "GGATCC", "AagI": "ATCGAT", "M.Aam10684I": "GANTC", "Aam10684II": "ACCGAG", "AanI": "TTATAA", "M.Aap5906II": "GATC", "M.Aap5908I": "GATC", "M.Aap10433II": "GATC", "AaqI": "GTGCAC", "AarI": "CACCTGC", "AasI": "GACNNNNNNGTC", "AatI": "AGGCCT", "AatII": "GACGTC", "AauI": "TGTACA", "M.Aav11297II": "GATC", "AbaI": "TGATCA", "Aba0088II": "GNGCAAC", "Aba6411II": "CRRTAAG", "Aba7804II": "GNGCAAC", "Aba12156II": "CTATCAV", "Aba13301I": "GCAAAC", "AbaAI": "C", "AbaB8342IV": "CATTAG", "AbaBGI": "C", "AbaCI": "C", "AbaCIII": "CTATCAV", "AbaDI": "C", "AbaHI": "C", "AbaSI": "C", "AbaTI": "C", "AbaUI": "C", "AbaUMB2I": "YCCGSS", "AbrI": "CTCGAG", "Abr4036II": "GRTYGACC", "Abr4039II": "TGGCCA", "AbsI": "CCTCGAGG", "AcaI": "TTCGAA", "AcaII": "GGATCC", "AcaIII": "TGCGCA", "AcaIV": "GGCC", "M.Aca72Dam": "GATC", "M.Aca2539Dam": "GATC", "M.Aca12244Dam": "GATC", "AcaPI": "C", "M.AcaR252Dam": "GATC", "M.AcaR256Dam": "GATC", "AccI": "GTMKAC", "AccII": "CGCG", "AccIII": "TCCGGA", "AccVIII": "CTGAAG", "AccIX": "GACRAC", "AccX": "GGARCA", "Acc16I": "TGCGCA", "Acc36I": "ACCTGC", "Acc38I": "CCWGG", "Acc65I": "GGTACC", "Acc65V": "GACGCA", "Acc113I": "AGTACT", "AccB1I": "GGYRCC", "AccB2I": "RGCGCY", "AccB7I": "CCANNNNNTGG", "AccEBI": "GGATCC", "AceI": "GCWGC", "AceII": "GCTAGC", "AceIII": "CAGCTC", "AchA6III": "AGCCAG", "AclI": "AACGTT", "AclNI": "ACTAGT", "AclWI": "GGATC", "AcoI": "YGGCCR", "Aco12261II": "CCRGAG", "AcoY31II": "TAGCRAB", "AcpI": "TTCGAA", "AcpII": "CCANNNNNTGG", "AcrI": "CYCGRG", "AcrII": "GGTNACC", "M.Acr10283VI": "GATC", "AcsI": "RAATTY", "Acs1371I": "GTCGAC", "Acs1372I": "GTCGAC", "Acs1373I": "GTCGAC", "Acs1421I": "GTCGAC", "Acs1422I": "GTCGAC", "AcuI": "CTGAAG", "AcuII": "CCWGG", "AcvI": "CACGTG", "AcyI": "GRCGYC", "AdeI": "CACNNNGTG", "Adh6U21I": "GAANCAG", "AehI": "CTCGAG", "AehII": "AGGCCT", "M.Aen12917Dam": "GATC", "AerAI": "CTCGAG", "AeuI": "CCWGG", "AfaI": "GTAC", "Afa22MI": "CGATCG", "Afa16RI": "CGATCG", "Afa24RI": "GCCGGC", "AfeI": "AGCGCT", "AfiI": "CCNNNNNNNGG", "AflI": "GGWCC", "AflII": "CTTAAG", "AflIII": "ACRYGT", "AflIV": "AGTACT", "Afl83I": "TTCGAA", "Afl83II": "GGCC", "M.Aga11188VI": "GATC", "AgeI": "ACCGGT", "AglI": "CCWGG", "AgsI": "TTSAA", "AhaI": "CCSGG", "AhaII": "GRCGYC", "AhaIII": "TTTAAA", "Aha10306III": "GTAGAT", "AhaB1I": "GGNCC", "AhaB8I": "GGTACC", "AhdI": "GACNNNNNGTC", "M.AhdDam": "GATC", "M.Ahe12129Dam": "GATC", "AhlI": "ACTAGT", "AhyI": "CCCGGG", "AhyAI": "CTCGAG", "M.Ahy8049Dam": "GATC", "AhyRBAHI": "GCYYGAC", "M.AhyRBAHDam": "GATC", "M.Ahy1R2Dam": "GATC", "M.AhySSUDam": "GATC", "AhyYL17I": "YAAMGAG", "AinI": "CTGCAG", "AinII": "GGATCC", "AitI": "AGCGCT", "AitII": "RGATCY", "AitAI": "RGATCY", "AjnI": "CCWGG", "AjoI": "CTGCAG", "Aju10307I": "GACGCA", "Aju12153I": "GACGCA", "Ala17612I": "GCWGC", "AleI": "CACNNNNGTG", "AliI": "GGATCC", "Ali2882I": "CTGCAG", "Ali12257I": "GGATCC", "Ali12258I": "GGATCC", "AliAJI": "CTGCAG", "AluI": "AGCT", "AluBI": "AGCT", "AlwI": "GGATC", "Alw21I": "GWGCWC", "Alw26I": "GTCTC", "Alw44I": "GTGCAC", "AlwFI": "GAAAYNNNNNRTG", "AlwFII": "CTCGAG", "AlwNI": "CAGNNNCTG", "AlwXI": "GCAGC", "Ama87I": "CYCGRG", "AmaCSI": "GCTCCA", "AmaSI": "TCGCGA", "AmeI": "GTGCAC", "AmeII": "GCCGGC", "AniMI": "GCCGGC", "AocI": "CCTNAGG", "AocII": "GDGCHC", "Aod1I": "GATCNAC", "AorI": "CCWGG", "Aor13HI": "TCCGGA", "Aor51HI": "AGCGCT", "AosI": "TGCGCA", "AosII": "GRCGYC", "AosIII": "CCGCGG", "AoxI": "GGCC", "ApaI": "GGGCCC", "ApaBI": "GCANNNNNTGC", "ApaCI": "GGATCC", "ApaLI": "GTGCAC", "ApaORI": "CCWGG", "ApcTR183I": "TGCGCA", "ApeI": "ACGCGT", "ApeAI": "GCCGGC", "ApeKI": "GCWGC", "M.Aph11096III": "GATC", "ApiI": "CTGCAG", "AplI": "CTGCAG", "M.Apl10976II": "GATC", "ApoI": "RAATTY", "AprI": "GCCGGC", "ApuI": "GGNCC", "Apu16I": "ATCGAT", "ApyI": "CCWGG", "ApyPI": "ATCGAC", "AquI": "CYCGRG", "AquII": "GCCGNAC", "AquIII": "GAGGAG", "AquIV": "GRGGAAG", "M.AquDam": "GATC", "M.Ari11N1Dam": "GATC", "M.Asa10402Dam": "GATC", "M.Asa12959Dam": "GATC", "AscI": "GGCGCGCC", "AseI": "ATTAAT", "AseII": "CCSGG", "AsiI": "GGATCC", "Asi256I": "GATC", "Asi372I": "ATGCAT", "AsiAI": "ACCGGT", "AsiGI": "ACCGGT", "AsiSI": "GCGATCGC", "Asl11923II": "GGGABCC", "AsnI": "ATTAAT", "AspI": "GACNNNGTC", "Asp1I": "CCSGG", "Asp14I": "ATCGAT", "Asp15I": "CTCGAG", "Asp17I": "RGATCY", "Asp22I": "RGATCY", "Asp36I": "CTGCAG", "Asp37I": "ATCGAT", "Asp47I": "CTCGAG", "Asp52I": "AAGCTT", "Asp78I": "AGGCCT", "Asp86I": "ATCGAT", "Asp90I": "ACRYGT", "Asp103I": "CGRAGGC", "Asp123I": "ATCGAT", "Asp130I": "ATCGAT", "M.Asp199I": "GATC", "Asp337I": "CARABGG", "Asp697I": "GGWCC", "Asp700I": "GAANNNNTTC", "Asp703I": "CTCGAG", "Asp707I": "ATCGAT", "Asp708I": "CTGCAG", "Asp713I": "CTGCAG", "Asp718I": "GGTACC", "Asp742I": "GGCC", "Asp745I": "GGWCC", "Asp748I": "CCGG", "Asp763I": "AGTACT", "Asp3065I": "AAGCTT", "AspAI": "GGTNACC", "AspA2I": "CCTAGG", "AspAMDIV": "ACCCAC", "AspBI": "CYCGRG", "AspBII": "GGWCC", "AspBHI": "YSCNS", "AspBHII": "RGGWCCY", "AspCNI": "GCCGC", "AspDI": "CYCGRG", "AspDII": "GGWCC", "AspDUT2V": "GNGCAAC", "AspEI": "GACNNNNNGTC", "AspHI": "GWGCWC", "Asp1HI": "RGATCY", "Asp2HI": "CCWGG", "Asp5HI": "GCATGC", "Asp6HI": "RGATCY", "Asp8HI": "RGATCY", "Asp10HI": "TTCGAA", "Asp10HII": "CCANNNNNTGG", "Asp14HI": "RGATCY", "Asp16HI": "GTAC", "Asp17HI": "GTAC", "Asp18HI": "GTAC", "Asp21HI": "RGATCY", "Asp29HI": "GTAC", "Asp32HI": "CCGCGG", "AspJI": "GACGTC", "AspJHL3II": "CGCCCAG", "AspLEI": "GCGC", "AspMI": "AGGCCT", "AspMDI": "GATC", "AspNI": "GGNNCC", "AspNIH4III": "AAGAACB", "M.AspNIH1Dam": "GATC", "M.AspNIH2Dam": "GATC", "M.AspNIH3Dam": "GATC", "M.AspNIH4Dam": "GATC", "M.AspNIH5Dam": "GATC", "M.AspNIH7Dam": "GATC", "M.AspRAC06II": "GANTC", "AspS9I": "GGNCC", "AspSLV7III": "GTCTCA", "AspTI": "CTGCAG", "AspTII": "GGATCC", "AspTIII": "GGCC", "AspTB23I": "GCWGC", "Asp114pII": "AGCABCC", "AssI": "AGTACT", "AstWI": "GRCGYC", "AsuI": "GGNCC", "AsuII": "TTCGAA", "AsuIII": "GRCGYC", "Asu14238II": "GCCNNNNNNTCC", "Asu14238IV": "CGTRAC", "AsuC2I": "CCSGG", "AsuHPI": "GGTGA", "AsuMBI": "GATC", "AsuNHI": "GCTAGC", "AsuSAI": "CCTNAGG", "AteI": "CCATGG", "AteTI": "GGGRAG", "AtsI": "GACNNNGTC", "AtuII": "CCWGG", "Atu1I": "CCWGG", "Atu1II": "GGATCC", "M.Atu13543I": "GANTC", "AtuBI": "CCWGG", "M.AtuCI": "GANTC", "AtuSI": "TGATCA", "AvaI": "CYCGRG", "AvaII": "GGWCC", "AvaIII": "ATGCAT", "Ava458I": "YGGCCR", "AvcI": "GGNCC", "AviI": "TTCGAA", "AviII": "TGCGCA", "AvoI": "RCATGY", "M.Avo3438I": "GATC", "AvrI": "CYCGRG", "AvrII": "CCTAGG", "AvrBI": "GGCC", "AvrBII": "CCTAGG", "Awo1030IV": "GCCRAG", "AxyI": "CCTNAGG", "M.BabI": "GANTC", "BacI": "CCGCGG", "Bac36I": "GGNCC", "Bac465I": "CCGCGG", "BadI": "CTCGAG", "BaeGI": "GKGCMC", "Bag18758I": "CCCGAG", "BalI": "TGGCCA", "Bal228I": "GGNCC", "Bal475I": "GGCC", "Bal3006I": "GGCC", "BamFI": "GGATCC", "BamGI": "CAGCTG", "BamHI": "GGATCC", "M.BamHII": "GGATCC", "BamKI": "GGATCC", "BamNI": "GGATCC", "BamNII": "GGWCC", "BanI": "GGYRCC", "BanII": "GRGCYC", "BanIII": "ATCGAT", "Ban4602I": "GACGAG", "BanAI": "GGCC", "BanLI": "RTCAGG", "BasI": "CCANNNNNTGG", "Bau1417V": "GTTCAG", "Bau1419II": "ACRGAG", "BavI": "CAGCTG", "BavAI": "CAGCTG", "BavAII": "GGNCC", "BavBI": "CAGCTG", "BavBII": "GGNCC", "BavCI": "ATCGAT", "BazI": "ATCGAT", "Bba179I": "WCCGGW", "BbeI": "GGCGCC", "BbeAI": "GGCGCC", "BbfI": "CTCGAG", "Bbf7411I": "TCCGGA", "BbiI": "CTGCAG", "BbiII": "GRCGYC", "BbiIII": "CTCGAG", "Bbi24I": "ACGCGT", "BbiDI": "C", "BbrI": "AAGCTT", "Bbr02I": "GATC", "Bbr7I": "GAAGAC", "Bbr09I": "RGATCY", "Bbr11I": "GGRCAG", "Bbr51I": "GGCGAG", "Bbr52I": "RGATCY", "Bbr52II": "GGCGAG", "Bbr57II": "GAGGAC", "Bbr57III": "GTRAAYG", "Bbr215I": "GGCGAG", "Bbr215II": "GAGGAC", "Bbr7017II": "CGGGAG", "Bbr7017III": "GGRCAG", "BbrAI": "AAGCTT", "BbrPI": "CACGTG", "R1.BbrUI": "GGCGCC", "BbrUII": "GTCGAC", "BbrUIII": "CTGCAG", "BbsI": "GAAGAC", "BbtI": "GCGC", "BbuI": "GCATGC", "M.Bbu297I": "CCWGG", "BbuB31I": "GNAAYG", "BbuB31II": "CGRKA", "BbuNRZI": "CGRKA", "BbvI": "GCAGC", "BbvII": "GAAGAC", "Bbv12I": "GWGCWC", "Bbv16II": "GAAGAC", "BbvAI": "GAANNNNTTC", "BbvAII": "ATCGAT", "BbvAIII": "TCCGGA", "BbvBI": "GGYRCC", "M.BbvSI": "GCWGC", "BcaI": "GCGC", "Bca77I": "WCCGGW", "M.Bca420II": "GANTC", "Bca1259I": "GGATCC", "BccI": "CCATC", "Bce4I": "GCNNNNNNNGC", "Bce22I": "GGNCC", "Bce71I": "GGCC", "Bce83I": "CTTGAG", "Bce95I": "GCNGC", "Bce170I": "CTGCAG", "Bce243I": "GATC", "Bce751I": "GGATCC", "Bce1247I": "GCNNNNNNNGC", "Bce1273I": "GCNGC", "Bce3081I": "TAGGAG", "Bce14579I": "GCWGC", "Bce31293I": "CGCG", "BceAI": "ACGGC", "BceBI": "CGCG", "BceCI": "GCNNNNNNNGC", "BceDI": "TGATCA", "BceJI": "CACAG", "BceLI": "GCNGC", "BceRI": "CGCG", "BceSI": "CGAAG", "BceSII": "GGWCC", "BceSIII": "ACGGC", "BceYI": "GCNGC", "BcefI": "ACGGC", "BchI": "GCAGC", "Bci29I": "ATCGAT", "Bci528I": "GAATTC", "BciBI": "ATCGAT", "BciBII": "CCWGG", "BciT130I": "CCWGG", "BciVI": "GTATCC", "BclI": "TGATCA", "BcmI": "ATCGAT", "BcnI": "CCSGG", "BcoI": "CYCGRG", "Bco5I": "CTCTTC", "Bco6I": "TGCGCA", "Bco27I": "CCGG", "Bco33I": "GGCC", "Bco35I": "CTGGAG", "Bco63I": "GATNNNNATC", "Bco79I": "ATCGAT", "Bco102I": "TGATCA", "Bco102II": "GAAGAC", "Bco116I": "CTCTTC", "Bco118I": "RCCGGY", "Bco163I": "CTRYAG", "Bco631I": "GATNNNNATC", "Bco10278I": "GGATCC", "BcoAI": "CACGTG", "BcoDI": "GTCTC", "BcoKI": "CTCTTC", "BcoSI": "CTCTTC", "BcrI": "GGNNCC", "BcrAI": "CTCTTC", "BctI": "ACGGC", "BcuI": "ACTAGT", "BcuAI": "GGWCC", "BdiI": "ATCGAT", "BdiSI": "CTRYAG", "BecAII": "GGCC", "BepI": "CGCG", "BetI": "WCCGGW", "BfaI": "CTAG", "BfaSII": "GANGGAG", "BfiI": "ACTGGG", "Bfi57I": "GATC", "Bfi89I": "YGGCCR", "Bfi105I": "GGNCC", "Bfi458I": "GGCC", "BfiSHI": "GATC", "BflI": "CCNNNNNNNGG", "BfmI": "CTRYAG", "BfoI": "RGCGCY", "BfrI": "CTTAAG", "Bfr9343III": "GANGGAG", "BfrAI": "ATCGAT", "BfrBI": "ATGCAT", "BfrCI": "ATGCAT", "Bfr9343DIII": "GANGGAG", "BfuI": "GTATCC", "Bfu1570I": "GWGCWC", "BfuAI": "ACCTGC", "BfuAII": "GCATGC", "BfuCI": "GATC", "Bga514I": "GTRAAG", "BgiI": "GACNNNGTC", "BglI": "GCCNNNNNGGC", "BglII": "AGATCT", "Bgl2196IV": "GGAGGC", "BhaI": "GCATC", "BhaII": "GGCC", "BheI": "GCCGGC", "BimI": "TTCGAA", "Bim19I": "TTCGAA", "Bim19II": "GGCC", "BinI": "GGATC", "BinSI": "CCWGG", "BinSII": "GGCGCC", "BisI": "GCNGC", "Bka1125I": "GDGCHC", "BkrAM31DI": "RTTAAATM", "Bla7920I": "TCCGGA", "Ble401II": "GRAGCAG", "Ble402II": "GRAGCAG", "BlfI": "TCCGGA", "BliI": "GGCC", "Bli41I": "ATCGAT", "Bli49I": "GGTCTC", "Bli86I": "ATCGAT", "Bli161I": "GGTCTC", "Bli576I": "ATCGAT", "Bli576II": "GGTCTC", "Bli585I": "ATCGAT", "Bli643I": "CCTNAGG", "Bli736I": "GGTCTC", "Bli1904II": "GCNGC", "Bli5508I": "GGTCTC", "BliAI": "ATCGAT", "BliHKI": "CCTNAGG", "BliRI": "ATCGAT", "BlnI": "CCTAGG", "BloAII": "GAGGAC", "BloHI": "RGATCY", "BloHII": "CTGCAG", "BloHIII": "CTGCAG", "BloMGI": "CCGCGG", "BlpI": "GCTNAGC", "BlsI": "GCNGC", "BluI": "CTCGAG", "BluII": "GGCC", "BmaI": "CGATCG", "BmaAI": "CGATCG", "BmaBI": "CGATCG", "BmaCI": "CGATCG", "BmaDI": "CGATCG", "BmcAI": "AGTACT", "Bme05I": "GGYRCC", "Bme12I": "GATC", "Bme18I": "GGWCC", "Bme46I": "GGCC", "Bme74I": "GGCC", "Bme142I": "RGCGCY", "Bme216I": "GGWCC", "Bme361I": "GGCC", "Bme585I": "CCCGC", "Bme1390I": "CCNGG", "Bme1580I": "GKGCMC", "Bme2095I": "CCWGG", "Bme2494I": "GATC", "BmeBI": "CTGCAG", "BmeDI": "C", "BmeRI": "GACNNNNNGTC", "BmeTI": "TGATCA", "BmeT110I": "CYCGRG", "BmeU1594I": "GGCC", "BmgI": "GKGCCC", "BmgAI": "GKGCMC", "BmgT120I": "GGNCC", "BmiI": "GGNNCC", "BmpI": "GGWCC", "BmrI": "ACTGGG", "BmrFI": "CCNGG", "BmsI": "GCATC", "BmtI": "GCTAGC", "BmuI": "ACTGGG", "BmyI": "GDGCHC", "BnaI": "GGATCC", "BniI": "GCNNGC", "BoxI": "GACNNNNGTC", "Bpa34I": "AGTACT", "Bpa36I": "GGCC", "Bpa36II": "CTNAG", "BpcI": "CTRYAG", "BpeI": "AAGCTT", "BpiI": "GAAGAC", "BpmI": "CTGGAG", "BpoAI": "ATTAAT", "BpsI": "GGNCC", "BpsII": "ATCGAT", "BptI": "CCWGG", "BpuI": "GRGCYC", "Bpu14I": "TTCGAA", "Bpu86I": "GCCNNNNNGGC", "Bpu95I": "CGCG", "Bpu1102I": "GCTNAGC", "Bpu1268I": "CCTNNNNNAGG", "Bpu1811I": "GCNGC", "Bpu1831I": "TACGTA", "BpuAI": "GAAGAC", "BpuAmI": "GAGCTC", "BpuB5I": "CGTACG", "BpuCI": "GGCGGA", "BpuEI": "CTTGAG", "BpuFI": "GGATC", "BpuGI": "RGATCY", "BpuGCI": "GCTNAGC", "BpuHI": "TTCGAA", "BpuJI": "CCCGT", "BpuMI": "CCSGG", "BpuNI": "GGGAC", "BpuSI": "GGGAC", "BpvUI": "CGATCG", "BsaI": "GGTCTC", "Bsa29I": "ATCGAT", "BsaAI": "YACGTR", "BsaBI": "GATNNNNATC", "BsaCI": "CCNGG", "BsaDI": "GGATCC", "BsaEI": "GGNNCC", "BsaFI": "CTTAAG", "BsaGI": "GWGCWC", "BsaHI": "GRCGYC", "BsaJI": "CCNNGG", "BsaKI": "GTTAAC", "BsaLI": "AGCT", "BsaNI": "CCWGG", "BsaNII": "CTGCAG", "BsaOI": "CGRYCG", "BsaPI": "GATC", "BsaQI": "CTGCAG", "BsaRI": "GGCC", "BsaSI": "GGNCC", "BsaTI": "TGCGCA", "BsaUI": "GCAGC", "BsaVI": "GAAGAC", "BsaWI": "WCCGGW", "BsaZI": "CCGG", "BsbI": "CAACAC", "BscI": "ATCGAT", "Bsc4I": "CCNNNNNNNGG", "Bsc91I": "GAAGAC", "Bsc107I": "CCNNNNNNNGG", "Bsc217I": "GATATC", "BscAI": "GCATC", "BscBI": "GGNNCC", "BscDI": "CTGCAG", "BscEI": "GCGCGC", "BscFI": "GATC", "BscGI": "CCCGT", "BscHI": "ACTGG", "BscJI": "CCANNNNNNTGG", "BscKI": "GAAGAC", "BscLI": "CTTAAG", "BscMI": "GRGCYC", "BscNI": "CGRYCG", "BscOI": "GCATGC", "BscPI": "CTNAG", "BscQI": "GGCC", "BscQII": "GTCTC", "BscRI": "RCCGGY", "BscSI": "RGATCY", "BscTI": "CCGCGG", "BscUI": "GCATC", "BscWI": "GGGAC", "BseI": "GGCC", "BseII": "GTTAAC", "Bse8I": "GATNNNNATC", "Bse9I": "GGCC", "Bse15I": "CYCGRG", "Bse16I": "CCWGG", "Bse17I": "CCWGG", "Bse19I": "CCATGG", "Bse21I": "CCTNAGG", "Bse23I": "CCNNNNNNNGG", "Bse24I": "CCWGG", "Bse54I": "GGNCC", "Bse59I": "GGTNACC", "Bse64I": "GGTNACC", "Bse118I": "RCCGGY", "Bse126I": "GGCC", "Bse631I": "GATNNNNATC", "Bse634I": "RCCGGY", "BseAI": "TCCGGA", "BseBI": "CCWGG", "BseB631I": "GCCNNNNNGGC", "BseB631II": "AGATCT", "BseCI": "ATCGAT", "BseDI": "CCNNGG", "Bse3DI": "GCAATG", "BseGI": "GGATG", "BseG73I": "CCTNAGG", "BseHI": "AAGCTT", "BseJI": "GATNNNNATC", "BseKI": "GCAGC", "BseLI": "CCNNNNNNNGG", "BseMI": "GCAATG", "BseMII": "CTCAG", "BsePI": "GCGCGC", "BseQI": "GGCC", "BseRI": "GAGGAG", "BseSI": "GKGCMC", "BseT9I": "GGTNACC", "BseT10I": "GGTNACC", "BseXI": "GCAGC", "BseX3I": "CGGCCG", "BseZI": "CTCTTC", "BsgI": "GTGCAG", "BshI": "GGCC", "Bsh45I": "GWGCWC", "Bsh1236I": "CGCG", "Bsh1285I": "CGRYCG", "Bsh1365I": "GATNNNNATC", "BshAI": "GGCC", "Bsh108AI": "ATCGAT", "BshBI": "GGCC", "BshCI": "GGCC", "BshDI": "GGCC", "BshEI": "GGCC", "BshFI": "GGCC", "BshGI": "CCWGG", "BshHI": "AGTACT", "BshKI": "GGNCC", "BshLI": "GATATC", "BshMI": "CCGG", "BshNI": "GGYRCC", "BshTI": "ACCGGT", "BshVI": "ATCGAT", "BsiAI": "GGCC", "BsiBI": "GATNNNNATC", "BsiCI": "TTCGAA", "BsiDI": "GGCC", "BsiEI": "CGRYCG", "BsiGI": "TCCGGA", "BsiHI": "GGCC", "BsiHKAI": "GWGCWC", "BsiHKCI": "CYCGRG", "BsiKI": "GGTNACC", "BsiLI": "CCWGG", "BsiMI": "TCCGGA", "BsiOI": "TCCGGA", "BsiQI": "TGATCA", "BsiSI": "CCGG", "BsiUI": "CCWGG", "BsiVI": "CCWGG", "BsiWI": "CGTACG", "BsiXI": "ATCGAT", "BsiYI": "CCNNNNNNNGG", "BsiZI": "GGNCC", "BslI": "CCNNNNNNNGG", "BslFI": "GGGAC", "Bsm6I": "GWGCWC", "BsmAI": "GTCTC", "BsmBI": "CGTCTC", "BsmCI": "ACNNNNNCTCC", "BsmDI": "ACNNNNNCTCC", "BsmEI": "GAGTC", "BsmFI": "GGGAC", "BsmGI": "TGTACA", "BsmGII": "AAGCTT", "BsmHI": "RGCGCY", "BsmNI": "GCATC", "BsmPI": "GWGCWC", "BsmRI": "TGTACA", "BsmSI": "CCWWGG", "BsmWI": "CGTACG", "BsmXI": "ACNNNNNCTCC", "BsmXII": "GATC", "BsmYI": "CCNNNNNNNGG", "BsnI": "GGCC", "BsoI": "CCNGG", "Bso31I": "GGTCTC", "BsoAI": "GATATC", "BsoBI": "CYCGRG", "BsoCI": "GDGCHC", "BsoDI": "CGGCCG", "BsoEI": "CCTNNNNNAGG", "BsoFI": "GCNGC", "BsoGI": "CCWGG", "BsoHI": "ACTGG", "BsoJI": "GCCNNNNNGGC", "BsoKI": "CCNNGG", "BsoMAI": "GTCTC", "BsoPI": "GCGCGC", "BsoSI": "AGTACT", "BspI": "GATC", "Bsp2I": "ATCGAT", "Bsp4I": "ATCGAT", "Bsp5I": "CCGG", "Bsp6I": "GCNGC", "Bsp6II": "CTGAAG", "Bsp7I": "CCSGG", "Bsp8I": "CCSGG", "Bsp9I": "GATC", "Bsp12I": "CCGCGG", "Bsp13I": "TCCGGA", "Bsp16I": "GATATC", "Bsp17I": "CTGCAG", "Bsp18I": "GATC", "Bsp19I": "CCATGG", "Bsp21I": "RCCGGY", "Bsp22I": "CTGGAG", "Bsp23I": "GGCC", "Bsp28I": "CTGGAG", "Bsp29I": "GGNNCC", "Bsp30I": "GGATCC", "Bsp43I": "CTGCAG", "Bsp44I": "CCWGG", "Bsp44II": "GGCC", "Bsp46I": "GGATCC", "Bsp47I": "CCGG", "Bsp48I": "CCGG", "Bsp49I": "GATC", "Bsp50I": "CGCG", "Bsp51I": "GATC", "Bsp52I": "GATC", "Bsp53I": "CCNGG", "Bsp54I": "GATC", "Bsp55I": "CCSGG", "Bsp56I": "CCWGG", "Bsp57I": "GATC", "Bsp58I": "GATC", "Bsp59I": "GATC", "Bsp60I": "GATC", "Bsp61I": "GATC", "Bsp63I": "CTGCAG", "Bsp64I": "GATC", "Bsp65I": "GATC", "Bsp66I": "GATC", "Bsp67I": "GATC", "Bsp68I": "TCGCGA", "Bsp70I": "CGCG", "Bsp71I": "GGWCC", "Bsp72I": "GATC", "Bsp73I": "CCNGG", "Bsp74I": "GATC", "Bsp76I": "GATC", "Bsp78I": "CTGCAG", "Bsp81I": "CTGCAG", "Bsp82I": "TTCGAA", "Bsp84I": "ATCGAT", "Bsp87I": "CACGTG", "Bsp90I": "TTCGAA", "Bsp90II": "GGATCC", "Bsp91I": "GATC", "Bsp92I": "CTCGAG", "Bsp93I": "CTGCAG", "Bsp98I": "GGATCC", "Bsp100I": "GGWCC", "Bsp101I": "TTCGAA", "Bsp102I": "TTCGAA", "Bsp103I": "CCWGG", "Bsp104I": "TTCGAA", "Bsp105I": "GATC", "Bsp106I": "ATCGAT", "Bsp107I": "CTGCAG", "Bsp108I": "CTGCAG", "Bsp116I": "CCGG", "Bsp117I": "GRGCYC", "Bsp119I": "TTCGAA", "Bsp120I": "GGGCCC", "Bsp121I": "GCATGC", "Bsp122I": "GATC", "Bsp123I": "CGCG", "Bsp125I": "ATCGAT", "Bsp126I": "ATCGAT", "Bsp127I": "ATCGAT", "Bsp128I": "GGWCC", "Bsp129I": "CTCGAG", "Bsp130I": "GGATCC", "Bsp131I": "GGATCC", "Bsp132I": "GGWCC", "Bsp133I": "GGWCC", "Bsp135I": "GATC", "Bsp136I": "GATC", "Bsp137I": "GGCC", "Bsp138I": "GATC", "Bsp139I": "CTCGAG", "Bsp140I": "CTCGAG", "Bsp141I": "CTCGAG", "Bsp142I": "CTCGAG", "Bsp143I": "GATC", "Bsp143II": "RGCGCY", "Bsp144I": "GGATCC", "Bsp145I": "ATCGAT", "Bsp146I": "GTGCAC", "Bsp147I": "GATC", "Bsp148I": "TTCGAA", "Bsp151I": "TTCGAA", "Bsp211I": "GGCC", "Bsp226I": "GGCC", "Bsp228I": "TCCGGA", "Bsp233I": "TCCGGA", "Bsp241I": "TTCGAA", "Bsp268I": "CTGCAG", "Bsp317I": "CCWGG", "Bsp423I": "GCAGC", "Bsp460III": "CGCGCAG", "Bsp508I": "TCCGGA", "Bsp519I": "GRGCYC", "Bsp548I": "CCNGG", "Bsp881I": "GGCC", "Bsp1260I": "GGWCC", "Bsp1261I": "GGCC", "Bsp1286I": "GDGCHC", "Bsp1407I": "TGTACA", "Bsp1591I": "GGTNACC", "Bsp1591II": "CCGG", "Bsp1593I": "GGCC", "Bsp1720I": "GCTNAGC", "Bsp1894I": "GGNCC", "Bsp2013I": "GGCC", "Bsp2095I": "GATC", "Bsp2362I": "GGCC", "Bsp2500I": "GGCC", "Bsp3004IV": "CCGCAT", "Bsp4009I": "GGATCC", "BspAI": "GATC", "BspA2I": "CCTAGG", "Bsp153AI": "CAGCTG", "BspAAI": "CTCGAG", "BspAAII": "TCTAGA", "BspAAIII": "GGATCC", "BspANI": "GGCC", "BspBI": "CTGCAG", "BspBII": "GGNCC", "BspBDG2I": "GGCC", "BspBRI": "GGCC", "BspBS31I": "GAAGAC", "BspBSE18I": "GGCC", "BspBake1I": "GGCC", "BspCI": "CGATCG", "BspCHE15I": "GGCC", "BspCNI": "CTCAG", "BspDI": "ATCGAT", "BspD6I": "GAGTC", "BspD6II": "CTGAAG", "BspEI": "TCCGGA", "BspFI": "GATC", "BspF4I": "GGNCC", "BspF53I": "GGWCC", "BspF105I": "CCSGG", "BspFNI": "CGCG", "BspGI": "CTGGAC", "BspGHA1I": "GGCC", "BspHI": "TCATGA", "BspHII": "GTAGAT", "BspH22I": "TTCGAA", "BspH43I": "CCWGG", "BspH103I": "TTCGAA", "BspH106I": "TTCGAA", "BspH106II": "GGCC", "BspH226I": "TCCGGA", "BspIS4I": "GAAGAC", "BspJI": "GATC", "BspJII": "ATCGAT", "BspJ64I": "GATC", "BspJ67I": "CCSGG", "BspJ74I": "CTGGAG", "BspJ76I": "CGCG", "BspJ105I": "GGWCC", "BspJ106I": "GGTACC", "BspKI": "GGCC", "BspKMI": "GATC", "BspKT5I": "CTGAAG", "BspKT6I": "GATC", "BspKT8I": "AAGCTT", "BspLI": "GGNNCC", "BspLAI": "GCGC", "BspLAII": "TTCGAA", "BspLAIII": "AAGCTT", "M.BspLCT2I": "GANTC", "BspLRI": "GGCC", "BspLS2I": "GDGCHC", "BspLU4I": "CYCGRG", "BspLU11I": "ACATGT", "BspLU11II": "TCTAGA", "BspLU11III": "GGGAC", "BspMI": "ACCTGC", "BspMII": "TCCGGA", "BspM39I": "CAGCTG", "BspM90I": "GTATAC", "BspMAI": "CTGCAG", "BspMKI": "GTCGAC", "BspNI": "CCWGG", "BspNCI": "CCAGA", "BspOI": "GCTAGC", "BspO4I": "CAGCTG", "BspOVI": "GACNNNNNGTC", "BspOVII": "ATCGAT", "BspPI": "GGATC", "BspQI": "GCTCTTC", "BspRI": "GGCC", "BspR7I": "CCTNAGG", "M.BspRAC05I": "GANTC", "BspSI": "CCWGG", "BspS122I": "CTGCAG", "BspST5I": "GCATC", "BspTI": "CTTAAG", "BspT104I": "TTCGAA", "BspT107I": "GGYRCC", "BspTNI": "GGTCTC", "BspTS514I": "GAAGAC", "BspUI": "GCSGC", "BspVI": "GAAGAC", "BspWI": "GCNNNNNNNGC", "BspXI": "ATCGAT", "BspXII": "TGATCA", "BspZEI": "ATCGAT", "BsrAI": "GGWCC", "BsrBRI": "GATNNNNATC", "BsrCI": "ATCGAT", "BsrDI": "GCAATG", "BsrEI": "CTCTTC", "BsrFI": "RCCGGY", "BsrGI": "TGTACA", "BsrHI": "GCGCGC", "BsrMI": "GATC", "BsrPII": "GATC", "BsrVI": "GCAGC", "BsrWI": "GGATC", "BsrXI": "TCTAGA", "BssI": "GGNNCC", "BssAI": "RCCGGY", "BssBI": "GCGCGC", "BssCI": "GGCC", "BssECI": "CCNNGG", "BssFI": "GCNGC", "BssGI": "CCANNNNNNTGG", "BssGII": "GATC", "BssHI": "CTCGAG", "BssHII": "GCGCGC", "BssKI": "CCNGG", "BssMI": "GATC", "BssNI": "GRCGYC", "BssNAI": "GTATAC", "BssT1I": "CCWWGG", "BssXI": "GCNGC", "BstI": "GGATCC", "Bst1I": "CCWGG", "Bst2I": "CCWGG", "Bst6I": "CTCTTC", "Bst12I": "GCAGC", "Bst16I": "RGCGCY", "Bst19I": "GCATC", "Bst19II": "GATC", "Bst22I": "CCNNNNNNNGG", "Bst28I": "ATCGAT", "Bst29I": "CCTNAGG", "Bst30I": "CCTNAGG", "Bst31I": "GGTNACC", "Bst38I": "CCWGG", "Bst40I": "CCGG", "Bst71I": "GCAGC", "Bst77I": "TGATCA", "Bst98I": "CTTAAG", "Bst100I": "CCWGG", "Bst158I": "CTCTTC", "Bst170I": "TGTACA", "Bst170II": "AAGCTT", "Bst224I": "CCWWGG", "Bst295I": "CTNAG", "Bst1107I": "GTATAC", "Bst1126I": "GGATCC", "Bst1274I": "GATC", "Bst1473I": "WCCGGW", "Bst1473II": "RGCGCY", "Bst2464I": "GGATCC", "Bst2902I": "GGATCC", "BstACI": "GRCGYC", "BstAFI": "CTTAAG", "BstAPI": "GCANNNNNTGC", "BstAUI": "TGTACA", "BstBI": "TTCGAA", "BstBAI": "YACGTR", "BstBAII": "CYCGRG", "BstBSI": "GTATAC", "BstB7SI": "RCCGGY", "BstBS32I": "GAAGAC", "BstBZ153I": "GCGCGC", "BstCI": "GGCC", "Bst4CI": "ACNGT", "BstC8I": "GCNNGC", "BstDI": "GGTNACC", "BstDEI": "CTNAG", "BstDSI": "CCRYGG", "BstDZ247I": "CCCGT", "BstEII": "GGTNACC", "BstEIII": "GATC", "BstENI": "CCTNNNNNAGG", "BstENII": "GATC", "BstEZ359I": "GTTAAC", "BstFI": "AAGCTT", "BstF5I": "GGATG", "Bst1FBI": "GDGCHC", "BstFNI": "CGCG", "BstFZ438I": "CCCGC", "BstGI": "TGATCA", "BstGII": "CCWGG", "BstGZ53I": "CGTCTC", "BstHI": "CTCGAG", "BstH2I": "RGCGCY", "BstH9I": "GGATC", "BstHHI": "GCGC", "BstHPI": "GTTAAC", "BstHZ55I": "CCANNNNNNTGG", "BstIZ316I": "CACNNNGTG", "BstJI": "GGCC", "BstJZ301I": "CTNAG", "BstKI": "TGATCA", "BstKTI": "GATC", "BstLI": "CTCGAG", "BstLVI": "ATCGAT", "BstMI": "AGTACT", "BstM6I": "CCWGG", "BstMAI": "GTCTC", "BstMBI": "GATC", "BstMCI": "CGRYCG", "BstMWI": "GCNNNNNNNGC", "BstMZ611I": "CCNGG", "BstNI": "CCWGG", "M.BstNBII": "GASTC", "BstNSI": "RCATGY", "BstNSII": "CYCGRG", "BstNZ169I": "ATCGAT", "BstOI": "CCWGG", "BstOZ616I": "GGGAC", "BstPI": "GGTNACC", "BstPAI": "GACNNNNGTC", "BstPZ740I": "CTTAAG", "BstQI": "GGATCC", "Bst4QI": "GGWCC", "Bst7QI": "CYCGRG", "Bst7QII": "CCWGG", "BstRI": "GATATC", "BstRZ246I": "ATTTAAAT", "BstSI": "CYCGRG", "BstSCI": "CCNGG", "BstSFI": "CTRYAG", "BstSLI": "GKGCMC", "BstSNI": "TACGTA", "BstSWI": "ATTTAAAT", "BstTI": "CCANNNNNNTGG", "BstT7I": "TGATCA", "BstT9I": "GGTNACC", "BstT10I": "GGTNACC", "Bst31TI": "GGATC", "BstTS5I": "GAAGAC", "BstUI": "CGCG", "Bst2UI": "CCWGG", "BstVI": "CTCGAG", "BstV1I": "GCAGC", "BstV2I": "GAAGAC", "BstWI": "CCTNNNNNAGG", "BstXI": "CCANNNNNNTGG", "BstXII": "GATC", "BstX2I": "RGATCY", "BstYI": "RGATCY", "BstZI": "CGGCCG", "BstZ1I": "TCCGGA", "BstZ1II": "AAGCTT", "BstZ2I": "GACNNNNNGTC", "BstZ3I": "TCCGGA", "BstZ4I": "CYCGRG", "BstZ5I": "CGRYCG", "BstZ6I": "CCTNAGG", "BstZ7I": "GRGCYC", "BstZ8I": "CGATCG", "BstZ9I": "ACGCGT", "BstZ10I": "CCNNGG", "BstZ10II": "TGATCA", "BstZ15I": "GDGCHC", "BstZ16I": "GTCGAC", "BstZ17I": "GTATAC", "BsuI": "GTATCC", "Bsu6I": "CTCTTC", "Bsu15I": "ATCGAT", "Bsu22I": "TCCGGA", "Bsu23I": "TCCGGA", "Bsu36I": "CCTNAGG", "Bsu54I": "GGNCC", "Bsu90I": "GGATCC", "Bsu537I": "GGTCTC", "Bsu1076I": "GGCC", "Bsu1114I": "GGCC", "Bsu1192I": "CCGG", "Bsu1192II": "CGCG", "Bsu1193I": "CGCG", "Bsu1532I": "CGCG", "Bsu1854I": "GRGCYC", "Bsu3610I": "GACGAG", "Bsu5044I": "GGNCC", "Bsu6633I": "CGCG", "Bsu7003I": "GACGAG", "Bsu8565I": "GGATCC", "Bsu8646I": "GGATCC", "BsuBI": "CTGCAG", "BsuB519I": "GGATCC", "BsuB763I": "GGATCC", "BsuEII": "CGCG", "BsuFI": "CCGG", "BsuJPCI": "GACGAG", "BsuMI": "CTCGAG", "R1.BsuMI": "CTCGAG", "R2.BsuMI": "CTCGAG", "R3.BsuMI": "CTCGAG", "BsuRI": "GGCC", "BsuTUI": "ATCGAT", "BsxI": "ACTGGG", "BtcI": "GATC", "BteI": "GGCC", "BtgI": "CCRYGG", "BtgAI": "GTCGAC", "BtgAII": "GCATGC", "BtgZI": "GCGATG", "BthI": "CTCGAG", "BthII": "GGATC", "Bth84I": "GATC", "Bth171I": "GCNGC", "Bth211I": "GATC", "Bth213I": "GATC", "Bth221I": "GATC", "Bth571I": "GCAGT", "Bth617I": "GGATC", "Bth945I": "GATC", "Bth1140I": "GATC", "Bth1141I": "GATC", "Bth1202I": "ATCGAT", "Bth1786I": "GATC", "Bth1795I": "CTGGAG", "Bth1997I": "GATC", "Bth2350I": "CAGCTG", "Bth9411I": "CTGCAG", "Bth9415I": "ATCGAT", "BthAI": "GGWCC", "BthCI": "GCNGC", "BthCanI": "GATC", "BthDI": "CCWGG", "BthEI": "CCWGG", "BthP35I": "CTRYAG", "BtiI": "GGWCC", "BtkI": "CGCG", "BtkII": "GATC", "Btr192II": "ACATC", "BtsI": "GCAGTG", "BtsIMutI": "CAGTG", "BtsCI": "GGATG", "BtsPI": "GGGTC", "BtuI": "ATCGAT", "Btu33I": "GATC", "Btu34I": "GATC", "Btu34II": "RGCGCY", "Btu36I": "GATC", "Btu37I": "GATC", "Btu39I": "GATC", "Btu41I": "GATC", "BtuMI": "TCGCGA", "BveI": "ACCTGC", "M.Bve289II": "GANTC", "Bve1B23I": "GACNNNNNTGG", "BvuI": "GRGCYC", "BvuBI": "CGTACG", "CacI": "GATC", "Cac8I": "GCNNGC", "Cac824I": "GCNGC", "CagI": "GCCNNNNNGGC", "CaiI": "CAGNNNCTG", "Cal5972I": "ACGRAG", "Cal14237I": "GGTTAG", "CalB3II": "GRTTRAG", "M.Cam122Dam": "GATC", "M.Cam122Dcm": "CCWGG", "M.Cam165Dcm": "CCWGG", "Cas2I": "CGATCG", "CatHI": "CTCTTC", "CauI": "GGWCC", "CauII": "CCSGG", "CauIII": "CTGCAG", "Cau10061II": "GTTAAT", "CauB3I": "TCCGGA", "Cba13II": "AGGAAT", "Cba16038I": "CCTNAYNC", "Cba4G11III": "CACGAG", "CbeI": "GGCC", "CbiI": "TTCGAA", "CboI": "CCGG", "Cbo1632I": "GCRGAAG", "Cbo67071IV": "GCRGAAG", "CboB305II": "CTGGAG", "CbrI": "CCWGG", "M.Cbr253II": "CCWGG", "M.Cbr253Dam": "GATC", "M.Cbr290Dam": "GATC", "CbuDI": "GCNGC", "CceI": "CCGG", "Cce743I": "GACGC", "Cce743II": "CCWGG", "CchI": "CTAG", "CchII": "GGARGA", "CchIII": "CCCAAG", "Cch467III": "GNGAAAY", "Cch7528III": "GNGAAAY", "CciI": "TCATGA", "CciNI": "GCGGCCGC", "CcoI": "GCCGGC", "Cco280III": "GAGNNNNNRTG", "Cco280IV": "GGGTDA", "Cco14983III": "GAGNNNNNRTG", "Cco14983V": "GGGTDA", "Cco14983VI": "GCYGA", "Cco54106III": "GAGNNNNNRTG", "Cco104626I": "GAGNNNNNRTG", "Cco104626IV": "GGGTDA", "Cco104627II": "GGGTDA", "CcoLI": "GATC", "CcoMI": "CAGCAG", "CcoP13III": "CAYNNNNNRTG", "CcoP31I": "GATC", "CcoP73I": "GTAC", "CcoP76I": "GATC", "CcoP84I": "GATC", "CcoP95I": "GCGC", "CcoP95II": "GATC", "CcoP215I": "GCNGC", "CcoP216I": "GCNGC", "CcoP219I": "GATC", "CcrI": "CTCGAG", "CcrNAIII": "CGACCAG", "CcuI": "GGNCC", "CcyI": "GATC", "Cdi27I": "CCWGG", "Cdi81III": "GCMGAAG", "Cdi197II": "GCGGAG", "Cdi1678I": "GCGGAG", "Cdi11397I": "GCGCAG", "CdiAI": "GGNCC", "CdiCD6I": "GGNCC", "CdiCD6II": "GATC", "CdpI": "GCGGAG", "Cdu23823II": "GTGAAG", "CelI": "GGATCC", "CelII": "GCTNAGC", "CeqI": "GATATC", "CfaI": "RAATTY", "CflI": "CTGCAG", "CfoI": "GCGC", "CfrI": "YGGCCR", "Cfr4I": "GGNCC", "Cfr5I": "CCWGG", "Cfr6I": "CAGCTG", "Cfr7I": "GGTNACC", "Cfr8I": "GGNCC", "Cfr9I": "CCCGGG", "Cfr10I": "RCCGGY", "Cfr11I": "CCWGG", "Cfr13I": "GGNCC", "Cfr14I": "YGGCCR", "Cfr19I": "GGTNACC", "Cfr20I": "CCWGG", "Cfr22I": "CCWGG", "M.Cfr023I": "CCWGG", "Cfr23I": "GGNCC", "Cfr24I": "CCWGG", "Cfr25I": "CCWGG", "Cfr27I": "CCWGG", "Cfr28I": "CCWGG", "Cfr29I": "CCWGG", "Cfr30I": "CCWGG", "Cfr31I": "CCWGG", "Cfr32I": "AAGCTT", "Cfr33I": "GGNCC", "Cfr35I": "CCWGG", "Cfr37I": "CCGCGG", "Cfr38I": "YGGCCR", "Cfr39I": "YGGCCR", "Cfr40I": "YGGCCR", "Cfr41I": "CCGCGG", "Cfr42I": "CCGCGG", "Cfr43I": "CCGCGG", "Cfr45I": "GGNCC", "Cfr45II": "CCGCGG", "Cfr46I": "GGNCC", "Cfr47I": "GGNCC", "Cfr48I": "GRGCYC", "Cfr51I": "CGATCG", "Cfr52I": "GGNCC", "Cfr54I": "GGNCC", "Cfr55I": "YGGCCR", "Cfr56I": "GGTCTC", "Cfr57I": "TCCGGA", "Cfr58I": "CCWGG", "Cfr59I": "YGGCCR", "Cfr92I": "CTTAAG", "CfrAI": "GCANNNNNNNNGTGG", "CfrA4I": "CTGCAG", "M.CfrABFQGDam": "GATC", "CfrBI": "CCWWGG", "CfrCI": "C", "M.Cfr023Dam": "GATC", "M.Cfr61Dam": "GATC", "M.Cfr101Dam": "GATC", "CfrJ4I": "CCCGGG", "CfrJ5I": "GCGCGC", "CfrMH13II": "AGCANCC", "M.CfrMH13III": "CCWGG", "M.CfrMH14II": "CCWGG", "CfrMH15IV": "GACATC", "M.CfrMH16V": "CCWGG", "CfrMH16VI": "CTAAAG", "M.CfrMH17III": "CCWGG", "M.CfrMH19II": "CCWGG", "M.CfrMH13Dam": "GATC", "M.CfrMH17Dam": "GATC", "CfrNI": "GGNCC", "M.CfrNIH3Dam": "GATC", "M.CfrNIH4Dam": "GATC", "M.CfrNIH9Dam": "GATC", "CfrS37I": "CCWGG", "CfuI": "GATC", "CfuII": "CTGCAG", "Cfupf3II": "GARCAG", "CgaBS1I": "CGANNNNNNTGA", "CglI": "GCSGC", "Cgl13032I": "GGCGCA", "Cgl13032II": "ACGABGG", "CglAI": "GCATGC", "CglAII": "GTCGAC", "ChaI": "GATC", "ChuEI": "AAGCTT", "ChuEII": "GTYRAC", "ChyI": "AGGCCT", "Cin1467I": "GATC", "CjaI": "CTCGAG", "Cje262III": "CCYGA", "Cje263II": "CCYGA", "Cje263III": "GKAAYG", "Cje263IV": "GAGNNNNNGT", "Cje265II": "CCANNNNNNGT", "Cje265V": "GKAAGC", "Cje32488II": "GKAAGC", "Cje54107III": "GKAAYC", "CjeFII": "AGTNNNNNNRTTG", "CjeFIII": "GCAAGG", "CjeFIV": "GCANNNNNRTTA", "CjeFV": "GGRCA", "CjeF38011III": "GAGNNNNNRTG", "CjeF38011IV": "GKAAYC", "CjeIAIII": "GAGNNNNNRTG", "CjeNII": "GAGNNNNNGT", "CjeNIII": "GKAAYG", "CjeNIV": "GCANNNNNRTTA", "CjeNV": "CCYGA", "CjeP338I": "GATC", "CjeP338II": "GCATC", "CjeP659IV": "CACNNNNNNNGAA", "CjeYH002II": "CCYGA", "CjeYH002III": "GKAAYG", "CjeYH002IV": "GAGNNNNNGT", "CjuI": "CAYNNNNNRTG", "CjuII": "CAYNNNNNCTC", "M.Cko24Dam": "GATC", "M.Cko25Dam": "GATC", "M.Cko393Dam": "GATC", "ClaI": "ATCGAT", "Cla11845III": "GCGAA", "ClcI": "CTGCAG", "ClcII": "TGCGCA", "CliI": "GGWCC", "CliII": "TGCGCA", "ClmI": "GGCC", "ClmII": "GGWCC", "CltI": "GGCC", "Cly7489II": "AAAAGRG", "CmaLM2II": "GCNGC", "M.Cmi38I": "GANTC", "M.Cne392Dam": "GATC", "Cpa1150I": "CGCG", "CpaAI": "CGCG", "CpaPI": "GATC", "CpeI": "TGATCA", "CpfI": "GATC", "CpfAI": "GATC", "CphBI": "CCCGGG", "CpoI": "CGGWCCG", "CprJK722I": "ATTAAT", "CscI": "CCGCGG", "CseI": "GACGC", "CsiI": "ACCWGGT", "CsiAI": "ACCGGT", "CsiBI": "GCGGCCGC", "CspI": "CGGWCCG", "Csp2I": "GGCC", "Csp4I": "ATCGAT", "Csp5I": "GATC", "Csp6I": "GTAC", "M.Csp26II": "GANTC", "Csp45I": "TTCGAA", "Csp231I": "AAGCTT", "Csp1470I": "GCGC", "Csp2014I": "GGAGGC", "CspAI": "ACCGGT", "Csp12AI": "GGATG", "CspBI": "GCGGCCGC", "Csp68KI": "GGWCC", "Csp68KII": "TTCGAA", "Csp68KIII": "ATGCAT", "M.Csp68KIV": "CCGG", "M.Csp68KV": "GGCC", "Csp68KVI": "CGCG", "M.CspM006Dam": "GATC", "CstI": "CTGCAG", "CstMI": "AAGGAG", "Cte1I": "CCGCGG", "Cte1179I": "GATC", "Cte1180I": "GATC", "CthI": "TGATCA", "CthII": "CCWGG", "CtyI": "GATC", "M.Cun9529Dam": "GATC", "CviAI": "GATC", "CviAII": "CATG", "M.CviAIV": "RGCB", "CviBI": "GANTC", "M.CviBII": "GATC", "M.CviBIII": "TCGA", "CviCI": "GANTC", "CviDI": "GANTC", "CviEI": "GANTC", "CviFI": "GANTC", "CviGI": "GANTC", "CviHI": "GATC", "CviJI": "RGCY", "CviKI-1": "RGCY", "CviKI": "RGCY", "CviLI": "RGCY", "CviMI": "RGCY", "CviNI": "RGCY", "CviOI": "RGCY", "M.CviPI": "GC", "CviQI": "GTAC", "CviRI": "TGCA", "CviRII": "GTAC", "M.CviSI": "TGCA", "M.CviSII": "CATG", "CviSIII": "TCGA", "CvnI": "CCTNAGG", "M.CweMH18IV": "CCWGG", "DaqI": "GTGCAC", "DdeI": "CTNAG", "DdeII": "CTCGAG", "Dde51507I": "CCWGG", "DdsI": "GGATCC", "DfaPA1III": "TACGAG", "M.DfaPA1Dam": "GATC", "DinI": "GGCGCC", "DmaI": "CAGCTG", "Dor12838III": "CTGGAG", "DpaI": "AGTACT", "DpnI": "GATC", "DpnII": "GATC", "DraI": "TTTAAA", "DraII": "RGGNCCY", "DraIII": "CACNNNGTG", "DraRI": "CAAGNAC", "DrdI": "GACNNNNNNGTC", "DrdII": "GAACCA", "DrdIII": "CGATCG", "DrdIV": "TACGAC", "DrdV": "CATGNAC", "DrdVI": "GCAGCC", "DrdVIII": "ARGAGC", "DrdAI": "CCGCGG", "DrdBI": "CCGCGG", "DrdCI": "CCGCGG", "DrdDI": "CTCGAG", "DrdEI": "CCGCGG", "DrdFI": "CCGCGG", "DriI": "GACNNNNNGTC", "DsaI": "CCRYGG", "DsaII": "GGCC", "DsaIII": "RGATCY", "DsaIV": "GGWCC", "DsaV": "CCNGG", "DsaVI": "GTMKAC", "DseDI": "GACNNNNNNGTC", "M.Dsh12I": "GANTC", "Dsp1I": "CCGCGG", "Dsp20I": "GCWGC", "DvuI": "TGACNNNNNNTTC", "DvuIII": "CACNCAC", "EacI": "GGATC", "M.Ead53I": "GANTC", "EaeI": "YGGCCR", "Eae2I": "CTCGAG", "Eae46I": "CCGCGG", "EaeAI": "CCCGGG", "M.Eae35Dam": "GATC", "EaePI": "CTGCAG", "EagI": "CGGCCG", "EagBI": "CGATCG", "EagKI": "CCWGG", "EagMI": "GGWCC", "Eam1104I": "CTCTTC", "Eam1105I": "GACNNNNNGTC", "EarI": "CTCTTC", "M.EasAEB30Dam": "GATC", "M.EasL1Dam": "GATC", "EbaNIH1II": "CTGAAG", "EbaNIH3I": "CTGAAG", "EcaI": "GGTNACC", "EcaII": "CCWGG", "EccI": "CCGCGG", "EciI": "GGCGGA", "Eci125I": "GGTNACC", "EciAI": "TACGTA", "EciBI": "YGGCCR", "EciCI": "CCTNAGG", "EciDI": "CCSGG", "EciEI": "GGGCCC", "EclI": "CAGCTG", "EclII": "CCWGG", "Ecl1I": "CCGCGG", "Ecl28I": "CCGCGG", "Ecl37I": "CCGCGG", "Ecl66I": "CCWGG", "Ecl77I": "CTGCAG", "Ecl133I": "CTGCAG", "Ecl136I": "CCWGG", "Ecl136II": "GAGCTC", "Ecl137I": "GAGCTC", "Ecl137II": "CCWGG", "Ecl234I": "CGGNAAG", "Ecl388I": "CGGNAAG", "Ecl593I": "CTGCAG", "Ecl35734I": "GAAAYTC", "M.EclAA4Dam": "GATC", "M.Ecl91Dam": "GATC", "M.EclF77Dam": "GATC", "M.EclHC3Dam": "GATC", "M.EclHC4Dam": "GATC", "EclHKI": "GACNNNNNGTC", "EclJI": "CGATCG", "M.EclNH77Dam": "GATC", "M.EclNIH2Dam": "GATC", "M.EclNIH3Dam": "GATC", "M.EclNIH4Dam": "GATC", "M.EclNIH5Dam": "GATC", "EclRI": "CCCGGG", "EclS39I": "CCWGG", "EclXI": "CGGCCG", "Ecl18kI": "CCNGG", "Ecl37kI": "CTGCAG", "Ecl37kII": "CCWGG", "Ecl54kI": "CCWGG", "Ecl57kI": "CCWGG", "Ecl699kI": "CTGCAG", "Ecl1zI": "CTGCAG", "Ecl1zII": "CCWGG", "Ecl2zI": "CTGCAG", "Eco17I": "GATATC", "Eco24I": "GRGCYC", "Eco25I": "GRGCYC", "Eco26I": "GRGCYC", "Eco31I": "GGTCTC", "Eco32I": "GATATC", "Eco35I": "GRGCYC", "Eco37I": "GGANNNNNNNNATGC", "Eco38I": "CCWGG", "Eco39I": "GGNCC", "Eco40I": "CCWGG", "Eco41I": "CCWGG", "Eco42I": "GGTCTC", "Eco43I": "CCNGG", "Eco47I": "GGWCC", "Eco47II": "GGNCC", "Eco47III": "AGCGCT", "Eco48I": "CTGCAG", "Eco49I": "CTGCAG", "Eco50I": "GGYRCC", "Eco51I": "GGTCTC", "Eco51II": "CCNGG", "Eco52I": "CGGCCG", "Eco55I": "CCGCGG", "Eco56I": "GCCGGC", "Eco57I": "CTGAAG", "Eco60I": "CCWGG", "Eco61I": "CCWGG", "Eco64I": "GGYRCC", "Eco65I": "AAGCTT", "Eco67I": "CCWGG", "Eco68I": "GRGCYC", "Eco70I": "CCWGG", "Eco71I": "CCWGG", "Eco72I": "CACGTG", "Eco76I": "CCTNAGG", "Eco78I": "GGCGCC", "Eco80I": "CCNGG", "Eco81I": "CCTNAGG", "Eco82I": "GAATTC", "Eco83I": "CTGCAG", "Eco85I": "CCNGG", "Eco88I": "CYCGRG", "Eco90I": "YGGCCR", "Eco91I": "GGTNACC", "Eco92I": "CCGCGG", "Eco93I": "CCNGG", "Eco95I": "GGTCTC", "Eco96I": "CCGCGG", "Eco97I": "GGTCTC", "Eco98I": "AAGCTT", "Eco99I": "CCGCGG", "Eco100I": "CCGCGG", "Eco101I": "GGTCTC", "Eco104I": "CCGCGG", "Eco105I": "TACGTA", "Eco112I": "CTGAAG", "Eco113I": "GRGCYC", "Eco115I": "CCTNAGG", "Eco118I": "CCTNAGG", "Eco120I": "GGTCTC", "Eco121I": "CCSGG", "Eco125I": "CTGAAG", "Eco127I": "GGTCTC", "Eco128I": "CCWGG", "Eco129I": "GGTCTC", "Eco130I": "CCWWGG", "Eco134I": "CCGCGG", "Eco135I": "CCGCGG", "Eco143I": "GCGCGC", "Eco147I": "AGGCCT", "Eco149I": "GGTACC", "Eco151I": "CCGCGG", "Eco152I": "GCGCGC", "Eco153I": "CCNGG", "Eco155I": "GGTCTC", "Eco156I": "GGTCTC", "Eco157I": "GGTCTC", "Eco158I": "CCGCGG", "Eco158II": "TACGTA", "Eco159I": "GAATTC", "Eco161I": "CTGCAG", "Eco0162II": "ATGAAG", "Eco162I": "GGTCTC", "Eco164I": "YGGCCR", "Eco167I": "CTGCAG", "Eco168I": "GGYRCC", "Eco169I": "GGYRCC", "Eco170I": "CCWGG", "Eco171I": "GGYRCC", "Eco173I": "GGYRCC", "Eco178I": "GATATC", "Eco179I": "CCSGG", "Eco180I": "GRGCYC", "Eco182I": "CCGCGG", "Eco185I": "GGTCTC", "Eco188I": "AAGCTT", "Eco190I": "CCSGG", "Eco191I": "GGTCTC", "Eco193I": "CCWGG", "Eco195I": "GGYRCC", "Eco196I": "CCGCGG", "Eco196II": "GGNCC", "Eco200I": "CCNGG", "Eco201I": "GGNCC", "Eco203I": "GGTCTC", "Eco204I": "GGTCTC", "Eco205I": "GGTCTC", "Eco206I": "CCWGG", "Eco207I": "CCWGG", "Eco208I": "CCGCGG", "Eco208II": "CCWWGG", "Eco211I": "GRGCYC", "Eco215I": "GRGCYC", "Eco216I": "GRGCYC", "Eco217I": "GGTCTC", "Eco225I": "GGTCTC", "Eco228I": "GAATTC", "Eco231I": "AAGCTT", "Eco232I": "GRGCYC", "Eco233I": "GGTCTC", "Eco237I": "GAATTC", "Eco239I": "GGTCTC", "Eco240I": "GGTCTC", "Eco241I": "GGTCTC", "Eco246I": "GGTCTC", "Eco247I": "GGTCTC", "Eco249I": "GRGCYC", "Eco252I": "GAATTC", "Eco254I": "CCWGG", "Eco255I": "AGTACT", "Eco256I": "CCWGG", "Eco260I": "CTGCAG", "Eco261I": "CTGCAG", "Eco262I": "GRGCYC", "Eco263I": "GGTCTC", "Eco268I": "CRARCAG", "Eco293II": "GAAABCC", "Eco377I": "GGANNNNNNNNATGC", "Eco394I": "GACNNNNNRTAAY", "Eco0398II": "GAAABCC", "Eco585I": "GCCNNNNNNTGCG", "Eco644I": "GAAABCC", "Eco646I": "CCANNNNNNNCTTC", "Eco777I": "GGANNNNNNTATC", "Eco826I": "GCANNNNNNCTGA", "Eco851I": "GTCANNNNNNTGAY", "Eco912I": "CACNNNNNTGGC", "Eco933II": "GAAABCC", "Eco1130II": "GAAABCC", "Eco1158I": "TGANNNNNNNNTGCT", "Eco1265I": "TGANNNNNNNNTGCT", "Eco1323I": "GGANNNNNNNNATGC", "Eco1341I": "CCANNNNNNNCTTC", "Eco1342I": "AACNNNNNNGTGC", "Eco1344I": "AACNNNNNNGTGC", "Eco1344II": "GGANNNNNNNNATGC", "Eco1348I": "GGANNNNNNTATC", "Eco1383I": "CCANNNNNNNCTTC", "Eco1386I": "GGANNNNNNNNATGC", "Eco1394I": "AACNNNNNNGTGC", "Eco1412I": "GGANNNNNNTATC", "Eco1413I": "CCANNNNNNNCTTC", "Eco1422I": "CCANNNNNNNCTTC", "Eco1424I": "CCANNNNNNNCTTC", "Eco1427I": "GGANNNNNNNNATGC", "Eco1430I": "GGANNNNNNNNATGC", "Eco1432I": "CCANNNNNNNCTTC", "Eco1441I": "TGANNNNNNNNTGCT", "Eco1443I": "TGANNNNNNNNTGCT", "Eco1444I": "TGANNNNNNNNTGCT", "Eco1446I": "GAGNNNNNNNGTCA", "Eco1447I": "TGANNNNNNNNTGCT", "Eco1455I": "GCANNNNNNCTGA", "Eco1456I": "GGANNNNNNNNATGC", "Eco1476I": "GGANNNNNNNNATGC", "Eco1524I": "AGGCCT", "Eco1831I": "CCSGG", "Eco2149II": "GAAABCC", "Eco2159II": "GAAABCC", "Eco3384II": "GAAABCC", "Eco4276II": "GAAABCC", "Eco4465II": "GAAABCC", "Eco8368II": "GAAABCC", "Eco9234II": "GAAABCC", "Eco9272I": "CTGAAG", "Eco9276II": "CRARCAG", "Eco9280I": "CRARCAG", "Eco9322I": "CRARCAG", "Eco43896II": "CRARCAG", "EcoAI": "GAGNNNNNNNGTCA", "EcoA4I": "GGTCTC", "EcoAO83I": "GGANNNNNNNNATGC", "EcoBI": "TGANNNNNNNNTGCT", "M.EcoB4103Dam": "GATC", "EcoBE25II": "CTTGAC", "M.EcoBE25Dam": "GATC", "M.EcoBE104Dam": "GATC", "M.EcoBH212Dam": "GATC", "M.EcoBL21ADam": "GATC", "M.EcoC600Dam": "GATC", "M.EcoC2566Dam": "GATC", "EcoDI": "TTANNNNNNNGTCY", "M.EcoD181Dam": "GATC", "M.EcoDHB4Dam": "GATC", "EcoDR2": "TCANNNNNNGTCG", "EcoDR3": "TCANNNNNNNATCG", "EcoDXXI": "TCANNNNNNNRTTC", "M.Eco038Dam": "GATC", "M.Eco051Dam": "GATC", "M.Eco053Dam": "GATC", "M.Eco86Dam": "GATC", "M.Eco114Dam": "GATC", "M.Eco299Dam": "GATC", "M.Eco532Dam": "GATC", "M.Eco544Dam": "GATC", "M.Eco629Dam": "GATC", "M.Eco1109Dam": "GATC", "M.Eco1821Dam": "GATC", "M.Eco2525Dam": "GATC", "M.Eco3026Dam": "GATC", "M.Eco3029Dam": "GATC", "M.Eco3165Dam": "GATC", "M.Eco3936Dam": "GATC", "M.Eco9272Dam": "GATC", "M.Eco9276Dam": "GATC", "M.Eco9280Dam": "GATC", "M.Eco9387Dam": "GATC", "M.Eco11124Dam": "GATC", "M.Eco13452Dam": "GATC", "M.Eco14715Dam": "GATC", "M.Eco14723Dam": "GATC", "M.Eco29787Dam": "GATC", "M.Eco08Dcm": "CCWGG", "M.Eco089Dcm": "CCWGG", "M.Eco137Dcm": "CCWGG", "M.Eco0149Dcm": "CCWGG", "M.Eco150Dcm": "CCWGG", "M.Eco0151Dcm": "CCWGG", "M.Eco433Dcm": "CCWGG", "M.Eco590Dcm": "CCWGG", "M.Eco3763Dcm": "CCWGG", "M.Eco8426Dcm": "CCWGG", "EcoEI": "GAGNNNNNNNATGC", "EcoE1140I": "ACCYAC", "M.EcoEC149Dam": "GATC", "M.EcoE118Dam": "GATC", "M.EcoE455Dam": "GATC", "M.EcoE597Dam": "GATC", "M.EcoE686Dam": "GATC", "M.EcoE455LDam": "GATC", "M.EcoEc67Dam": "GATC", "M.EcoF5656Dam": "GATC", "M.EcoF9792Dam": "GATC", "M.EcoF144Dcm": "CCWGG", "EcoGIII": "CTGCAG", "EcoGVIII": "ACCACC", "M.EcoGDam": "GATC", "EcoHI": "CCSGG", "EcoHAI": "YGGCCR", "EcoHK31I": "YGGCCR", "EcoHSI": "GGTAAG", "EcoICRI": "GAGCTC", "M.EcoJA23PDam": "GATC", "M.EcoJA65PDam": "GATC", "M.EcoJA17PDcm": "CCWGG", "M.EcoJA65PDcm": "CCWGG", "EcoKI": "AACNNNNNNGTGC", "Eco71KI": "GGTCTC", "Eco75KI": "GRGCYC", "M.EcoKDam": "GATC", "EcoMII": "RTACNNNNGTG", "Eco57MI": "CTGRAG", "M.EcoMDam": "GATC", "EcoMVII": "CANCATC", "EcoNI": "CCTNNNNNAGG", "EcoNIH6II": "ATGAAG", "M.EcoNIH2Dcm": "CCWGG", "Eco95NR1II": "CRARCAG", "EcoO44I": "GGTCTC", "EcoO65I": "GGTNACC", "EcoO109I": "RGGNCCY", "EcoO128I": "GGTNACC", "EcoPI": "AGACC", "EcoP15I": "CAGCAG", "M.EcoP1Dam": "GATC", "EcoPT54I": "GAAABCC", "EcoRI": "GAATTC", "EcoRII": "CCWGG", "EcoRV": "GATATC", "EcoR124I": "GAANNNNNNRTCG", "EcoR124II": "GAANNNNNNNRTCG", "EcoRD2": "GAANNNNNNRTTC", "EcoRD3": "GAANNNNNNNRTTC", "M.EcoSC3Dam": "GATC", "M.EcoS448Dam": "GATC", "M.EcoS472Dam": "GATC", "M.EcoSanDam": "GATC", "EcoT14I": "CCWWGG", "EcoT22I": "ATGCAT", "EcoT38I": "GRGCYC", "EcoT88I": "GRGCYC", "EcoT93I": "GRGCYC", "EcoT95I": "GRGCYC", "EcoT104I": "CCWWGG", "M.EcoT1Dam": "GATC", "M.EcoT2Dam": "GATC", "M.EcoT4Dam": "GATC", "M.EcoTWDcm": "CCWGG", "M.EcoU1Dam": "GATC", "EcoVIII": "AAGCTT", "M.EcoVR50Dam": "GATC", "M.EcoVT2Dam": "GATC", "M.EcoW25Dam": "GATC", "Eco13kI": "CCNGG", "Eco21kI": "CCNGG", "Eco27kI": "CYCGRG", "Eco29kI": "CCGCGG", "Eco53kI": "GAGCTC", "Eco110kI": "CCTNAGG", "Eco137kI": "CCNGG", "EcoprrI": "CCANNNNNNNRTGC", "Efa1KI": "CCWGG", "Efa31KI": "GTCTC", "EgeI": "GGCGCC", "EheI": "GGCGCC", "EhoI": "CTGCAG", "Eli8509II": "CCGGAG", "ElmI": "GCNGC", "M.EmeR7I": "GANTC", "M.EpiCK41Dam": "GATC", "ErhI": "CCWWGG", "ErhB9I": "CGATCG", "ErhB9II": "CCWWGG", "ErhG4T10I": "CGANNNNNNTC", "ErhSE38I": "CGANNNNNNTC", "ErpI": "GGWCC", "EsaBC3I": "TCGA", "EsaBC4I": "GGCC", "EsaBS9I": "CGCG", "EsaLHCI": "GATC", "EsaMMI": "C", "EsaNI": "C", "M.EsaSP1Dam": "GATC", "EsaSSI": "GACCAC", "EsaWC1I": "GGCC", "EscI": "CTCGAG", "Ese3I": "CCGCGG", "Ese4I": "GRGCYC", "Ese6I": "CCGCGG", "Ese6II": "CCWGG", "EspI": "GCTNAGC", "Esp1I": "GGYRCC", "Esp2I": "CCWGG", "Esp3I": "CGTCTC", "Esp4I": "CTTAAG", "Esp5I": "GCCGGC", "Esp5II": "CTGCAG", "Esp6I": "GGYRCC", "Esp7I": "GCGCGC", "Esp8I": "GCGCGC", "Esp9I": "GGYRCC", "Esp10I": "GGYRCC", "Esp11I": "GGYRCC", "Esp12I": "GGYRCC", "Esp13I": "GGYRCC", "Esp14I": "GGYRCC", "Esp15I": "GGYRCC", "Esp16I": "CGTCTC", "Esp19I": "GGTACC", "Esp21I": "GGYRCC", "Esp22I": "GGYRCC", "Esp23I": "CGTCTC", "Esp24I": "CCWGG", "Esp25I": "GGYRCC", "Esp141I": "CTGCAG", "Esp638I": "GCNGC", "Esp1396I": "CCANNNNNTGG", "Esp3007I": "CAGAAG", "EspCI": "ACCTGC", "M.Esp169Dam": "GATC", "M.Esp193Dam": "GATC", "EspHK7I": "CCWGG", "EspHK16I": "YGGCCR", "EspHK22I": "CCWGG", "EspHK24I": "YGGCCR", "EspHK26I": "TCCGGA", "EspHK29I": "CYCGRG", "EspHK30I": "CCWGG", "M.EtaTXDDam": "GATC", "Exi27195I": "GCCGAC", "FaeI": "CATG", "FaiI": "YATR", "FalII": "CGCG", "FaqI": "GGGAC", "FatI": "CATG", "FauI": "CCCGC", "FauBII": "CGCG", "FauNDI": "CATATG", "FbaI": "TGATCA", "Fba202Z8II": "AGAAGG", "FblI": "GTMKAC", "FbrI": "GCNGC", "Fco1691IV": "GCVGAG", "FdiI": "GGWCC", "FdiII": "TGCGCA", "FgoI": "CTAG", "FinI": "GGGAC", "FinII": "CCGG", "FinSI": "GGCC", "FisI": "CTAG", "Fla104114II": "CCRGAG", "FmuI": "GGNCC", "FnuAI": "GANTC", "FnuAII": "GATC", "FnuCI": "GATC", "FnuDI": "GGCC", "FnuDII": "CGCG", "FnuDIII": "GCGC", "FnuEI": "GATC", "Fnu4HI": "GCNGC", "FokI": "GGATG", "Fph2801I": "GAGNNNNNRTG", "FpsJI": "CCGG", "FriOI": "GRGCYC", "FscI": "CCGCGG", "FseI": "GGCCGGCC", "FsfI": "CTGAAG", "FsiI": "RAATTY", "FspI": "TGCGCA", "FspII": "TTCGAA", "Fsp291I": "ACCTGC", "Fsp1604I": "CCWGG", "FspAI": "RTGCGCAY", "FspBI": "CTAG", "FspEI": "CC", "Fsp4HI": "GCNGC", "FspMI": "CGCG", "FspMSI": "GGWCC", "FspPK15I": "GARGAAG", "M.FspVDam": "GATC", "FssI": "GGWCC", "FsuI": "GACNNNGTC", "Fsy5DI": "TGGCCA", "M.Fsy5DDam": "GATC", "FtnUI": "GGHANNNNNNNTAG", "FtnUII": "CYYANNNNNNNCTC", "FtnUIII": "AGACC", "FtnUIV": "GATC", "FtnUV": "GAAACA", "FunI": "AGCGCT", "FunII": "GAATTC", "GalI": "CCGCGG", "GauT27I": "CGCGCAGG", "Gba708II": "ATGCAC", "GceI": "CCGCGG", "GceGLI": "CCGCGG", "GdiI": "AGGCCT", "GdoI": "GGATCC", "GeoICI": "GCAGC", "GinI": "GGATCC", "GlaI": "GCGC", "GluI": "GCNGC", "GmeII": "TCCAGG", "GobAI": "AGGCCT", "GoxI": "GGATCC", "GseI": "GGNCC", "GseII": "CTGCAG", "GseIII": "GGATCC", "GspI": "CAGCTG", "Gsp29I": "GRGCRAC", "GspAI": "GGWCC", "GspAII": "TGCGCA", "GstI": "GGATCC", "Gst1588I": "CYCGRG", "Gst1588II": "GATC", "GsuI": "CTGGAG", "HacI": "GATC", "HaeI": "WGGCCW", "HaeII": "RGCGCY", "HaeIII": "GGCC", "HalI": "GAATTC", "HalII": "CTGCAG", "Hal22I": "GAATTC", "M.Hal5920Dam": "GATC", "HapII": "CCGG", "HauII": "TGGCCANNNNNNNNNNN", "HbaII": "GCCCAG", "M.HdeA2CDam": "GATC", "M.Hde5DDam": "GATC", "M.HdeMI47Dam": "GATC", "HdeNY26I": "CGANNNNNNTCC", "M.HdeNY26Dam": "GATC", "HdeZA17I": "GCANNNNNNTCC", "M.Hdu297I": "GATC", "HgaI": "GACGC", "HgiI": "GRCGYC", "HgiAI": "GWGCWC", "HgiBI": "GGWCC", "HgiCI": "GGYRCC", "HgiCII": "GGWCC", "HgiCIII": "GTCGAC", "HgiDI": "GRCGYC", "HgiDII": "GTCGAC", "HgiEI": "GGWCC", "HgiEII": "ACCNNNNNNGGT", "HgiGI": "GRCGYC", "HgiHI": "GGYRCC", "HgiHII": "GRCGYC", "HgiHIII": "GGWCC", "HgiJI": "GGWCC", "HgiJII": "GRGCYC", "HgiS21I": "CCSGG", "HgiS22I": "CCSGG", "HhaI": "GCGC", "HhaII": "GANTC", "M.HhaIII": "GATC", "HhdI": "CCWGG", "HhgI": "GGCC", "Hin1I": "GRCGYC", "Hin1II": "CATG", "Hin2I": "CCGG", "Hin3I": "CCSGG", "Hin4II": "CCTTC", "Hin5I": "CCGG", "Hin5II": "GGNCC", "Hin5III": "AAGCTT", "Hin6I": "GCGC", "Hin7I": "GCGC", "Hin8I": "GRCGYC", "Hin8II": "CATG", "Hin173I": "AAGCTT", "M.Hin723II": "GATC", "Hin1056I": "CGCG", "Hin1076III": "AAGCTT", "Hin1160II": "GTYRAC", "Hin1161II": "GTYRAC", "M.HinC486II": "GATC", "M.Hin1209Dam": "GATC", "M.Hin2866Dam": "GATC", "M.Hin8143Dam": "GATC", "M.HinGG2III": "GATC", "HinGUI": "GCGC", "HinGUII": "GGATG", "HinHI": "RGCGCY", "M.HinHP1Dam": "GATC", "M.HinHP2Dam": "GATC", "M.HinHia1III": "GATC", "HinJCI": "GTYRAC", "HinJCII": "AAGCTT", "HinP1I": "GCGC", "HinS1I": "GCGC", "HinS2I": "GCGC", "HinSAFI": "AAGCTT", "HinbIII": "AAGCTT", "HincII": "GTYRAC", "HindII": "GTYRAC", "HindIII": "AAGCTT", "M.HindDam": "GATC", "HineI": "CGAAT", "HinfI": "GANTC", "Hinf232I": "CGAAT", "HjaI": "GATATC", "HpaI": "GTTAAC", "HpaII": "CCGG", "HphI": "GGTGA", "HpyII": "GAAGA", "HpyIV": "GANTC", "HpyV": "TCGA", "HpyVIII": "CCGG", "Hpy8I": "GTNNAC", "Hpy8II": "GTSAC", "Hpy8III": "GWGCWC", "Hpy26I": "TGCA", "Hpy26II": "TCGA", "Hpy30XI": "CCATC", "Hpy30XIII": "AGGAG", "Hpy51I": "GTSAC", "Hpy99I": "CGWCG", "Hpy99II": "GTSAC", "Hpy99III": "GCGC", "Hpy99IV": "CCNNGG", "Hpy99XIII": "GCCTA", "Hpy99XIV": "GGWTAA", "Hpy99XIV-mut1": "GGWCNA", "Hpy99XVI": "RTAYNNNNNRTAY", "Hpy99XXII": "TCANNNNNNTRG", "Hpy166I": "TCNGA", "Hpy166II": "GTNNAC", "Hpy166III": "CCTC", "Hpy178II": "GAAGA", "Hpy178III": "TCNNGA", "Hpy178VI": "GGATG", "Hpy178VII": "GGCC", "Hpy188I": "TCNGA", "Hpy188III": "TCNNGA", "Hpy298XI": "CYANNNNNNNTRG", "Hpy299IX": "CCGG", "Hpy299XII": "CYANNNNNNNTRG", "Hpy300VIII": "AGGAG", "Hpy300XI": "CCTYNA", "Hpy303I": "GTAC", "Hpy312I": "GTAC", "Hpy401I": "GTAC", "Hpy421I": "GTAC", "Hpy423I": "GTAC", "Hpy471I": "GTAC", "Hpy501I": "GTAC", "HpyAII": "GAAGA", "HpyAIII": "GATC", "HpyAIV": "GANTC", "HpyAV": "CCTTC", "HpyAXIV": "GCGTA", "HpyAXVI-mut1": "CRTTAA", "HpyAXVI-mut2": "CRTCNA", "HpyAXVIII": "GGANNAG", "Hpy87AI": "GANTC", "HpyBI": "GTAC", "HpyBII": "GTNNAC", "HpyCI": "GATATC", "HpyC1I": "CCATC", "HpyCH4I": "CATG", "HpyCH4II": "CTNAG", "HpyCH4III": "ACNGT", "HpyCH4IV": "ACGT", "HpyCH4V": "TGCA", "HpyCH4VI": "TCNNGA", "HpyF1I": "GTSAC", "HpyF2I": "CTRYAG", "HpyF2II": "GANTC", "HpyF3I": "CTNAG", "HpyF4I": "GTSAC", "HpyF4II": "CTNAG", "HpyF5I": "CTNAG", "HpyF5II": "ACNGT", "HpyF6I": "GGATG", "HpyF6II": "GTSAC", "HpyF6III": "CTNAG", "HpyF7I": "CTNAG", "HpyF7II": "GWGCWC", "HpyF7III": "GTNNAC", "HpyF9I": "GTSAC", "HpyF9II": "CTNAG", "HpyF9III": "ACNGT", "HpyF10I": "GCGC", "HpyF10II": "GANTC", "HpyF10III": "CCNNGG", "HpyF10IV": "GTAC", "HpyF10V": "GGCC", "HpyF10VI": "GCNNNNNNNGC", "HpyF11I": "CTNAG", "HpyF11II": "TCNGA", "HpyF12I": "ACNGT", "HpyF12II": "TCNGA", "HpyF13I": "GTSAC", "HpyF13II": "CTNAG", "HpyF13III": "ACGT", "HpyF13IV": "GTAC", "HpyF14I": "CGCG", "HpyF14II": "GTNNAC", "HpyF14III": "TCGA", "HpyF15I": "CGCG", "HpyF15II": "TCNGA", "HpyF16I": "TCGA", "HpyF16II": "TCNNGA", "HpyF17I": "TCNGA", "HpyF18I": "GANTC", "HpyF19I": "CTNAG", "HpyF19II": "TCNGA", "HpyF19III": "TCNNGA", "HpyF20I": "ACNGT", "HpyF21I": "CTNAG", "HpyF21II": "GTAC", "HpyF22I": "ACNGT", "HpyF22II": "CTNAG", "HpyF22III": "TCNNGA", "HpyF23I": "TCGA", "HpyF24I": "TCGA", "HpyF24II": "CTNAG", "HpyF25I": "CTNAG", "HpyF25II": "GTSAC", "HpyF26I": "CGCG", "HpyF26II": "GGCC", "HpyF26III": "TCGA", "HpyF27I": "CTNAG", "HpyF27II": "TCNGA", "HpyF28I": "TCNGA", "HpyF29I": "GGCC", "HpyF30I": "TCGA", "HpyF30II": "CTNAG", "HpyF31I": "GTAC", "HpyF31II": "GTSAC", "HpyF32I": "CTNAG", "HpyF33I": "TCNGA", "HpyF33II": "GGCC", "HpyF34I": "CTNAG", "HpyF34II": "GTSAC", "HpyF35I": "TCGA", "HpyF35II": "ACGT", "HpyF35III": "ACNGT", "HpyF35IV": "GTSAC", "HpyF36I": "GTSAC", "HpyF36II": "GTAC", "HpyF36III": "TGCA", "HpyF36IV": "GDGCHC", "HpyF37I": "CTNAG", "HpyF38I": "GANTC", "HpyF38II": "TGCA", "HpyF40I": "ACNGT", "HpyF40II": "TCGA", "HpyF40III": "GTSAC", "HpyF41I": "ACNGT", "HpyF41II": "CTNAG", "HpyF42I": "GGCC", "HpyF42II": "ACNGT", "HpyF42III": "TCNGA", "HpyF42IV": "TCGA", "HpyF43I": "CCGG", "HpyF44I": "GANTC", "HpyF44II": "GGNNCC", "HpyF44III": "TGCA", "HpyF44IV": "TCNNGA", "HpyF44V": "GTAC", "HpyF45I": "TCGA", "HpyF45II": "TGCA", "HpyF46I": "ACNGT", "HpyF46II": "GWGCWC", "HpyF46III": "GTNNAC", "HpyF46IV": "TCNGA", "HpyF46V": "GGCC", "HpyF47I": "GDGCHC", "HpyF48I": "GTSAC", "HpyF48II": "ACNGT", "HpyF48III": "TGCA", "HpyF49I": "TCGA", "HpyF49II": "GTSAC", "HpyF49III": "GTNNAC", "HpyF49IV": "GGCC", "HpyF49V": "TGCA", "HpyF50I": "GTNNAC", "HpyF50II": "TCNGA", "HpyF51I": "GTSAC", "HpyF51II": "ACNGT", "HpyF52I": "TCGA", "HpyF52II": "CGCG", "HpyF52III": "GTAC", "HpyF53I": "GGCC", "HpyF53II": "GTAC", "HpyF54I": "ACNGT", "HpyF55I": "ACNGT", "HpyF55II": "GANTC", "HpyF56I": "ACNGT", "HpyF57I": "GGCC", "HpyF58I": "ACNGT", "HpyF59I": "CTNAG", "HpyF59II": "GTAC", "HpyF59III": "TCGA", "HpyF60I": "GANTC", "HpyF60II": "CTNAG", "HpyF61I": "TCNGA", "HpyF61II": "CCNNGG", "HpyF61III": "CGWCG", "HpyF62I": "ACNGT", "HpyF62II": "TCGA", "HpyF62III": "GTSAC", "HpyF63I": "GGCC", "HpyF64I": "TCGA", "HpyF64II": "ACNGT", "HpyF64III": "TCNGA", "HpyF64IV": "CGCG", "HpyF64V": "CTNAG", "HpyF65I": "ACNGT", "HpyF65II": "TCGA", "HpyF65III": "GTAC", "HpyF66I": "GGNCC", "HpyF66II": "CTNAG", "HpyF66III": "GTAC", "HpyF66IV": "TCGA", "HpyF67I": "CTNAG", "HpyF67II": "TGCA", "HpyF67III": "GGATG", "HpyF67IV": "CCNNGG", "HpyF68I": "ACNGT", "HpyF68II": "CTNAG", "HpyF69I": "ACNGT", "HpyF69II": "GGCC", "HpyF70I": "CTNAG", "HpyF71I": "TCGA", "HpyF71II": "GGNCC", "HpyF71III": "GANTC", "HpyF72I": "GGCC", "HpyF72II": "CTNAG", "HpyF72III": "GANTC", "HpyF73I": "GGNNCC", "HpyF73II": "TCGA", "HpyF73III": "GGCC", "HpyF73IV": "GGNCC", "HpyF74I": "ACNGT", "HpyF74II": "ACGT", "HpyGI": "TCNNGA", "HpyGII": "TGCA", "HpyHI": "CTNAG", "HpyHII": "GTAC", "HpyHPK5I": "CTNAG", "HpyHPK5II": "GATC", "HpyJP26I": "TGCA", "HpyJP26II": "TCGA", "HpyNI": "CCNGG", "HpyNSH57I": "GTAC", "HpyNSH57II": "TCNNGA", "HpyS300XIII": "GCCTA", "HpyS300XIV": "TCANNNNNNTRG", "HpySE526I": "ACGT", "HpyUM032XIII": "CYANNNNNNNTRG", "HpyUM032XIII-mut1": "CYANNNNNNNTTC", "HpyUM032XIV": "GAAAG", "Hsa13891I": "GATC", "HsoI": "GCGC", "Hso63250II": "CGANNNNNRTAY", "Hso63250IV": "AACNNNNNGTT", "Hso63255I": "CGANNNNNRTAY", "Hso63368I": "CGANNNNNRTAY", "Hso63368II": "AACNNNNNGTT", "Hso63369II": "CGANNNNNRTAY", "Hso63370II": "CGANNNNNRTAY", "Hso63373III": "CGANNNNNRTAY", "Hso63374II": "CGANNNNNRTAY", "Hsp2I": "GGWCC", "Hsp92I": "GRCGYC", "Hsp92II": "CATG", "HspAI": "GCGC", "M.HspF0629I": "GATC", "HspMHR1II": "GAGCAGC", "HsuI": "AAGCTT", "ItaI": "GCNGC", "Jma19592I": "GTATNAC", "Jma19592II": "GRGCRAC", "Jsp2502II": "GRNGAAT", "M.Kaq16071I": "GATC", "KasI": "GGCGCC", "Kaz48kI": "RGGNCCY", "KflI": "GGGWCCC", "Kor51II": "RTCGAG", "M.Kor51Dam": "GATC", "KoxI": "GGTNACC", "KoxII": "GRGCYC", "Kox165I": "CCWGG", "M.Kox28Dam": "GATC", "M.Kox147Dam": "GATC", "KoyI": "GTCGAC", "Kpl79I": "CGATCG", "KpnI": "GGTACC", "Kpn2I": "TCCGGA", "Kpn10I": "CCWGG", "Kpn12I": "CTGCAG", "Kpn13I": "CCWGG", "Kpn14I": "CCWGG", "Kpn16I": "CCWGG", "Kpn19I": "CCGCGG", "Kpn30I": "GCGCGC", "Kpn156V": "CRTGATT", "Kpn160II": "CANCATC", "Kpn327I": "GACATC", "Kpn378I": "CCGCGG", "Kpn39795III": "CANCATC", "KpnAI": "GGCANNNNNNTTC", "M.KpnABFPVDam": "GATC", "M.KpnABFQVDam": "GATC", "M.KpnAR161Dam": "GATC", "KpnBI": "CAAANNNNNNRTCA", "M.Kpn21Dam": "GATC", "M.Kpn76Dam": "GATC", "M.Kpn156Dam": "GATC", "M.Kpn160Dam": "GATC", "M.Kpn204Dam": "GATC", "M.Kpn1215Dam": "GATC", "M.Kpn1936Dam": "GATC", "M.Kpn2297Dam": "GATC", "M.Kpn5046Dam": "GATC", "M.Kpn5047Dam": "GATC", "M.Kpn5048Dam": "GATC", "M.Kpn5052Dam": "GATC", "M.Kpn5053Dam": "GATC", "M.Kpn5054Dam": "GATC", "M.Kpn5055Dam": "GATC", "M.Kpn5056Dam": "GATC", "M.Kpn5057Dam": "GATC", "M.Kpn7427Dam": "GATC", "M.Kpn7761Dam": "GATC", "M.Kpn7799Dam": "GATC", "M.Kpn8172Dam": "GATC", "M.Kpn8839Dam": "GATC", "M.Kpn8849Dam": "GATC", "M.Kpn8883Dam": "GATC", "M.Kpn8895Dam": "GATC", "M.Kpn9127Dam": "GATC", "M.Kpn9129Dam": "GATC", "M.Kpn9130Dam": "GATC", "M.Kpn9132Dam": "GATC", "M.Kpn9133Dam": "GATC", "M.Kpn9135Dam": "GATC", "M.Kpn9136Dam": "GATC", "M.Kpn9137Dam": "GATC", "M.Kpn9138Dam": "GATC", "M.Kpn9139Dam": "GATC", "M.Kpn9142Dam": "GATC", "M.Kpn9144Dam": "GATC", "M.Kpn9150Dam": "GATC", "M.Kpn9151Dam": "GATC", "M.Kpn9154Dam": "GATC", "M.Kpn9156Dam": "GATC", "M.Kpn9159Dam": "GATC", "M.Kpn9163Dam": "GATC", "M.Kpn9166Dam": "GATC", "M.Kpn9167Dam": "GATC", "M.Kpn9169Dam": "GATC", "M.Kpn9174Dam": "GATC", "M.Kpn9176Dam": "GATC", "M.Kpn9177Dam": "GATC", "M.Kpn9182Dam": "GATC", "M.Kpn9184Dam": "GATC", "M.Kpn9494Dam": "GATC", "M.Kpn9495Dam": "GATC", "M.Kpn9498Dam": "GATC", "M.Kpn9504Dam": "GATC", "M.Kpn9601Dam": "GATC", "M.Kpn9616Dam": "GATC", "M.Kpn9617Dam": "GATC", "M.Kpn9632Dam": "GATC", "M.Kpn9633Dam": "GATC", "M.Kpn9635Dam": "GATC", "M.Kpn9636Dam": "GATC", "M.Kpn9637Dam": "GATC", "M.Kpn9656Dam": "GATC", "M.Kpn9657Dam": "GATC", "M.Kpn9660Dam": "GATC", "M.Kpn9661Dam": "GATC", "M.Kpn9662Dam": "GATC", "M.Kpn10313Dam": "GATC", "M.Kpn11692Dam": "GATC", "M.Kpn11697Dam": "GATC", "M.Kpn13368Dam": "GATC", "M.Kpn13672Dam": "GATC", "M.Kpn13673Dam": "GATC", "M.Kpn13674Dam": "GATC", "M.Kpn13675Dam": "GATC", "M.Kpn13676Dam": "GATC", "M.Kpn13684Dam": "GATC", "M.Kpn13688Dam": "GATC", "M.Kpn13691Dam": "GATC", "M.Kpn13694Dam": "GATC", "M.Kpn13695Dam": "GATC", "M.Kpn13696Dam": "GATC", "M.Kpn13697Dam": "GATC", "M.Kpn13698Dam": "GATC", "M.Kpn13700Dam": "GATC", "M.Kpn13701Dam": "GATC", "M.Kpn43816Dam": "GATC", "M.Kpn154414Dam": "GATC", "M.Kpn182374Dam": "GATC", "KpnK14I": "GGTACC", "KpnNH25III": "CTRGAG", "KpnNIH30III": "GTTCNAC", "KpnNIH50I": "GCYAAG", "M.KpnXen39Dam": "GATC", "Kpn2kI": "CCNGG", "Kpn49kI": "GAATTC", "Kpn49kII": "CCSGG", "M.Kpnkp3Dam": "GATC", "M.Kpns9645Dam": "GATC", "KroI": "GCCGGC", "KspI": "CCGCGG", "Ksp22I": "TGATCA", "Ksp632I": "CTCTTC", "KspAI": "GTTAAC", "KspHK12I": "CCWGG", "KspHK14I": "CCWGG", "KspHK15I": "YGGCCR", "KteAI": "CCCGGG", "M.Kva120EDam": "GATC", "M.KvaS01Dam": "GATC", "Kzo9I": "GATC", "Kzo49I": "GGWCC", "M.Lam395Dam": "GATC", "Lba2029III": "CYAAANG", "Lbr124II": "CATCNAC", "Lbr449I": "AGCCAG", "LcaI": "ATCGAT", "Lde4408II": "ACAAAG", "LfeI": "GCAGC", "LfuRB21I": "AAGGAG", "LguI": "GCTCTTC", "Lla497I": "CCWGG", "LlaAI": "GATC", "LlaBI": "CTRYAG", "LlaBIII": "TNAGCC", "LlaCI": "AAGCTT", "LlaDI": "AGTACT", "LlaDII": "GCNGC", "LlaDCHI": "GATC", "LlaGI": "CTNGAYG", "LlaG2I": "GCTAGC", "LlaG50I": "CCGTKA", "LlaKR2I": "GATC", "LlaMI": "CCNGG", "Lmo370I": "AGCGCCG", "Lmo540I": "TAGRAG", "Lmo911II": "TAGRAG", "Lmo6905I": "TAGRAG", "Lmo6907II": "TAGRAG", "Lmo11238I": "TAGRAG", "Lmo23459II": "TAGRAG", "Lmo11AII": "TAGRAG", "LmoH7I": "GTATCC", "Lmu60I": "CCTNAGG", "M.Lph93Dam": "GATC", "LplI": "ATCGAT", "Lpl116III": "CAGRAG", "Lpl520I": "AGCCAG", "Lpl1004II": "AGGRAG", "LplK25I": "AGGRAG", "LpnI": "RGCGCY", "M.Lpn2005I": "GATC", "M.Lpn11984I": "GATC", "M.Lpn11986I": "GATC", "M.Lpn12008I": "GATC", "M.Lpn12025I": "GATC", "M.Lpn12181I": "GATC", "LpnPI": "CCDG", "LraI": "GAATTC", "Lra68I": "GTTCNAG", "LsaDS4I": "TGGAAT", "LsaM18I": "GCWGC", "LspI": "TTCGAA", "Lsp48III": "AGCACC", "Lsp1109I": "GCAGC", "Lsp1109II": "GATC", "Lsp1270I": "RCATGY", "Lsp6406VI": "CRAGCAC", "M.LspNIH3Dam": "GATC", "LweI": "GCATC", "MabI": "ACCWGGT", "MaeI": "CTAG", "MaeII": "ACGT", "MaeIII": "GTNAC", "MaeK81I": "CGTACG", "MaeK81II": "GGNCC", "Mae7806SLI": "AAGGAG", "Maf25II": "CACGCAG", "MalI": "GATC", "MamI": "GATNNNNATC", "M.Mam123II": "GANTC", "MaqI": "CRTTGAC", "MarI": "AGCT", "MauI": "CTGCAG", "MauAI": "GCCGGC", "MauBI": "CGCGCGCG", "MavI": "CTCGAG", "Mba11I": "AGGCGA", "MbaR4I": "GCWGC", "MboI": "GATC", "MboII": "GAAGA", "Mbo26II": "CACGCAG", "Mbo30II": "CACGCAG", "McaI": "CTCGAG", "McaAI": "GGCGCC", "McaCI": "CCATC", "McaTI": "GCGCGC", "MchI": "GGCGCC", "MchAI": "GCGGCCGC", "MchAII": "GGCC", "MchCM4I": "GAGGAG", "McrI": "CGRYCG", "Mcr10I": "GAAGNNNNNCTC", "MecI": "CTCGAG", "Mel3JI": "GATC", "Mel5JI": "GATC", "Mel7JI": "GATC", "Mel4OI": "GATC", "Mel5OI": "GATC", "Mel2TI": "GATC", "Mel5TI": "GATC", "MeuI": "GATC", "M.MexAMI": "GANTC", "MfeI": "CAATTG", "MflI": "RGATCY", "MfoI": "GGWCC", "MfoAI": "GGCC", "Mgl14481I": "CCSGG", "MgoI": "GATC", "MhaI": "CTCGAG", "Mha185III": "CTGAAG", "Mha187III": "CTGAAG", "MhaAI": "CTGCAG", "MhlI": "GDGCHC", "MhoI": "GGNCC", "Mho2111I": "AGCT", "Mho2965I": "GCGC", "MhyGDL1III": "GATC", "MisI": "GCCGGC", "MizI": "CTGCAG", "MjaI": "CTAG", "MjaII": "GGNCC", "MjaIII": "GATC", "MjaIV": "GTNNAC", "MjaV": "GTAC", "MkaDII": "GAGAYGT", "MkiI": "AAGCTT", "MkrI": "CTGCAG", "MkrAI": "GATC", "MlaI": "TTCGAA", "MlaAI": "CTCGAG", "MleI": "GGATCC", "MliI": "GGWCC", "MlsI": "TGGCCA", "MltI": "AGCT", "MluI": "ACGCGT", "Mlu23I": "GGATCC", "Mlu31I": "TGGCCA", "Mlu40I": "GDGCHC", "Mlu211III": "AGCCCA", "Mlu1106I": "RGGWCCY", "Mlu2300I": "CCWGG", "Mlu9273I": "TCGCGA", "Mlu9273II": "GCCGGC", "MluB2I": "TCGCGA", "MluCI": "AATT", "MluNI": "TGGCCA", "MlyI": "GAGTC", "Mly113I": "GGCGCC", "MmaI": "CTGCAG", "MmeI": "TCCRAC", "MmeII": "GATC", "MmoSTI": "CCNGG", "Mmu5I": "GATC", "MmuP2I": "GATC", "MmyCI": "TGAG", "MmyCVI": "CCATC", "MniI": "GGCC", "MniII": "CCGG", "MnlI": "CCTC", "MnnI": "GTYRAC", "MnnII": "GGCC", "MnnIV": "GCGC", "MnoI": "CCGG", "MnoIII": "GATC", "MosI": "GATC", "Mox20I": "TGGCCA", "M.MpeI": "CG", "MphI": "CCWGG", "Mph1103I": "ATGCAT", "Mph1103II": "GATC", "Mpr154I": "CCGCGG", "MprAceLVII": "GATCAG", "MpsI": "CCWGG", "MpuI": "CTCGAG", "MraI": "CCGCGG", "MreI": "CGCCGGCG", "MrhI": "CTCGAG", "MroI": "TCCGGA", "MroNI": "GCCGGC", "MroXI": "GAANNNNTTC", "M.Mru1279VII": "GATC", "MsaI": "GGCGCC", "MscI": "TGGCCA", "MscAI": "CTCGAG", "MseI": "TTAA", "MsiI": "CTCGAG", "MslI": "CAYNNNNRTG", "MspI": "CCGG", "M.Msp8II": "GANTC", "Msp11I": "CTGCAG", "Msp16I": "TGGCCA", "Msp17I": "GRCGYC", "Msp20I": "TGGCCA", "Msp23I": "TCTAGA", "Msp23II": "CTCGAG", "Msp24I": "GGNCC", "Msp67I": "CCNGG", "Msp67II": "GATC", "Msp199I": "CCGG", "MspAI": "GGWCC", "MspA1I": "CMGCKG", "MspAK21I": "GCWGC", "MspBI": "GATC", "MspB4I": "GGYRCC", "MspCI": "CTTAAG", "MspCY2I": "AGCGCC", "MspF392I": "CCCAATV", "MspGI": "GCCGGC", "MspI7II": "ACGRAG", "MspI7IV": "GCMGAAG", "MspJI": "CNNR", "MspNI": "GGWCC", "MspNII": "RGATCY", "MspR9I": "CCNGG", "MspSC27II": "CCGCGAC", "MspSWI": "ATTTAAAT", "MspV281I": "GWGCWC", "MspYI": "YACGTR", "MssI": "GTTTAAAC", "MstI": "TGCGCA", "MstII": "CCTNAGG", "MteI": "GCGCNGCGC", "Mte37I": "C", "MthI": "GATC", "Mth1047I": "GATC", "MthAI": "GATC", "MthBI": "GGNCC", "MthFI": "CTAG", "MthTI": "GGCC", "MthZI": "CTAG", "Mtu18II": "CACGCAG", "Mtu445II": "CACGCAG", "Mtu446II": "CACGCAG", "Mtu447III": "CACGCAG", "Mtu448II": "CACGCAG", "Mtu1007III": "CACGCAG", "Mtu1008III": "CACGCAG", "Mtu1009III": "CACGCAG", "Mtu1451III": "CACGCAG", "Mtu1452III": "CACGCAG", "Mtu1453III": "CACGCAG", "Mtu1454III": "CACGCAG", "Mtu1456II": "CACGCAG", "Mtu1457III": "CACGCAG", "Mtu1459III": "CACGCAG", "Mtu1460III": "CACGCAG", "Mtu2242II": "CACGCAG", "Mtu2279II": "CACGCAG", "Mtu12961II": "CACGCAG", "Mtu22115I": "CACGCAG", "Mtu26105I": "CACGCAG", "Mtu37004I": "CACGCAG", "MtuBlII": "CACGCAG", "MtuHN878II": "CACGCAG", "MunI": "CAATTG", "MvaI": "CCWGG", "Mva16I": "TTCGAA", "Mva1261III": "CTANNNNNNRTTC", "Mva1312II": "GCATC", "MvaAI": "CGCG", "MvnI": "CGCG", "MvrI": "CGATCG", "MvsI": "GGTACC", "MvsAI": "GGTACC", "MvsBI": "GGTACC", "MvsCI": "GGTACC", "MvsDI": "GGTACC", "MvsEI": "GGTACC", "MwhI": "GTTAAC", "MwoI": "GCNNNNNNNGC", "MxaI": "GAGCTC", "MziI": "CAGCTG", "NaeI": "GCCGGC", "Nal45188II": "ACCAGC", "NamI": "GGCGCC", "Nan19573I": "GATATC", "Nan19573II": "GATC", "NarI": "GGCGCC", "Nar7I": "CTGRAG", "NasI": "CTGCAG", "NasBI": "GGATCC", "NasSI": "GAGCTC", "NbaI": "GCCGGC", "NblI": "CGATCG", "NbrI": "GCCGGC", "Nbr128II": "ACCGAC", "NcaI": "GANTC", "NciI": "CCSGG", "NciAI": "GATC", "NcoI": "CCATGG", "NcrI": "AGATCT", "NcuI": "GAAGA", "NcuII": "CCCG", "NdaI": "GGCGCC", "NdeI": "CATATG", "NdeII": "GATC", "NflI": "GATC", "NflAI": "GATATC", "NflAII": "GATC", "NflBI": "GATC", "NflHI": "GCGGAG", "NflHII": "CCGG", "NgbI": "CTGCAG", "NgoAI": "RGCGCY", "NgoAII": "GGCC", "NgoAIII": "CCGCGG", "NgoAIV": "GCCGGC", "NgoAV": "GCANNNNNNNNTGC", "NgoAVI": "GATC", "NgoAVII": "GCCGC", "NgoAX": "CCACC", "NgoAXVI": "GGTGA", "NgoBI": "RGCGCY", "NgoBV": "GGNNCC", "NgoBVIII": "GGTGA", "M.NgoBXII": "GCNGC", "NgoCI": "RGCGCY", "NgoCII": "GGCC", "NgoDIII": "CCGCGG", "M.NgoDCXV": "GCCHR", "NgoDVIII": "GGTGA", "NgoDXIV": "GATC", "M.NgoEI": "RGCGCY", "NgoEII": "GCGC", "NgoFVII": "GCSGC", "NgoGI": "RGCGCY", "M.NgoGII": "GGCC", "NgoGIII": "CCGCGG", "NgoGV": "GGNNCC", "M.NgoHVIII": "GGTGA", "NgoJI": "RGCGCY", "NgoJIII": "CCGCGG", "NgoJVIII": "GGTGA", "NgoKIII": "CCGCGG", "NgoMI": "RGCGCY", "NgoMIII": "CCGCGG", "NgoMIV": "GCCGGC", "NgoMV": "GGNNCC", "NgoMVIII": "GGTGA", "NgoMX": "CCACC", "NgoNII": "GGCC", "NgoPII": "GGCC", "NgoPIII": "CCGCGG", "NgoSII": "GGCC", "NgoTII": "GGCC", "NgoWI": "RGCGCY", "NhaXI": "CAAGRAG", "NheI": "GCTAGC", "NhoI": "GCWGC", "NlaI": "GGCC", "NlaII": "GATC", "NlaIII": "CATG", "NlaIV": "GGNNCC", "NlaCI": "CATCAC", "NlaDI": "GATC", "NlaDII": "GGNCC", "NlaDIII": "CCGCGG", "NlaSI": "CCGCGG", "NlaSII": "GRCGYC", "NliI": "CYCGRG", "NliII": "GGWCC", "Nli3877I": "CYCGRG", "Nli3877II": "GGWCC", "NmeAII": "GATC", "NmeAIII": "GCCGAG", "NmeA6CIII": "GCCGAC", "NmeBI": "GACGC", "NmeBL859I": "GATC", "NmeCI": "GATC", "NmeRI": "CAGCTG", "NmeSI": "AGTACT", "NmiI": "GGTACC", "NmuI": "GCCGGC", "NmuAI": "CYCGRG", "NmuAII": "GGWCC", "NmuCI": "GTSAC", "NmuDI": "GATC", "NmuEI": "GATC", "NmuEII": "GGNCC", "NmuFI": "GCCGGC", "NmuSI": "GGNCC", "NocI": "CTGCAG", "NopI": "GTCGAC", "NotI": "GCGGCCGC", "NovII": "GANTC", "NpeUS61II": "GATCGAC", "NphI": "GATC", "NruI": "TCGCGA", "NruGI": "GACNNNNNGTC", "NsbI": "TGCGCA", "NsiI": "ATGCAT", "NsiAI": "GATC", "NsiCI": "GATATC", "NsiHI": "GANTC", "NsoJS138I": "CAGCTG", "NspI": "RCATGY", "NspII": "GDGCHC", "NspIII": "CYCGRG", "NspIV": "GGNCC", "NspV": "TTCGAA", "Nsp7121I": "GGNCC", "Nsp29132I": "TTCGAA", "Nsp29132II": "GGATCC", "NspAI": "GATC", "NspBI": "TTCGAA", "NspBII": "CMGCKG", "NspDI": "CYCGRG", "NspDII": "GGWCC", "NspEI": "CYCGRG", "NspFI": "TTCGAA", "NspGI": "GGWCC", "NspHI": "RCATGY", "NspHII": "GGWCC", "NspHIII": "TGCGCA", "NspJI": "TTCGAA", "NspKI": "GGWCC", "NspLI": "TGCGCA", "NspLII": "GGNCC", "NspLKI": "GGCC", "NspMI": "TGCGCA", "NspMACI": "AGATCT", "NspSAI": "CYCGRG", "NspSAII": "GGTNACC", "NspSAIII": "CCATGG", "NspSAIV": "GGATCC", "NspWI": "GCCGGC", "NsuI": "GATC", "NsuDI": "GATC", "NtaI": "GACNNNGTC", "NtaSI": "AGGCCT", "NtaSII": "GCCGGC", "Nti1539I": "ACCTGC", "NunII": "GGCGCC", "ObaBS10I": "ACGAG", "OchI": "GGCC", "OcoI": "CTCGAG", "OfoI": "CYCGRG", "OgrI": "CAACNAC", "OkrAI": "GGATCC", "OliI": "CACNNNNGTG", "OmiAI": "GRGCYC", "OmiBI": "GRGCYC", "OmiBII": "GTMKAC", "M.OpiAA2I": "GANTC", "M.Opr1222Dam": "GATC", "M.OpsK8I": "GANTC", "OspI": "TTCGAA", "M.Osp2203Dam": "GATC", "OspHL35III": "YAGGAG", "OtuI": "AGCT", "OtuNI": "AGCT", "OxaI": "AGCT", "OxaNI": "CCTNAGG", "PabI": "GTAC", "PacI": "TTAATTAA", "PacII": "CCCNNNNNRTTGY", "PacIII": "GTAATC", "Pac25I": "CCCGGG", "Pac1110I": "GGATCC", "Pac1110II": "GATATC", "Pac19842II": "CCTTGA", "PaeI": "GCATGC", "Pae7I": "CCGCGG", "Pae8I": "CTGCAG", "Pae9I": "CTGCAG", "Pae14I": "CTGCAG", "Pae15I": "CTGCAG", "Pae17I": "CCGCGG", "Pae22I": "CTGCAG", "Pae24I": "CTGCAG", "Pae25I": "CTGCAG", "Pae26I": "CTGCAG", "Pae36I": "CCGCGG", "Pae39I": "CTGCAG", "Pae40I": "CTGCAG", "Pae41I": "CTGCAG", "Pae42I": "CCGCGG", "Pae43I": "CCGCGG", "Pae44I": "CCGCGG", "Pae95II": "GATCGAG", "Pae177I": "GGATCC", "Pae181I": "CCSGG", "PaeAI": "CCGCGG", "PaeBI": "CCCGGG", "PaeCI": "GCATGC", "PaeHI": "GRGCYC", "PaeHII": "CTGCAG", "PaePA99III": "AAGAYC", "PaePS50I": "GCNNGC", "PaeQI": "CCGCGG", "PaeR7I": "CTCGAG", "Pae2kI": "AGATCT", "Pae5kI": "CCGCGG", "Pae14kI": "CCGCGG", "Pae17kI": "CAGCTG", "Pae18kI": "AGATCT", "PagI": "TCATGA", "M.Pag9381Dam": "GATC", "PaiI": "GGCC", "PalI": "GGCC", "Pal408I": "CCRTGAG", "PalAI": "GGCGCGCC", "PamI": "TGCGCA", "PamII": "GRCGYC", "Pam7686I": "CCATGG", "Pam7902I": "GCNNGC", "PanI": "CTCGAG", "Pan13I": "GCWGC", "ParI": "TGATCA", "PasI": "CCCWGGG", "PatAI": "GGCGCC", "PatTI": "C", "PauI": "GCGCGC", "PauAI": "RCATGY", "PauAII": "TTTAAA", "Pba2294I": "GTAAG", "PboCI": "GGATCC", "PbrTI": "GATC", "PbuJKI": "GGATG", "PbuMZI": "ATTAAT", "PcaII": "GACGAG", "Pca17AI": "CCWGG", "M.Pca67Dam": "GATC", "PceI": "AGGCCT", "PciI": "ACATGT", "PciSI": "GCTCTTC", "PcoI": "GAACNNNNNNTCC", "Pcr308II": "CCAAAG", "PcsI": "WCGNNNNNNNCGW", "M.Pda11646Dam": "GATC", "M.Pda11647Dam": "GATC", "M.Pda11648Dam": "GATC", "M.PdaNa1I": "GATC", "Pde12I": "GGNCC", "Pde133I": "GGCC", "Pde137I": "CCGG", "PdiI": "GCCGGC", "Pdi8503III": "CCGGNAG", "PdmI": "GAANNNNTTC", "Pdu1735I": "CACCAC", "Pei9403I": "GATC", "PenI": "GCAGT", "PfaI": "GATC", "PfaAI": "GGYRCC", "PfaAII": "CATATG", "PfaAIII": "GCATGC", "PfeI": "GAWTC", "Pfl8I": "GGATCC", "Pfl16I": "GATATC", "Pfl18I": "GAGCTC", "Pfl19I": "GGWCC", "Pfl21I": "CTGCAG", "Pfl23I": "GTGCAC", "Pfl23II": "CGTACG", "Pfl27I": "RGGWCCY", "Pfl37I": "CTGCAG", "Pfl67I": "CTCGAG", "Pfl1108I": "TCGTAG", "Pfl1108II": "CCGCGG", "Pfl8569I": "GCNNGC", "PflAI": "CGCG", "PflBI": "CCANNNNNTGG", "PflFI": "GACNNNGTC", "PflKI": "GGCC", "PflMI": "CCANNNNNTGG", "PflNI": "CTCGAG", "PflPt14I": "RGCCCAC", "PflWI": "CTCGAG", "PfoI": "TCCNGGA", "Pfr12I": "GTGCAC", "PfrCI": "C", "PfrJS2IV": "CTTCNAC", "PfrJS12IV": "TANAAG", "PfrJS12V": "GGCGGAG", "PfrJS15III": "CTTCNAC", "PfrJS22I": "GAANNNNNNNCTT", "PfrJS23II": "CTTCNAC", "PfrJS12aIV": "TANAAG", "PfrJS12aV": "GGCGGAG", "PfrJS15aIII": "CTTCNAC", "PfrUF1II": "CTTCNAC", "PfuNI": "CGTACG", "PgaI": "ATCGAT", "PgaP128IV": "GGCGAG", "PgaP129V": "GGCGAG", "M.Pge186Dam": "GATC", "M.Pge11434Dam": "GATC", "M.PgiI": "GATC", "PglI": "GCCGGC", "Pgl34I": "CACGTG", "PhaI": "GCATC", "M.PhaTDam": "GATC", "M.PhiMx8I": "CTSSAG", "M.Phi3TII": "TCGA", "PhoI": "GGCC", "PinI": "AGTACT", "PinAI": "ACCGGT", "PinBI": "ATGCAT", "PinBII": "TCCGGA", "Pin17FIII": "GGYGAB", "PinP10III": "ACACAG", "PinP23II": "CTRKCAG", "PinP30IV": "ACACAG", "PinP42II": "CTRKCAG", "PinP57III": "ACACAG", "PinP59III": "GAAGNAG", "PkrI": "GCNGC", "PlaI": "GGCC", "PlaII": "TTCGAA", "PlaAI": "CYCGRG", "PlaAII": "GTAC", "PlaDI": "CATCAG", "PleI": "GAGTC", "Ple19I": "CGATCG", "Ple214I": "GGCC", "PliI": "GTGCAC", "PliMI": "CGCCGAC", "PluI": "AGGCCT", "PluTI": "GGCGCC", "M.PluTDam": "GATC", "PmaI": "CTGCAG", "Pma44I": "CTGCAG", "PmaCI": "CACGTG", "PmdI": "CCTCAGC", "PmeI": "GTTTAAAC", "PmeII": "GGWCC", "Pme5II": "GACGAG", "Pme35I": "CCGG", "Pme55I": "AGGCCT", "PmeS132I": "GACGAG", "M.Pmi29Dam": "GATC", "M.Pmi67Dam": "GATC", "M.Pmi80Dam": "GATC", "M.Pmi159Dam": "GATC", "PmlI": "CACGTG", "PmnI": "GGCGCC", "M.Pmu216II": "GATC", "M.Pmu384II": "GATC", "M.Pmu385II": "GATC", "M.Pmu59910I": "GATC", "M.Pmu59962I": "GATC", "M.Pmu60213I": "GATC", "M.Pmu60214I": "GATC", "M.Pmu60215I": "GATC", "M.Pmu60248I": "GATC", "M.Pmu60380I": "GATC", "M.Pmu60381I": "GATC", "M.Pmu60385I": "GATC", "M.Pmu60494I": "GATC", "M.Pmu60675I": "GATC", "M.Pmu60712I": "GATC", "M.Pmu60713I": "GATC", "M.Pmu60714I": "GATC", "M.Pmu60717I": "GATC", "M.PmuADam": "GATC", "M.PmuDam": "GATC", "PmyI": "CTGCAG", "M.Pni10691Dam": "GATC", "PntI": "CGATCG", "PolI": "GGWCC", "PovI": "TGATCA", "PpaI": "GGTCTC", "PpaAI": "TTCGAA", "PpaAII": "TCGA", "PpeI": "GGGCCC", "PpeHI": "C", "Pph14I": "GGYRCC", "Pph288I": "GATC", "Pph1579I": "GGNCC", "Pph1773I": "GGNCC", "Pph2059I": "CTGCAG", "Pph2066I": "CTGCAG", "Pph3215I": "GWGCWC", "PpiP13II": "CGCRGAC", "PpiP36II": "CTRKCAG", "PpsI": "GAGTC", "Pps170I": "GCWGC", "PpuI": "GGCC", "Ppu6I": "YACGTR", "Ppu10I": "ATGCAT", "Ppu11I": "YACGTR", "Ppu13I": "AGGCCT", "Ppu20I": "GRGCYC", "Ppu21I": "YACGTR", "Ppu111I": "GAATTC", "Ppu1253I": "GACGTC", "PpuAI": "CGTACG", "PpuMI": "RGGWCCY", "PpuXI": "RGGWCCY", "Pre82I": "CCGCAG", "Pru2I": "GGCC", "Pru4541I": "GCWGC", "Psb9879I": "GGCC", "PscI": "ACATGT", "Psc2I": "GAANNNNTTC", "Psc27I": "TTCGAA", "Psc28I": "TTCGAA", "PseI": "GGNCC", "Pse18267I": "RCCGAAG", "PshAI": "GACNNNNGTC", "PshBI": "ATTAAT", "PshCI": "CACGTG", "PshDI": "CACGTG", "PshEI": "CTGCAG", "PsiI": "TTATAA", "PspI": "GGNCC", "Psp03I": "GGWCC", "Psp3I": "CAGCTG", "Psp4I": "CTCGAG", "Psp5I": "CAGCTG", "Psp5II": "RGGWCCY", "Psp6I": "CCWGG", "Psp23I": "CTGCAG", "Psp28I": "CTGCAG", "Psp29I": "GGCC", "Psp30I": "GGGCCC", "Psp31I": "GRGCYC", "Psp32I": "GTCGAC", "Psp33I": "GTCGAC", "Psp38I": "CACGTG", "Psp39I": "CCWGG", "Psp46I": "CTGCAG", "Psp56I": "GGATCC", "Psp61I": "GCCGGC", "Psp89I": "GTCGAC", "Psp0357II": "GCGAAG", "Psp1009I": "GCCNNNNNGGC", "Psp1406I": "AACGTT", "M.Psp5444II": "GATC", "PspAI": "CCCGGG", "PspALI": "CCCGGG", "PspBI": "CACGTG", "Psp124BI": "GAGCTC", "PspCI": "CACGTG", "PspDI": "TCGCGA", "PspEI": "GGTNACC", "PspGI": "CCWGG", "Psp10HII": "GRAGCAG", "M.PspHJ039I": "GATC", "PspLI": "CGTACG", "PspNI": "CTCGAG", "PspN4I": "GGNNCC", "M.PspNIH2Dam": "GATC", "PspOMI": "GGGCCC", "PspOMII": "CGCCCAR", "PspPI": "GGNCC", "PspPPI": "RGGWCCY", "PspPRI": "CCYCAG", "PspR84I": "TACYCAC", "PspSI": "CTGCAG", "M.PspS12I": "GANTC", "M.Psp16SW7Dam": "GATC", "PspXI": "VCTCGAGB", "PssI": "RGGNCCY", "PstI": "CTGCAG", "PstII": "CTGATG", "Pst145I": "CTAMRAG", "Pst273I": "GATCGAG", "Pst14472I": "CNYACAC", "M.Pst87Dam": "GATC", "PstNI": "CAGNNNCTG", "PstNHI": "GCTAGC", "PsuI": "RGATCY", "Psu161I": "CGATCG", "PsuAI": "YACGTR", "PsuGI": "BBCGD", "PsuNI": "CRCCGGYG", "PsyI": "GACNNNGTC", "PtaI": "TCCGGA", "PteI": "GCGCGC", "Pun14627I": "TGCGCA", "Pun14627II": "CAGCTG", "PunAI": "CYCGRG", "PunAII": "RCATGY", "PvuI": "CGATCG", "PvuII": "CAGCTG", "Pvu84I": "CGATCG", "Pvu84II": "CAGCTG", "PvuHKUI": "CAGCTG", "PxyI": "C", "PxyARI": "GATATC", "PxyJKI": "ATGCAT", "PxyMZI": "CCTNAGG", "M.RacFH23I": "GANTC", "Ral8I": "GGATC", "RalF40I": "GATC", "Ran11014IV": "GAAAGAG", "RaqI": "CCGCGG", "M.RaqMEM40Dam": "GATC", "Rba2021I": "CACGAGH", "Rba2021V": "CGAGG", "RcaI": "TCATGA", "RceI": "CATCGAC", "Rde10917I": "CCGCAG", "Rde10917II": "TGRYCA", "Rde10917III": "ACCCAG", "Rde10918I": "ACCCAG", "RdeGBI": "CCGCAG", "RdeGBII": "ACCCAG", "RdeR2I": "GCNGC", "M.RetCII": "GANTC", "Rfl17I": "GCNGC", "RflFI": "GTCGAC", "RflFII": "AGTACT", "RflFIII": "CGCCAG", "RgaI": "GCGATCGC", "RhcI": "TCATGA", "RheI": "GTCGAC", "M.RheG1III": "GATC", "M.Rho11sII": "TCGA", "RhpI": "GTCGAC", "RhsI": "GGATCC", "RigI": "GGCCGGCC", "M.Rja525I": "GANTC", "Rkr11038I": "GGANNNNNRTGA", "RlaI": "VCW", "RlaII": "ACACAG", "Rle69I": "GGTCTC", "RleAI": "CCCACA", "RluI": "GCCGGC", "Rlu1I": "GATC", "Rlu3I": "GGNNCC", "Rlu4I": "GGATCC", "RmaI": "CTAG", "Rma376I": "TTCGAA", "Rma485I": "CTAG", "Rma486I": "CTAG", "Rma490I": "CTAG", "Rma495I": "CTAG", "Rma495II": "GATATC", "Rma496I": "CTAG", "Rma496II": "GATATC", "Rma497I": "CTAG", "Rma497II": "GATATC", "Rma500I": "CTAG", "Rma501I": "CTAG", "Rma503I": "CTAG", "Rma506I": "CTAG", "Rma509I": "CTAG", "Rma510I": "CTAG", "Rma515I": "CTAG", "Rma516I": "CTAG", "Rma517I": "CTAG", "Rma518I": "CTAG", "Rma519I": "CTAG", "Rma522I": "CTAG", "Rma523I": "TTCGAA", "Rme21I": "ATCGAT", "Rmu369III": "GGCYAC", "Ror431III": "CTRGAG", "M.Ror431Dam": "GATC", "M.Ror9152Dam": "GATC", "M.Ror9155Dam": "GATC", "M.Ror9164Dam": "GATC", "M.Ror24724Dam": "GATC", "RpaI": "GTYGGAG", "RpaBI": "CCCGCAG", "RpaB5I": "CGRGGAC", "RpaTI": "GRTGGAG", "M.Rpl9179Dam": "GATC", "M.Rpl9527Dam": "GATC", "M.Rpl9528Dam": "GATC", "M.Rpl12998Dam": "GATC", "RrhI": "GTCGAC", "Rrh4273I": "GTCGAC", "RrhJ1I": "GCCGGC", "RroI": "GTCGAC", "RruI": "TCGCGA", "RsaI": "GTAC", "RsaNI": "GTAC", "RseI": "CAYNNNNRTG", "RshI": "CGATCG", "RshII": "CCSGG", "RspI": "CGATCG", "Rsp008IV": "ACGCAG", "Rsp008V": "GCCCAT", "Rsp241I": "CGATCG", "M.Rsp531I": "GANTC", "Rsp531II": "CACACG", "M.Rsp532I": "GANTC", "RspLKI": "GCATGC", "RspLKII": "GGATCC", "RspPBTS2III": "CTTCGAG", "RspRSI": "GCCNNNNNGGC", "RspXI": "TCATGA", "RsrI": "GAATTC", "RsrII": "CGGWCCG", "Rsr2I": "CGGWCCG", "RtrI": "GTCGAC", "Rtr20I": "GAAGAC", "Rtr63I": "GTCGAC", "Rtr1953I": "TGANNNNNNTGA", "SaaI": "CCGCGG", "SabI": "CCGCGG", "SacI": "GAGCTC", "SacII": "CCGCGG", "SacAI": "GCCGGC", "SacNI": "GRGCYC", "Saf8902III": "CAATNAG", "SagI": "GGCC", "Sag16I": "CTGCAG", "Sag23I": "CTGCAG", "Sag901I": "GCAAAT", "SaiI": "GGGTC", "SakI": "CCGCGG", "SalI": "GTCGAC", "Sal13I": "CTGCAG", "Sal1974I": "CTCGAG", "SalAI": "GATC", "SalCI": "GCCGGC", "SalDI": "TCGCGA", "SalHI": "GATC", "SalPI": "CTGCAG", "SanDI": "GGGWCCC", "SaoI": "GCCGGC", "SapI": "GCTCTTC", "SaqAI": "TTAA", "SarI": "AGGCCT", "SasI": "GAGGAG", "SatI": "GCNGC", "SauI": "CCTNAGG", "Sau2I": "GGNCC", "Sau5I": "GGNCC", "Sau10I": "GGTACC", "Sau12I": "GGTCTC", "Sau13I": "GGNCC", "Sau14I": "GGNCC", "Sau15I": "GATC", "Sau16I": "CCWGG", "Sau17I": "GGNCC", "Sau32I": "GGNCC", "Sau33I": "GGNCC", "Sau90I": "CTYRAG", "Sau93I": "CTYRAG", "Sau96I": "GGNCC", "Sau98I": "CTYRAG", "Sau557I": "GGNCC", "Sau3239I": "CTCGAG", "Sau6782I": "GATC", "Sau64037IV": "GTANNNNNNTGG", "SauAI": "GCCGGC", "Sau3AI": "GATC", "SauBI": "GGNCC", "SauBMKI": "GCCGGC", "SauCI": "GATC", "SauDI": "GATC", "SauEI": "GATC", "SauFI": "GATC", "SauGI": "GATC", "SauHI": "CCTNAGG", "SauHPI": "GCCGGC", "SauLPI": "GCCGGC", "SauLPII": "CTCGAG", "SauMI": "GATC", "SauMJ015III": "GARCNAG", "SauNI": "GCCGGC", "SauN315I": "AGGNNNNNGAT", "SauSI": "GCCGGC", "Sau96mI": "CTYRAG", "SbaI": "CAGCTG", "Sba460II": "GGNGAYG", "M.SbaUDam": "GATC", "SbfI": "CCTGCAGG", "SbgI": "GATC", "Sbi68I": "CTCGAG", "SblAI": "CCWWGG", "SblBI": "CCWWGG", "SblCI": "CCWWGG", "SboI": "CCGCGG", "Sbo13I": "TCGCGA", "Sbo46I": "TGAAC", "M.Sbo1621Dam": "GATC", "M.Sbo8700Dam": "GATC", "M.Sbo9733Dam": "GATC", "M.Sbo9734Dam": "GATC", "M.Sbo12419Dam": "GATC", "M.Sbo12985Dam": "GATC", "M.Sbo49812Dam": "GATC", "M.Sbo1621Dcm": "CCWGG", "SbvI": "GGCC", "ScaI": "AGTACT", "Sca1827I": "CTCGAG", "SceIII": "GCCGGC", "SceAI": "CGCG", "Scg2I": "CCWGG", "SchI": "GAGTC", "SchZI": "CCGCGG", "SciI": "CTCGAG", "Sci1831I": "CTCGAG", "SciAI": "GGTNACC", "SciAII": "CAGCTG", "SciBI": "CTCGAG", "SciNI": "GCGC", "ScoI": "GAGCTC", "ScoAI": "CTGCAG", "ScoDS2II": "GCTAAT", "ScoNI": "GTGCAC", "ScrFI": "CCNGG", "ScuI": "CTCGAG", "SdaI": "CCTGCAGG", "Sde240I": "GCNGC", "SdeAI": "CAGRAG", "SdeAII": "CCWGG", "SdiI": "GGCCNNNNNGGCC", "SdiAI": "CTCGAG", "SduI": "GDGCHC", "SdyI": "GGNCC", "M.Sdy547Dam": "GATC", "M.Sdy3308Dam": "GATC", "M.Sdy3380Dam": "GATC", "M.Sdy3818Dam": "GATC", "M.Sdy3937Dam": "GATC", "M.Sdy9718Dam": "GATC", "M.Sdy9764Dam": "GATC", "M.Sdy10954Dam": "GATC", "M.Sdy12039Dam": "GATC", "M.Sdy13313Dam": "GATC", "M.Sdy49345Dam": "GATC", "SecI": "CCNNGG", "SecII": "CCGG", "SecIII": "CCTNAGG", "SelI": "CGCG", "SelAI": "GGNCC", "SelPI": "CCWGG", "Sen13III": "GATCAG", "Sen23IV": "ATGAAG", "Sen31III": "GATCAG", "Sen312IV": "GATCAG", "Sen321III": "GATCAG", "Sen373III": "CANCATC", "Sen0392IV": "GATCAG", "Sen00538II": "GATCAG", "Sen870II": "GATCAG", "Sen1736II": "GATCAG", "Sen1878IV": "GATCAG", "Sen1880IV": "GATCAG", "Sen1896IV": "GATCAG", "Sen1898IV": "GATCAG", "Sen1906IV": "GATCAG", "Sen1908IV": "GATCAG", "Sen1910IV": "GATCAG", "Sen13311III": "GATCAG", "Sen17963III": "CCAAAC", "Sen18746III": "GATCAG", "SenA242III": "GATCAG", "SenA1673III": "GNGGCAG", "M.SenA242Dcm": "CCWGG", "SenAZII": "CAGAG", "M.SenAZDam": "GATC", "M.SenAbaDam": "GATC", "M.SenAboDam": "GATC", "M.SenAnaDcm": "CCWGG", "M.SenB4212Dam": "GATC", "SenC1808III": "GATCAG", "SenC1810IV": "GATCAG", "M.SenC618Dam": "GATC", "M.SenC6482Dam": "GATC", "M.SenC7299Dam": "GATC", "M.SenC7304Dam": "GATC", "M.SenC9854Dam": "GATC", "M.SenC1765Dcm": "CCWGG", "M.SenCer87Dam": "GATC", "M.Sen23Dam": "GATC", "M.Sen74Dam": "GATC", "M.Sen92Dam": "GATC", "M.Sen313Dam": "GATC", "M.Sen0738Dam": "GATC", "M.Sen954Dam": "GATC", "M.Sen1357Dam": "GATC", "M.Sen1435Dam": "GATC", "M.Sen1705Dam": "GATC", "M.Sen1706Dam": "GATC", "M.Sen1764Dam": "GATC", "M.Sen1781Dam": "GATC", "M.Sen1853Dam": "GATC", "M.Sen1896Dam": "GATC", "M.Sen1898Dam": "GATC", "M.Sen1901Dam": "GATC", "M.Sen1908Dam": "GATC", "M.Sen1912Dam": "GATC", "M.Sen1927Dam": "GATC", "M.Sen2169Dam": "GATC", "M.Sen2444Dam": "GATC", "M.Sen2916Dam": "GATC", "M.Sen3072Dam": "GATC", "M.Sen3307Dam": "GATC", "M.Sen3387Dam": "GATC", "M.Sen3861Dam": "GATC", "M.Sen4028Dam": "GATC", "M.Sen4173Dam": "GATC", "M.Sen4174Dam": "GATC", "M.Sen4175Dam": "GATC", "M.Sen4444Dam": "GATC", "M.Sen5722Dam": "GATC", "M.Sen5735Dam": "GATC", "M.Sen5742Dam": "GATC", "M.Sen5743Dam": "GATC", "M.Sen5747Dam": "GATC", "M.Sen5772Dam": "GATC", "M.Sen5774Dam": "GATC", "M.Sen5785Dam": "GATC", "M.Sen5793Dam": "GATC", "M.Sen5794Dam": "GATC", "M.Sen5797Dam": "GATC", "M.Sen5798Dam": "GATC", "M.Sen5801Dam": "GATC", "M.Sen6016Dam": "GATC", "M.Sen6024Dam": "GATC", "M.Sen6245Dam": "GATC", "M.Sen6385Dam": "GATC", "M.Sen6802Dam": "GATC", "M.Sen7101Dam": "GATC", "M.Sen7112Dam": "GATC", "M.Sen7295Dam": "GATC", "M.Sen7302Dam": "GATC", "M.Sen7303Dam": "GATC", "M.Sen7308Dam": "GATC", "M.Sen7323Dam": "GATC", "M.Sen7346Dam": "GATC", "M.Sen7349Dam": "GATC", "M.Sen7388Dam": "GATC", "M.Sen7832Dam": "GATC", "M.Sen8297Dam": "GATC", "M.Sen8392Dam": "GATC", "M.Sen8495Dam": "GATC", "M.Sen8497Dam": "GATC", "M.Sen8705Dam": "GATC", "M.Sen9322Dam": "GATC", "M.Sen9606Dam": "GATC", "M.Sen9868Dam": "GATC", "M.Sen9884Dam": "GATC", "M.Sen9912Dam": "GATC", "M.Sen10060Dam": "GATC", "M.Sen10252Dam": "GATC", "M.Sen10384Dam": "GATC", "M.Sen10480Dam": "GATC", "M.Sen10536Dam": "GATC", "M.Sen10705Dam": "GATC", "M.Sen10717Dam": "GATC", "M.Sen10718Dam": "GATC", "M.Sen10720Dam": "GATC", "M.Sen10722Dam": "GATC", "M.Sen10729Dam": "GATC", "M.Sen11511Dam": "GATC", "M.Sen12116Dam": "GATC", "M.Sen12416Dam": "GATC", "M.Sen12417Dam": "GATC", "M.Sen12418Dam": "GATC", "M.Sen12420Dam": "GATC", "M.Sen13311Dam": "GATC", "M.Sen13349Dam": "GATC", "M.Sen14882Dam": "GATC", "M.Sen15791Dam": "GATC", "M.Sen17963Dam": "GATC", "M.Sen22425Dam": "GATC", "M.Sen44454Dam": "GATC", "M.Sen45763Dam": "GATC", "M.Sen49284Dam": "GATC", "M.Sen51958Dam": "GATC", "M.Sen51960Dam": "GATC", "M.Sen55391Dam": "GATC", "M.Sen64033Dam": "GATC", "M.Sen69835Dam": "GATC", "M.Sen76211Dam": "GATC", "M.Sen76214Dam": "GATC", "M.Sen13Dcm": "CCWGG", "M.Sen31Dcm": "CCWGG", "M.Sen195Dcm": "CCWGG", "M.Sen255Dcm": "CCWGG", "M.Sen0392Dcm": "CCWGG", "M.Sen954Dcm": "CCWGG", "M.Sen1285Dcm": "CCWGG", "M.Sen1387Dcm": "CCWGG", "M.Sen1427Dcm": "CCWGG", "M.Sen1677Dcm": "CCWGG", "M.Sen1781Dcm": "CCWGG", "M.Sen1783Dcm": "CCWGG", "M.Sen1853Dcm": "CCWGG", "M.Sen1880Dcm": "CCWGG", "M.Sen1900Dcm": "CCWGG", "M.Sen1901Dcm": "CCWGG", "M.Sen1942Dcm": "CCWGG", "M.Sen3307Dcm": "CCWGG", "M.Sen4173Dcm": "CCWGG", "M.Sen4174Dcm": "CCWGG", "M.Sen4175Dcm": "CCWGG", "M.Sen9120Dcm": "CCWGG", "M.Sen10708Dcm": "CCWGG", "M.Sen13311Dcm": "CCWGG", "M.Sen18486Dcm": "CCWGG", "M.Sen18569Dcm": "CCWGG", "M.Sen18746Dcm": "CCWGG", "M.Sen22425Dcm": "CCWGG", "M.Sen24229Dcm": "CCWGG", "M.SenG9184Dam": "GATC", "SenK002II": "GAANCAG", "M.SenK1674Dam": "GATC", "M.SenL1Dam": "GATC", "SenLT2III": "GATCAG", "M.SenLT2Dam": "GATC", "M.SenLT2Dcm": "CCWGG", "M.SenN1660Dam": "GATC", "SenPT16I": "CGGCCG", "SenPT14bI": "CCGCGG", "SenPU131IV": "GAANCAG", "M.SenPU131Dam": "GATC", "M.Sen1344RDam": "GATC", "SenS317IV": "GATCAG", "SenS320IV": "GATCAG", "SenS320V": "GATCAG", "SenSARA26III": "ACRCAG", "M.SenSARA26Dam": "GATC", "M.SenSLDam": "GATC", "M.SenSPBDam": "GATC", "SenTFIV": "GATCAG", "SenWA15II": "GATCAG", "SenWT7IV": "GATCAG", "SenWT8IV": "CCAAAT", "M.SenWT7Dam": "GATC", "M.SenWT8Dam": "GATC", "M.SenWT281Dam": "GATC", "M.SenWT281Dcm": "CCWGG", "SenWW012III": "GATCAG", "SenpCI": "CCGCGG", "SepI": "ATGCAT", "Sep704II": "CAGRAG", "Sep723II": "GGAAC", "SepMI": "GATATC", "SetI": "ASST", "SexI": "CTCGAG", "SexAI": "ACCWGGT", "SexBI": "CCGCGG", "SexCI": "CCGCGG", "SfaI": "GGCC", "SfaAI": "GCGATCGC", "SfaGUI": "CCGG", "SfaNI": "GCATC", "SfcI": "CTRYAG", "SfeI": "CTRYAG", "SfiI": "GGCCNNNNNGGCC", "SflI": "CTGCAG", "M.Sfl74Dam": "GATC", "M.Sfl394Dam": "GATC", "M.Sfl877Dam": "GATC", "M.Sfl1508Dam": "GATC", "M.Sfl3008Dam": "GATC", "M.Sfl3063Dam": "GATC", "M.Sfl3193Dam": "GATC", "M.Sfl8524Dam": "GATC", "M.Sfl9728Dam": "GATC", "M.Sfl29903Dam": "GATC", "M.Sfl43145Dam": "GATC", "M.Sfl89141Dam": "GATC", "M.Sfl614982Dam": "GATC", "M.Sfl735612Dam": "GATC", "M.Sfl741170Dam": "GATC", "M.Sfl74Dcm": "CCWGG", "M.Sfl877Dcm": "CCWGG", "M.Sfl3008Dcm": "CCWGG", "M.Sfl3063Dcm": "CCWGG", "M.Sfl3193Dcm": "CCWGG", "M.Sfl9728Dcm": "CCWGG", "M.Sfl43145Dcm": "CCWGG", "M.Sfl614982Dcm": "CCWGG", "SflHK1794I": "CCWGG", "SflHK2374I": "CCWGG", "SflHK2731I": "CCWGG", "SflHK6873I": "CCWGG", "SflHK7234I": "CCWGG", "SflHK7462I": "CCWGG", "SflHK8401I": "CCWGG", "SflHK10695I": "CCSGG", "SflHK10790I": "CCWGG", "SflHK11086I": "CCSGG", "SflHK11087I": "CCSGG", "SflHK11572I": "CCSGG", "SflHK115731I": "CCSGG", "M.SflNCTC5Dam": "GATC", "Sfl2aI": "CCWGG", "Sfl2bI": "CCWGG", "SfnI": "GGWCC", "SfoI": "GGCGCC", "SfrI": "CCGCGG", "Sfr274I": "CTCGAG", "Sfr303I": "CCGCGG", "Sfr382I": "CCGCGG", "SfuI": "TTCGAA", "Sfu1762I": "CTCGAG", "SgaI": "CTCGAG", "SgeI": "CNNGNNNNNNNNN", "SgfI": "GCGATCGC", "Sgh1835I": "GGWCC", "SghWII": "GCCGGC", "SgiI": "CTGCAG", "SgoI": "CTCGAG", "Sgr20I": "CCWGG", "Sgr1839I": "TTCGAA", "Sgr1841I": "CTCGAG", "Sgr13350I": "GAGCTC", "Sgr13350III": "GCTCTTC", "SgrAI": "CRCCGGYG", "SgrAII": "CGAGATC", "SgrBI": "CCGCGG", "SgrDI": "CGTCGACG", "M.Sgr11543Dam": "GATC", "SgrTI": "CCDS", "SgsI": "GGCGCGCC", "SguI": "CC", "ShaI": "GGGTC", "ShyI": "CCGCGG", "Shy1766I": "CTCGAG", "SinI": "GGWCC", "SinAI": "GGWCC", "SinBI": "GGWCC", "SinCI": "GGWCC", "SinDI": "GGWCC", "SinEI": "GGWCC", "SinFI": "GGWCC", "SinGI": "GGWCC", "SinHI": "GGWCC", "SinJI": "GGWCC", "SinMI": "GATC", "SkaI": "GCCGGC", "SkaII": "CTGCAG", "SlaI": "CTCGAG", "SlbI": "GGTCTC", "SleI": "CCWGG", "SluI": "CTCGAG", "Slu1777I": "GCCGGC", "SmaI": "CCCGGG", "M.SmaII": "GATC", "SmaAI": "CGTACG", "SmaAII": "GACNNNGTC", "SmaAIII": "CGATCG", "SmaAIV": "CAGCTG", "M.Sma121Dam": "GATC", "M.Sma122Dam": "GATC", "M.Sma123Dam": "GATC", "M.Sma130Dam": "GATC", "M.Sma131Dam": "GATC", "M.Sma10211Dam": "GATC", "M.Sma13382Dam": "GATC", "SmaUMH5I": "CTTGAC", "SmaUMH8I": "GCGAACB", "M.SmaUMH1Dam": "GATC", "M.SmaUMH2Dam": "GATC", "M.SmaUMH9Dam": "GATC", "M.SmaUMH10Dam": "GATC", "M.SmaUMH11Dam": "GATC", "M.SmaUMH12Dam": "GATC", "M.SmeI": "GANTC", "M.Sme1115I": "GANTC", "SmiI": "ATTTAAAT", "SmiMI": "CAYNNNNRTG", "SmiMII": "GATATC", "SmiMBI": "GATC", "SmlI": "CTYRAG", "SmoI": "CTYRAG", "Smo40529I": "GCCGGC", "SmoLI": "CCGG", "SmoLII": "GCAGT", "SmuI": "CCCGC", "SmuCI": "ATGCAT", "SmuEI": "GGWCC", "SnaI": "GTATAC", "Sna507VIII": "CRTTGAG", "Sna3286I": "TCGCGA", "SnaBI": "TACGTA", "SniI": "CCWGG", "SnoI": "GTGCAC", "Sno506I": "GGCCGAG", "Sod1I": "GATCNAC", "SolI": "GGATCC", "Sol3335I": "CAGCTG", "Sol10179I": "CTCGAG", "SonI": "ATCGAT", "M.SonDam": "GATC", "SpaI": "CTCGAG", "SpaHI": "GCATGC", "SpaPI": "GACNNNGTC", "SpaPII": "CGATCG", "SpaPIII": "CAGCTG", "SpaPIV": "AAGCTT", "SpaXI": "GCATGC", "SpeI": "ACTAGT", "Spe19205IV": "GGACY", "SphI": "GCATGC", "Sph1719I": "CTCGAG", "SplI": "CGTACG", "SplII": "GACNNNGTC", "SplIII": "GGCC", "SplAI": "CGTACG", "SplAII": "GACNNNGTC", "SplAIII": "CGATCG", "SplAIV": "CAGCTG", "SplRp8I": "CCCAAG", "SpmI": "ATCGAT", "SpnD39IIIA": "CRAANNNNNNNNCTG", "SpnD39IIIB": "CRAANNNNNNNNNTTC", "SpnD39IIIC": "CACNNNNNNNCTT", "SpnD39IIID": "CACNNNNNNNCTG", "Spn23FI": "GATC", "SpnRII": "TCGAG", "SpoI": "TCGCGA", "SpoDI": "GCGGRAG", "SprLI": "CTGCAG", "M.SptADam": "GATC", "SpuI": "CCGCGG", "SpvI": "GGATCC", "SqiI": "GCWGC", "M.Squ13194Dam": "GATC", "SrfI": "GCCCGGGC", "SriI": "CTGCAG", "SrifpI": "CTCGAG", "SrlI": "GCCGGC", "SrlII": "ATGCAT", "Srl19I": "TTTAAA", "Srl1DI": "CTGCAG", "Srl2DI": "CTGCAG", "Srl5DI": "CTGCAG", "Srl8DI": "ATTAAT", "Srl32DI": "CTGCAG", "Srl32DII": "GAATTC", "Srl55DI": "GAATTC", "Srl55DII": "ATTAAT", "Srl56DI": "CTRYAG", "Srl61DI": "TTTAAA", "Srl65DI": "ATTAAT", "Srl76DI": "TTTAAA", "Srl77DI": "GCCGGC", "Srr17I": "ATTAAT", "SruI": "TTTAAA", "Sru4DI": "ATTAAT", "Sru30DI": "AGGCCT", "SsbI": "AAGCTT", "SscL1I": "GANTC", "SseI": "TGATCA", "SseII": "CCGCGG", "Sse9I": "AATT", "Sse232I": "CGCCGGCG", "Sse1825I": "GGGWCCC", "Sse8387I": "CCTGCAGG", "Sse8647I": "AGGWCCT", "SseAI": "GGCGCC", "SseBI": "AGGCCT", "SshAI": "CCTNAGG", "SsiAI": "GATC", "SsiBI": "GATC", "SslI": "CCWGG", "SsmI": "CTGATG", "SsmII": "CCGCGG", "Ssm4I": "GAGCTC", "Ssm5I": "GAGCTC", "Ssm5II": "CCGCGG", "SsoI": "GAATTC", "SsoII": "CCNGG", "M.Sso12984Dam": "GATC", "M.Sso29930Dam": "GATC", "M.Sso90Dcm": "CCWGG", "SspI": "AATATT", "Ssp1I": "TTCGAA", "Ssp2I": "CCSGG", "Ssp4I": "CTCGAG", "Ssp12I": "CTGCAG", "Ssp14I": "TTCGAA", "Ssp34I": "TTCGAA", "Ssp42I": "TTCGAA", "Ssp43I": "TTCGAA", "Ssp45I": "TTCGAA", "Ssp47I": "TTCGAA", "Ssp48I": "TTCGAA", "Ssp152I": "TTCGAA", "Ssp714II": "CGCAGCG", "Ssp1725I": "CCGCGG", "Ssp4800I": "TGTACA", "Ssp5230I": "GACGTC", "Ssp6714IV": "GCGGRAG", "M.Ssp6803III": "GATC", "Ssp6803IV": "GAAGGC", "Ssp27144I": "ATCGAT", "SspAI": "CCWGG", "SspBI": "TGTACA", "SspCI": "GCCGGC", "SspDI": "GGCGCC", "SspD5I": "GGTGA", "SspD5II": "ATGCAT", "M.Ssp354Dam": "GATC", "M.Ssp6947Dam": "GATC", "M.Ssp11881Dam": "GATC", "M.Ssp39006Dam": "GATC", "SspJI": "TACGTA", "SspJII": "GRCGYC", "SspJOR1II": "AGCGANC", "SspKI": "CGTACG", "M.SspLM21I": "GANTC", "SspMI": "CTAG", "SspM1I": "TACGTA", "SspM1II": "GRCGYC", "SspM1III": "GGYRCC", "SspM2I": "TACGTA", "SspM2II": "GRCGYC", "M.SspNIH1Dam": "GATC", "SspP33II": "GGGAC", "M.SspRAC02II": "GANTC", "SspRFI": "TTCGAA", "M.SspWT4Dam": "GATC", "SsrI": "GTTAAC", "M.SssI": "CG", "SstI": "GAGCTC", "SstII": "CCGCGG", "SstIV": "TGATCA", "Sst12I": "CTGCAG", "SstE37I": "CGAAGAC", "SstE37III": "CTGAAG", "Ssu211I": "GATC", "Ssu212I": "GATC", "Ssu220I": "GATC", "R1.Ssu2479I": "GATC", "R2.Ssu2479I": "GATC", "R1.Ssu4109I": "GATC", "R2.Ssu4109I": "GATC", "R1.Ssu4961I": "GATC", "R2.Ssu4961I": "GATC", "R1.Ssu8074I": "GATC", "R2.Ssu8074I": "GATC", "R1.Ssu11318I": "GATC", "R2.Ssu11318I": "GATC", "R1.SsuDAT1I": "GATC", "R2.SsuDAT1I": "GATC", "SsvI": "AGGCCT", "StaI": "CCGCGG", "StaAI": "CTCGAG", "SteI": "AGGCCT", "SthI": "GGTACC", "Sth117I": "CCWGG", "Sth132I": "CCCG", "Sth134I": "CCGG", "Sth302I": "CCWGG", "Sth302II": "CCGG", "Sth368I": "GATC", "Sth455I": "CCWGG", "Sth20745III": "GGACGAC", "Sth64987II": "GAAGT", "SthAI": "GGTACC", "SthBI": "GGTACC", "SthCI": "GGTACC", "SthDI": "GGTACC", "SthEI": "GGTACC", "SthFI": "GGTACC", "SthGI": "GGTACC", "SthHI": "GGTACC", "SthJI": "GGTACC", "SthKI": "GGTACC", "SthLI": "GGTACC", "SthMI": "GGTACC", "SthNI": "GGTACC", "SthSt3II": "GAAGT", "StrI": "CTCGAG", "StsI": "GGATG", "StuI": "AGGCCT", "StyI": "CCWWGG", "Sty13348III": "GATCAG", "M.StyADam": "GATC", "StyD4I": "CCNGG", "M.Sty14028Dam": "GATC", "M.Sty13348Dcm": "CCWGG", "StyLTI": "CAGAG", "StyLTIII": "GAGNNNNNNRTAYG", "StySBLI": "GGTANNNNNNTCG", "StySEAI": "ACANNNNNNTYCA", "StySENI": "GGYANNNNNNTCG", "StySGI": "TAANNNNNNRTCG", "StySJI": "GAGNNNNNNGTRC", "StySKI": "CGATNNNNNNNGTTA", "StySPI": "AACNNNNNNGTRC", "StySQI": "AACNNNNNNRTAYG", "SuaI": "GGCC", "SuiI": "GCWGC", "SulI": "GGCC", "SunI": "CGTACG", "SurI": "GGATCC", "SurP32aII": "ACRGAG", "Sve194I": "CTCGAG", "Sve396I": "GCWGC", "SviI": "TTCGAA", "SvoI": "CRCCGGYG", "SwaI": "ATTTAAAT", "M.SwoADam": "GATC", "SynI": "GGWCC", "SynII": "GAANNNNTTC", "TaaI": "ACNGT", "M.TaeII": "TGATCA", "TaiI": "ACGT", "TaqI": "TCGA", "TaqII": "GACCGA", "TaqIII": "CACCCA", "Taq20I": "TCGA", "Taq52I": "GCWGC", "TaqXI": "CCWGG", "TasI": "AATT", "TatI": "WGTACW", "TauI": "GCSGC", "TauII": "CGGCCG", "Tbr51I": "TCGA", "TceI": "GAAGA", "TdeI": "GATC", "TdeII": "CTCTTC", "TdeIII": "GGNCC", "TelI": "GACNNNGTC", "TfiI": "GAWTC", "TfiA3I": "TCGA", "TfiTok6A1I": "TCGA", "TflI": "TCGA", "TglI": "CCGCGG", "ThaI": "CGCG", "TliI": "CTCGAG", "TmaI": "CGCG", "Tmu1I": "CCSGG", "TneI": "GCWGC", "TneDI": "CGCG", "M.TpaI": "GATC", "TpyTP2I": "ACCAAG", "TrsKTI": "GATC", "TrsKTII": "GACNNNGTC", "TrsKTIII": "CATATG", "TrsSI": "GATC", "TrsSII": "GACNNNNNNGTC", "TrsTI": "GATC", "TrsTII": "CTTAAG", "TruI": "GGWCC", "TruII": "GATC", "Tru1I": "TTAA", "Tru9I": "TTAA", "Tru28I": "GGWCC", "Tru201I": "RGATCY", "TscI": "ACGT", "TscAI": "CASTGNN", "Tsc4aI": "TCGA", "TseI": "GCWGC", "TseAI": "GDGCHC", "TseBI": "GCWGC", "TseCI": "AATT", "TseDI": "RCCGGY", "TseFI": "GTSAC", "TsoI": "TARCCA", "TspI": "GACNNNGTC", "Tsp32I": "TCGA", "Tsp32II": "TCGA", "Tsp45I": "GTSAC", "Tsp49I": "ACGT", "Tsp132I": "GGCC", "Tsp133I": "GATC", "Tsp219I": "GCCNNNNNGGC", "Tsp266I": "GGCC", "Tsp273I": "GATATC", "Tsp273II": "GGCC", "Tsp281I": "GGCC", "Tsp301I": "GGWCC", "Tsp358I": "TCGA", "Tsp504I": "CGGCCG", "Tsp505I": "TCGA", "Tsp507I": "TCCGGA", "Tsp509I": "AATT", "Tsp510I": "TCGA", "Tsp514I": "TCCGGA", "Tsp560I": "GGCC", "TspAI": "CCWGG", "TspARh3I": "GRACGAC", "TspBI": "CCRYGG", "Tsp4CI": "ACNGT", "TspDTI": "ATGAA", "TspEI": "AATT", "Tsp8EI": "GCCNNNNNGGC", "TspGWI": "ACGGA", "TspGWII": "CTGCAG", "TspIDSI": "ACGT", "TspMI": "CCCGGG", "TspNI": "TCGA", "TspRI": "CASTGNN", "TspWAM8AI": "ACGT", "TspZNI": "GGCC", "TssI": "GAGNNNCTC", "TsuI": "GCGAC", "TteI": "GACNNNGTC", "TteAI": "GGCC", "Tth24I": "TCGA", "Tth111I": "GACNNNGTC", "Tth111II": "CAARCA", "TthHB8I": "TCGA", "TthHB27I": "CAARCA", "TthRQI": "TCGA", "TtmI": "ACGT", "TtmII": "GCGCGC", "TtnI": "GGCC", "TtoI": "CCGCGG", "TtrI": "GACNNNGTC", "Tvu2HI": "GGCC", "Uba4I": "GATC", "Uba6I": "ACGCGT", "Uba9I": "GGCC", "Uba11I": "CCWGG", "Uba13I": "CCWGG", "Uba17I": "CCNGG", "Uba19I": "GGATCC", "Uba20I": "CCWGG", "Uba22I": "ATCGAT", "Uba24I": "ATCGAT", "Uba30I": "ATCGAT", "Uba31I": "GGATCC", "Uba34I": "ATCGAT", "Uba36I": "YGGCCR", "Uba38I": "GGATCC", "Uba39I": "GRGCYC", "Uba40I": "AGGCCT", "Uba41I": "CCSGG", "Uba42I": "CCSGG", "Uba43I": "ATCGAT", "Uba46I": "CTGCAG", "Uba48I": "GGWCC", "Uba51I": "GGATCC", "Uba54I": "GGCC", "Uba57I": "GRGCYC", "Uba58I": "GAATTC", "Uba59I": "GATC", "Uba61I": "GGCC", "Uba62I": "GGWCC", "Uba65I": "GGTCTC", "Uba66I": "CCGCGG", "Uba69I": "GCGCGC", "Uba71I": "CTGCAG", "Uba72I": "CTGCAG", "Uba76I": "GGTACC", "Uba77I": "CCGCGG", "Uba81I": "CCWGG", "Uba82I": "CCWGG", "Uba83I": "AAGCTT", "Uba84I": "GGTCTC", "Uba85I": "GGTACC", "Uba86I": "GGTACC", "Uba87I": "GGTACC", "Uba88I": "GGATCC", "Uba89I": "GTCGAC", "Uba90I": "CCGCGG", "Uba1093I": "CCGCGG", "Uba1094I": "AGTACT", "Uba1095I": "CCGCGG", "Uba1096I": "ATCGAT", "Uba1097I": "GGCC", "Uba1098I": "GGATCC", "Uba1099I": "GGNCC", "Uba1100I": "ATCGAT", "Uba1101I": "GATC", "Uba1111I": "CCGCGG", "Uba1112I": "CTGCAG", "Uba1113I": "CCGCGG", "Uba1114I": "CCWGG", "Uba1115I": "CTGCAG", "Uba1116I": "CTGCAG", "Uba1117I": "TCGCGA", "Uba1118I": "CCWGG", "Uba1119I": "CTGCAG", "Uba1120I": "CCWGG", "Uba1121I": "CCWGG", "Uba1122I": "GCCGGC", "Uba1123I": "CTGCAG", "Uba1124I": "GRGCYC", "Uba1125I": "CCWGG", "Uba1126I": "CCGCGG", "Uba1127I": "GGYRCC", "Uba1128I": "CCGG", "Uba1129I": "CGATCG", "Uba1130I": "CTCGAG", "Uba1131I": "GGWCC", "Uba1133I": "ATCGAT", "Uba1134I": "GGNCC", "Uba1136I": "TCCGGA", "Uba1137I": "ATCGAT", "Uba1138I": "ATCGAT", "Uba1139I": "CGATCG", "Uba1140I": "GGCC", "Uba1141I": "CCGG", "Uba1142I": "GRGCYC", "Uba1144I": "ATCGAT", "Uba1145I": "ATCGAT", "Uba1146I": "GGCC", "Uba1147I": "GGCC", "Uba1148I": "CTCGAG", "Uba1149I": "CTGCAG", "Uba1150I": "GGCC", "Uba1152I": "GGCC", "Uba1153I": "GGCC", "Uba1154I": "CTCGAG", "Uba1155I": "GGCC", "Uba1156I": "GGGCCC", "Uba1157I": "GGGCCC", "Uba1158I": "AGTACT", "Uba1159I": "GRGCYC", "Uba1160I": "GGNCC", "Uba1161I": "ATCGAT", "Uba1162I": "GCATGC", "Uba1163I": "GGATCC", "Uba1164I": "GGNCC", "Uba1164II": "AAGCTT", "Uba1165I": "GGGCCC", "Uba1166I": "CTCGAG", "Uba1167I": "GGATCC", "Uba1168I": "ATCGAT", "Uba1169I": "GGCC", "Uba1170I": "AGGCCT", "Uba1171I": "CCWGG", "Uba1172I": "GGATCC", "Uba1173I": "GGATCC", "Uba1174I": "GGCC", "Uba1175I": "GGCC", "Uba1176I": "GGCC", "Uba1177I": "GATC", "Uba1178I": "GGCC", "Uba1179I": "GGCC", "Uba1180I": "AGGCCT", "Uba1181I": "CCWGG", "Uba1182I": "GATC", "Uba1183I": "GATC", "Uba1184I": "CTGCAG", "Uba1184II": "CCTNAGG", "Uba1185I": "CCWGG", "Uba1186I": "CTGCAG", "Uba1187I": "CCGCGG", "Uba1188I": "YGGCCR", "Uba1189I": "CCWGG", "Uba1190I": "GACNNNNNGTC", "Uba1191I": "GACNNNNNGTC", "Uba1192I": "CTCTTC", "Uba1193I": "CCWGG", "Uba1195I": "ATCGAT", "Uba1196I": "ATCGAT", "Uba1197I": "ATCGAT", "Uba1198I": "ATCGAT", "Uba1199I": "ATCGAT", "Uba1200I": "ATCGAT", "Uba1201I": "GGTACC", "Uba1202I": "GGGCCC", "Uba1203I": "GTGCAC", "Uba1204I": "GATC", "Uba1205I": "GGATCC", "Uba1205II": "CYCGRG", "Uba1206I": "GRGCYC", "Uba1207I": "GGCC", "Uba1208I": "GGCC", "Uba1209I": "GGCC", "Uba1210I": "GGCC", "Uba1211I": "CTGCAG", "Uba1212I": "CTGCAG", "Uba1213I": "CTGCAG", "Uba1214I": "GGCC", "Uba1215I": "CTGCAG", "Uba1216I": "CTGCAG", "Uba1217I": "AGGCCT", "Uba1218I": "CCWGG", "Uba1219I": "AAGCTT", "Uba1220I": "CCCGGG", "Uba1221I": "GCTNAGC", "Uba1222I": "GCTNAGC", "Uba1223I": "GGCC", "Uba1224I": "GGATCC", "Uba1225I": "CTGCAG", "Uba1226I": "GCATGC", "Uba1227I": "CAGCTG", "Uba1228I": "GGCC", "Uba1229I": "CCGCGG", "Uba1230I": "GGCC", "Uba1231I": "GGCC", "Uba1232I": "CTGCAG", "Uba1233I": "ATCGAT", "Uba1234I": "CCGCGG", "Uba1235I": "GGCC", "Uba1237I": "CTCGAG", "Uba1238I": "ATCGAT", "Uba1239I": "AGGCCT", "Uba1240I": "TACGTA", "Uba1241I": "GGGCCC", "Uba1242I": "GGATCC", "Uba1243I": "CCWGG", "Uba1244I": "CCGCGG", "Uba1245I": "CAGCTG", "Uba1246I": "ATCGAT", "Uba1248I": "CTCGAG", "Uba1249I": "GGWCC", "Uba1250I": "GGATCC", "Uba1256I": "CTGCAG", "Uba1257I": "ATCGAT", "Uba1258I": "GGATCC", "Uba1259I": "GATC", "Uba1262I": "CTGCAG", "Uba1263I": "GRGCYC", "Uba1264I": "GRGCYC", "Uba1266I": "CTTAAG", "Uba1267I": "CCGG", "Uba1271I": "CTCGAG", "Uba1272I": "GGWCC", "Uba1275I": "ATCGAT", "Uba1276I": "CTCTTC", "Uba1278I": "GGWCC", "Uba1279I": "TCCGGA", "Uba1280I": "CCSGG", "Uba1282I": "TGATCA", "Uba1283I": "TGATCA", "Uba1284I": "GCTNAGC", "Uba1286I": "ATCGAT", "Uba1287I": "CTGCAG", "Uba1288I": "GGCC", "Uba1289I": "CCTNNNNNAGG", "Uba1290I": "CCTNNNNNAGG", "Uba1291I": "GGTNACC", "Uba1292I": "GGCC", "Uba1293I": "GGCC", "Uba1294I": "CCTNAGG", "Uba1294II": "CTGCAG", "Uba1295I": "ATCGAT", "Uba1296I": "CTGCAG", "Uba1297I": "GGATCC", "Uba1298I": "CTCGAG", "Uba1299I": "CTTAAG", "Uba1302I": "GGATCC", "Uba1303I": "CGRYCG", "Uba1304I": "GGWCC", "Uba1305I": "GGNNCC", "Uba1306I": "CCGCGG", "Uba1307I": "GRGCYC", "Uba1308I": "CCTNNNNNAGG", "Uba1309I": "CCTNNNNNAGG", "Uba1310I": "CCTNNNNNAGG", "Uba1311I": "CCWWGG", "Uba1312I": "CTTAAG", "Uba1313I": "CTTAAG", "Uba1314I": "GGWCC", "Uba1315I": "ATCGAT", "Uba1316I": "GGTCTC", "Uba1317I": "GATC", "Uba1318I": "CCSGG", "Uba1319I": "GGCC", "Uba1320I": "GCTNAGC", "Uba1321I": "CGCG", "Uba1322I": "GGCC", "Uba1323I": "GATC", "Uba1324I": "GGATCC", "Uba1325I": "GGATCC", "Uba1326I": "RGGNCCY", "Uba1327I": "YGGCCR", "Uba1328I": "CTGCAG", "Uba1329I": "GRGCYC", "Uba1330I": "GRGCYC", "Uba1331I": "CTTAAG", "Uba1332I": "CCTNAGG", "Uba1333I": "CCTNAGG", "Uba1334I": "GGATCC", "Uba1335I": "CTCGAG", "Uba1336I": "GGCC", "Uba1337I": "CTGCAG", "Uba1338I": "CCGG", "Uba1339I": "GGATCC", "Uba1342I": "ATCGAT", "Uba1343I": "GGTCTC", "Uba1346I": "GGATCC", "Uba1347I": "CCSGG", "Uba1353I": "ATGCAT", "Uba1355I": "CCGG", "Uba1357I": "GRGCYC", "Uba1362I": "GDGCHC", "Uba1363I": "GRGCYC", "Uba1364I": "CCGCGG", "Uba1366I": "GATC", "Uba1366II": "ATCGAT", "Uba1367I": "ATGCAT", "Uba1368I": "GGGCCC", "Uba1369I": "CCGCGG", "Uba1370I": "CCSGG", "Uba1371I": "AGGCCT", "Uba1372I": "CCSGG", "Uba1373I": "GGWCC", "Uba1374I": "CTTAAG", "Uba1375I": "TCCGGA", "Uba1376I": "CCSGG", "Uba1377I": "GGCC", "Uba1378I": "CCSGG", "Uba1379I": "ATCGAT", "Uba1380I": "ATCGAT", "Uba1381I": "GRCGYC", "Uba1382I": "GAATGC", "Uba1383I": "GGATCC", "Uba1384I": "ATGCAT", "Uba1385I": "TTCGAA", "Uba1386I": "TCGCGA", "Uba1387I": "GTGCAC", "Uba1388I": "GGCC", "Uba1389I": "CCSGG", "Uba1391I": "CCNGG", "Uba1392I": "GGCC", "Uba1393I": "CCCGGG", "Uba1394I": "ATCGAT", "Uba1395I": "GGCC", "Uba1397I": "CTCGAG", "Uba1398I": "GGATCC", "Uba1399I": "CTGCAG", "Uba1400I": "GATATC", "Uba1401I": "CCSGG", "Uba1402I": "GGATCC", "Uba1403I": "AGGCCT", "Uba1404I": "CGCG", "Uba1405I": "CGCG", "Uba1408I": "GGCC", "Uba1408II": "GTTAAC", "Uba1409I": "GRGCYC", "Uba1410I": "CCWGG", "Uba1411I": "CTGCAG", "Uba1412I": "ATCGAT", "Uba1413I": "GGWCC", "Uba1414I": "GGATCC", "Uba1415I": "GAATGC", "Uba1416I": "ATCGAT", "Uba1417I": "CTGCAG", "Uba1418I": "GGCC", "Uba1419I": "AGGCCT", "Uba1420I": "CTTAAG", "Uba1421I": "GRGCYC", "Uba1422I": "GGCC", "Uba1423I": "CCSGG", "Uba1424I": "CCSGG", "Uba1425I": "TCCGGA", "Uba1426I": "CTTAAG", "Uba1427I": "ATCGAT", "Uba1428I": "CCWGG", "Uba1429I": "GGCC", "Uba1430I": "ATCGAT", "Uba1431I": "TGATCA", "Uba1432I": "RGATCY", "Uba1433I": "AGCT", "Uba1435I": "AAGCTT", "Uba1436I": "CYCGRG", "Uba1437I": "CTGGAG", "Uba1438I": "GGWCC", "Uba1439I": "CCGG", "Uba1440I": "CYCGRG", "Uba1441I": "AGCT", "Uba1442I": "CCNNGG", "Uba1443I": "CTTAAG", "Uba1444I": "CTGGAG", "Uba1445I": "GGNNCC", "Uba1446I": "CGCG", "Uba1447I": "TGATCA", "Uba1448I": "CTCGAG", "Uba1449I": "GGCC", "Uba1450I": "GGCC", "Uba1451I": "ATCGAT", "Uba1452I": "TTCGAA", "Uba1453I": "ATCGAT", "Uba4009I": "GGATCC", "Uba153AI": "CAGCTG", "UbaF9I": "TACNNNNNRTGT", "UbaF11I": "TCGTA", "UbaF12I": "CTACNNNGTC", "UbaF13I": "GAGNNNNNNCTGG", "UbaF14I": "CCANNNNNTCG", "UbaHKAI": "CCGCGG", "UbaHKBI": "CTGCAG", "UbaLAI": "CCWGG", "UbaM39I": "CAGCTG", "UbaN1I": "GATC", "UbaN2I": "GGCC", "UbaN3I": "GGATCC", "UbaN4I": "GATATC", "UbaN5I": "CTAG", "UbaN6I": "GATNNNNATC", "UbaN6II": "CYCGRG", "UbaN7I": "CTCTTC", "UbaN8I": "GGCC", "UbaN9I": "AACGTT", "UbaN10I": "WCCGGW", "UbaN11I": "CCSGG", "UbaN11II": "CCWGG", "UbaN12I": "CACGAG", "UbaN13I": "CCGG", "UbaN14I": "GGATCC", "UbaN15I": "GGWCC", "UbaN16I": "CCWGG", "UbaN17I": "AAGCTT", "UbaN18I": "ATCGAT", "UbaN19I": "TCTAGA", "UbaN20I": "GATNNNNATC", "UbaN21I": "GATATC", "UbaN22I": "ATCGAT", "UbaPI": "CGAACG", "Umi5I": "CYCGRG", "Umi7I": "TGATCA", "UnbI": "GGNCC", "Uth549I": "GGCC", "Uth554I": "GGWCC", "Uth555I": "GGCC", "Uth557I": "GGCC", "Uur960I": "GCNGC", "M.Val114Dam": "GATC", "M.Val33868Dam": "GATC", "M.ValJ207Dam": "GATC", "VanI": "GCCNNNNNGGC", "Van91I": "CCANNNNNTGG", "Van91II": "GAATTC", "Van91III": "GGCC", "Van425II": "CCKAAG", "Van9116I": "CCKAAG", "M.Van425Dam": "GATC", "M.Van9116Dam": "GATC", "M.Van11008Dam": "GATC", "M.Van68554Dam": "GATC", "M.VanS349Dam": "GATC", "VarFI": "GCNGC", "M.Vch2740Dam": "GATC", "VchE4II": "RTAAAYG", "M.VchE7946Dam": "GATC", "VchN100I": "GAATTC", "VchO2I": "GAATTC", "VchO25I": "GTATAC", "VchO44I": "AGGCCT", "VchO49I": "AGTACT", "VchO66I": "GGNCC", "VchO68I": "GCATGC", "VchO70I": "TCGCGA", "VchO85I": "GGNCC", "VchO87I": "CTGCAG", "VchO90I": "GGNCC", "M.VchO395Dam": "GATC", "M.Vco1Dam": "GATC", "M.Vco58Dam": "GATC", "M.VcoRE98Dam": "GATC", "Vdi96II": "GNCYTAG", "M.Vdi96Dam": "GATC", "M.Vdi105Dam": "GATC", "M.Vdi3418Dam": "GATC", "VfiI": "CTTAAG", "M.VflA21Dam": "GATC", "M.Vfl33809Dam": "GATC", "Vga43942II": "CCANNNNNCTC", "M.Vga43942Dam": "GATC", "VhaI": "GGCC", "Vha44I": "GATC", "Vha464I": "CTTAAG", "Vha1168I": "GGCC", "Vha43516II": "GCYYGAC", "M.VmeT6Dam": "GATC", "M.Vna16373Dam": "GATC", "M.Vna16374Dam": "GATC", "M.Vna51183Dam": "GATC", "VneI": "GTGCAC", "VneAI": "RGGNCCY", "VniI": "GGCC", "M.Vpa191Dam": "GATC", "M.Vpa4557Dam": "GATC", "M.Vpa10903Dam": "GATC", "M.Vpa17802Dam": "GATC", "VpaK11I": "GGWCC", "VpaK15I": "GGNCC", "VpaK25I": "GGNCC", "VpaK32I": "GCTCTTC", "VpaK57I": "GGTCTC", "VpaK65I": "GGWCC", "VpaK3AI": "CACGTG", "VpaK4AI": "CTGCAG", "VpaK7AI": "GGWCC", "VpaK9AI": "GGNCC", "VpaK11AI": "GGWCC", "VpaK13AI": "GGWCC", "VpaK19AI": "GGNCC", "VpaK29AI": "CTGCAG", "VpaK57AI": "GGTCTC", "VpaK3BI": "CACGTG", "VpaK4BI": "CTGCAG", "VpaK11BI": "GGWCC", "VpaK19BI": "GGNCC", "VpaK11CI": "GGWCC", "VpaK11DI": "GGWCC", "VpaKutAI": "GGNCC", "VpaKutBI": "GGNCC", "VpaKutEI": "CTCTTC", "VpaKutFI": "CTCTTC", "VpaKutGI": "CTGCAG", "VpaKutHI": "GGTCTC", "VpaKutJI": "GGNCC", "VpaO5I": "CTCTTC", "M.Vpa10329SDam": "GATC", "VspI": "ATTAAT", "Vsp2246I": "GGYRCC", "M.Vsp398Dam": "GATC", "M.Vsp4003Dam": "GATC", "M.Vsp61001Dam": "GATC", "Vtu19109I": "CACRAYC", "M.Vtu19109Dam": "GATC", "M.VvuCMCP6Dam": "GATC", "M.VvuDam": "GATC", "M.Vvu36Dam": "GATC", "M.Vvu11067Dam": "GATC", "M.Vvu13647Dam": "GATC", "M.Vvu61306Dam": "GATC", "VvuEnv1II": "CCGCAG", "M.VvuEnv1Dam": "GATC", "WviI": "CACRAG", "XagI": "CCTNNNNNAGG", "XamI": "GTCGAC", "XapI": "RAATTY", "XbaI": "TCTAGA", "XcaI": "GTATAC", "Xca85IV": "TACGAG", "XceI": "RCATGY", "XciI": "GTCGAC", "XcmI": "CCANNNNNNNNNTGG", "XcpI": "CTGCAG", "XcyI": "CCCGGG", "Xgl3216I": "CGATCG", "Xgl3217I": "CGATCG", "Xgl3218I": "CGATCG", "Xgl3219I": "CGATCG", "Xgl3220I": "CGATCG", "XhoI": "CTCGAG", "XhoII": "RGATCY", "XmaI": "CCCGGG", "XmaIII": "CGGCCG", "XmaCI": "CCCGGG", "XmaJI": "CCTAGG", "XmiI": "GTMKAC", "XmlI": "CGATCG", "XmlAI": "CGATCG", "XmnI": "GAANNNNTTC", "M.XnePDam": "GATC", "XniI": "CGATCG", "XorI": "CTGCAG", "XorII": "CGATCG", "XorKI": "CGATCG", "XorKII": "CTGCAG", "XpaI": "CTCGAG", "XphI": "CTGCAG", "XspI": "CTAG", "Xsp91II": "CGACCAG", "XveI": "CTGCAG", "M.Yal67083Dam": "GATC", "YenI": "CTGCAG", "YenAI": "CTGCAG", "YenBI": "CTGCAG", "YenCI": "CTGCAG", "YenDI": "CTGCAG", "M.Yen2516Dam": "GATC", "YenEI": "CTGCAG", "M.Yin358Dam": "GATC", "YkrI": "C", "M.YpeDam": "GATC", "M.Ype19Dam": "GATC", "M.Ype41Dam": "GATC", "M.YpeDodDam": "GATC", "M.YpeEDDam": "GATC", "M.YpeJ9Dam": "GATC", "M.YpeNaiDam": "GATC", "M.YpePGDam": "GATC", "M.YpeShDam": "GATC", "Yps3606I": "CGGAAG", "M.YpsADam": "GATC", "M.Yps342Dam": "GATC", "M.Yps3606Dam": "GATC", "M.Yps8480Dam": "GATC", "M.YpsEP2Dam": "GATC", "M.YpsPBDam": "GATC", "M.YroYRADam": "GATC", "M.Yru3758Dam": "GATC", "ZanI": "CCWGG", "ZhoI": "ATCGAT", "M.ZmoIII": "GANTC", "M.Zmo29192III": "GANTC", "ZraI": "GACGTC", "ZrmI": "AGTACT", "Zsp2I": "ATGCAT"}