import json
import re
from pathlib import Path
from typing import Literal

import fsspec

RAP2MSU: dict[str, list[str] | None] = {}
MSU2RAP: dict[str, str | None] = {}
NAU2MSU: dict[str, str] = json.loads((Path(__file__).parent / "NAU2MSU.json").read_text())
MSU2NAU: dict[str, str] = {v: k for k, v in NAU2MSU.items()}

map_file = "https://rapdb.dna.affrc.go.jp/download/archive/RAP-MSU_2025-03-19.txt.gz"
with fsspec.open(
    f"filecache::{map_file}",
    mode="rt",
    compression="gzip",
) as f:
    for line in f:
        rap_id, msu_ids = line.strip().split("\t")
        if rap_id == "None":
            for i in msu_ids.split(","):
                MSU2RAP[i] = None
        elif msu_ids == "None":
            RAP2MSU[rap_id] = None
        else:
            RAP2MSU[rap_id] = ids = msu_ids.split(",")
            for i in ids:
                MSU2RAP[i] = rap_id


def guess_id_system(id: str) -> Literal["MSU", "RAP"] | None:
    if re.match(r"Os(?:0[1-9]|1[012])g\d{7}", id):
        return "RAP"
    elif re.match(r"LOC_Os(?:0[1-9]|1[012])g\d{5}(\.\d+)?", id):
        return "MSU"
    else:
        return None


def convert(id: str) -> str | list[str] | None:
    id_system = guess_id_system(id)
    if id_system is None:
        raise ValueError(f"Your input id {id} is not RAP id or MSU id.")
    if id_system == "RAP":
        return RAP2MSU.get(id)
    else:
        if "." not in id:
            id += ".1"
        return MSU2RAP.get(id)
