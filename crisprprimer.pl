#!/usr/bin/perl
# revised by <PERSON><PERSON><PERSON><PERSON> (xian<PERSON><PERSON>@well.ox.ac.uk)

# Copyright (c) 2011 <PERSON> (<EMAIL>)
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
# THE SOFTWARE.
#
# ALSO, IT WOULD BE NICE IF YOU LET ME KNOW YOU USED IT.

use Data::Dumper;
use List::Util qw[min max];
use FindBin;
use lib "$FindBin::Bin/.";
use fastapro;
use crisprpro;
use strict;

use File::Temp qw/ tempfile tempdir /;

use Getopt::Std;
my %opts = (
    g => undef,
    a => undef,
    f => undef,
    p => "A",
    r => undef,
    d => undef,
    w => "37-70",
    h => undef,
    s => 20,
    t => 1,
    i => "BbsI",
    j => "BsaI",
    k => "GGCG;GTTT;TGTG;GTTT",
    l => "http://rice.uga.edu/cgi-bin/ORF_infopage.cgi?orf=%%",
    z =>
"http://rice.uga.edu/cgi-bin/gbrowse_img/rice?q=%%;t=Genes+Rice_Annotation;w=600;abs=0"
);
getopts( 'ag:f:r:w:h:p:s:t:i:j:k:l:z:d:', \%opts );
my $filegff   = $opts{g};
my $ref       = $opts{f};
my $reg       = $opts{r};
my $fragment  = $opts{d};
my $win       = $opts{w};
my $prefix    = $opts{p};
my $spacerlen = $opts{s};
my $pamtype   = $opts{t};
my $not4uniq  = $opts{a};

my $fenzyme = $opts{i};
my $senzyme = $opts{j};

my $links   = $opts{k};
my @linkseq = split( /;/, $links );

my $url    = $opts{l};
my $urlimg = $opts{z};

###my $url="http://rice.plantbiology.msu.edu/cgi-bin/ORF_infopage.cgi?db=osa1r5&orf=%%";
#my $url="http://rice.uga.edu/cgi-bin/ORF_infopage.cgi?orf=%%";
#my $urlimg="http://rice.uga.edu/cgi-bin/gbrowse_img/rice?q=%%;t=Genes+Rice_Annotation;w=600;abs=0";

## extpam: number of basepair of PAM to its near nick site.
## spacerdist: PAM's distance to the cutting point without any PAM basepair
## nnpam:  number of N in PAM, if N is at the beginning of PAM.
my %crisprinfo = (
    1 =>
      { extpam => 6, spacerdist => 3, nnpam => -1, pam => [ [ "CC", "GG" ] ] },
    2 => {
        extpam     =>  6,
        spacerdist =>  3,
        nnpam      => -1,
        pam        =>
          [ [ "CC", "GG" ], [ "CT", "AG" ], [ "CT", "GG" ], [ "CC", "AG" ] ]
    }
);
my $extpam     = $crisprinfo{$pamtype}->{extpam};
my $spacerdist = $crisprinfo{$pamtype}->{spacerdist};
my $nnpam      = $crisprinfo{$pamtype}->{nnpam};
my @list_pam   = @{ $crisprinfo{$pamtype}->{pam} };

## restriction enzyme used
my %restrenzyme = (
    "BbsI" => "GAAGAC",
    "BsaI" => "GAGACC"
);

my @proh;
push( @proh, $restrenzyme{$fenzyme} )
  if ( defined($fenzyme) && length($fenzyme) > 0 );
push( @proh, $restrenzyme{$senzyme} )
  if ( defined($senzyme) && length($senzyme) > 0 );
my @rproh;
for my $seq (@proh) {
    my $tmp = &reverseComplement($seq);
    push @rproh, $tmp;
}
my @single = ("TTTT");
my @aproh  = ( @proh, @rproh, @single );

my $outpre    = $opts{h};
my $indexhtml = undef;
my $indextxt  = undef;

if ( defined($outpre) ) {
    $indexhtml = $opts{h} . ".html";
    $indextxt  = $opts{h} . ".txt";
}

if ( !defined($ref) ) {
    warn "Please provide the genome sequence file!\n";
    &usage();
}

## if we only need to process a region or a single chromosome, keep the parameters.
my $lchr;
my $lbeg;
my $lend;
if ( defined($reg) ) {
    if ( $reg =~ m/([^:]+):([0-9]+)[-]([0-9]+)/ ) {
        $lchr = $1;
        $lbeg = $2;
        $lend = $3;
    }
    else {
        $lchr = $reg;
        $lend = -1;
        warn(
            "WARNING: chromosome id is $lchr. It contains : or - in the name\n")
          if ( $reg =~ /[:-]/ );
    }
}

# in fragment mode, $reg and annotation file are not used.
if ( defined($fragment) ) {
    if ( $fragment =~ m/([^:]+):([0-9]+)[-]([0-9]+)/ ) {
        $lchr = $1;
        $lbeg = $2;
        $lend = $3;
    }
    else {
        die "fragment mode, the parameter much be chr:start-end\n";
    }
}

my $winmin;
my $winmax;
if ( defined($win) ) {
    if ( $win =~ m/([0-9]+)-([0-9]+)/ ) {
        $winmin = $1 + $extpam - $nnpam +
          $spacerdist
          ; ####!!! the winmix and winmax use CC -> GG postion, $1/$2 use the length of distance between nick sites.
        $winmax = $2 + $extpam - $nnpam + $spacerdist;
    }
    else {
        die "WARNING: the win size desiganted by -w is not right\n";
    }
}

## If we only need to process a couple of genes, keep the list
my %glists;
my %parentkept
  ; ##only used to keep the exon/cds/transcripts/genes  whose id or whose ancestral id is inside the list
my $issubset = 0;
if ( @ARGV > 0 ) {
    foreach (@ARGV) {
        $glists{$_} = 1;
    }
    $issubset = 1;
}

my $id;
my $pid;    ## parent id
my %exons;
my %trans;
my %sc;
my %genes;

if ( !defined($fragment) && defined($filegff) ) {
    my $format_gff = undef;
    open IN,
      (
          $filegff =~ /\.gz$/  ? "gunzip -c $filegff"
        : $filegff =~ /\.zip$/ ? "unzip -p $filegff"
        :                        "$filegff"
      );
    while (<IN>) {
        if ( !defined($format_gff) ) {
            $format_gff = 2 if /^##gff-version 2/;
            $format_gff = 3 if /^##gff-version 3/;
        }
        next if /^#/ && $format_gff;
        if ( !defined($format_gff) ) { $format_gff = 4; }

        s/\s+$//;

        # 0-chr 1-src 2-feat 3-beg 4-end 5-scor 6-dir 7-fram 8-attr
        my @f = split /\t/;

        if ( defined($reg) ) {
            next if ( $f[0] ne $lchr );
            if ( $lend > 0 ) {
                next if ( $lend < $f[3] || $lbeg > $f[4] );
            }
        }

        $pid = undef;
        $id  = undef;

        # most ver 2's stick gene names in the id field
        ($id) = $f[8] =~ /\bID=["]*([^";]+)["]*/;

        # most ver 3's stick unquoted names in the name field
        ($id) = $f[8] =~ /\bName=([^";]+)/ if !$id && $format_gff == 3;

        # other ver directly in the name field
        ($id) = $f[8] =~ /transcript_id "([^" ;]+)"/ if !$id && $format_gff > 3;

        ($pid) = $f[8] =~ /\bParent=([^";,]+)/ if !$pid;
        next unless ( $id || $pid ) && $f[0];

        if ($issubset) {
            my $kept = 0;
            if ( defined($id)
                && ( exists $glists{$id} || exists $parentkept{$id} ) )
            {
                $kept = 1;
            }
            if ( defined($pid)
                && ( exists $glists{$pid} || exists $parentkept{$pid} ) )
            {
                $kept = 1;
            }
            if ( $kept == 1 ) {
                $parentkept{$id} = 1 if ( defined($id) );
            }
            else {
                next;
            }

        }

        if ( $f[2] eq 'CDS' ) {
            die "no position at exon on line $." if !$f[3];
            push @{ $exons{$pid} }, \@f;

            # save lowest start
            if ( !$trans{$pid} ) {
                warn("$pid has no mRNA entries\n");
                $trans{$pid} = \@f;
            }

        }
        elsif ( $f[2] eq 'start_codon' ) {

            #optional, output codon start/stop as "thick" region in bed
            $sc{$pid}->[0] = $f[3];
        }
        elsif ( $f[2] eq 'stop_codon' ) {
            $sc{$pid}->[1] = $f[4];
        }
        elsif ($f[2] eq 'mRNA'
            || $f[2] eq 'miRNA'
            || $f[2] eq 'ncRNA'
            || $f[2] eq 'tRNA'
            || $f[2] eq 'rRNA'
            || $f[2] eq 'snRNA'
            || $f[2] eq 'snoRNA' )
        {
            $trans{$id} = \@f if !$trans{$id};
            push @{ $genes{$pid}->{trid} }, $id;
        }
    }
    close IN;
}
else {
   #warn "No gff annotation file provided, use the whole region/chromosome!\n" ;
    if ( !defined($reg) ) {
        my @chrs = get_chromosome_list($ref);
        $lchr = $chrs[0];
        $lbeg = 1;
        $lend = -1;

    }
    if ( $lend < 0 ) {
        my $sequ = get_chromosome_part( $ref, $lchr, $lbeg, $lend );
        $lend = length($sequ);
    }

    my $id   = $lchr;
    my $pid  = $lchr;
    my @mrna = ( $lchr, "customer", "mRNA", $lbeg, $lend, ".", "+" );
    $trans{$id} = \@mrna;
    push @{ $genes{$pid}->{trid} }, $id;

    my @cds = ( $lchr, "customer", "CDS", $lbeg, $lend, ".", "+" );
    push @{ $exons{$pid} }, \@cds;
}

my $phtml;
my $ptxt;
if ( defined $indexhtml ) {
    open $phtml, ">", "$indexhtml" or die "cannot create file $indexhtml\n";
    open $ptxt,  ">", "$indextxt"  or die "cannot create file $indextxt\n";
    print_sumheader($phtml);
}

my $indgene = 0;
for my $gid ( sort keys(%genes) ) {
    my @tids = @{ $genes{$gid}->{trid} };
    my @ovlapcds;
    if ( scalar(@tids) == 0 ) {
        next;
    }
    elsif ( scalar(@tids) == 1 ) {
        $genes{$gid}->{cds}     = $exons{ $tids[0] };    #get the reference
        $genes{$gid}->{sscodon} = $sc{ $tids[0] };
        $genes{$gid}->{chr}     = $exons{ $tids[0] }->[0]->[0];
    }
    else {
        $genes{$gid}->{chr} = $exons{ $tids[0] }->[0]->[0];
        my @ez = @{ $exons{ $tids[0] } };
        for my $t (@ez) {
            my $tmp = $t;
            for ( my $i = 1 ; $i < scalar(@tids) ; $i++ ) {
                my $p2ez = $exons{ $tids[$i] };
                $tmp = &ref_overlap( $tmp, $p2ez );
                if ( !defined($tmp) ) {
                    last;
                }
            }
            if ( defined($tmp) ) {
                push @{ $genes{$gid}->{cds} }, $tmp;

            }
        }
        if ( !defined( $genes{$gid}->{cds} ) ) {
            $genes{$gid}->{cds} = [];
        }
    }

    $indgene++;
    print STDERR "Number of transcripts for gene $gid: ", scalar(@tids), "\n";
    print STDERR "Candidate CDS regions: ", scalar( @{ $genes{$gid}->{cds} } ),
      "\n";

#calculate the start position and the end position of shared CDS regions for scanning
    $genes{$gid}->{sscodon}->[0] =
      $exons{ $tids[0] }->[0]->[0];    #get the chrom id;
    $genes{$gid}->{sscodon}->[1] = -1;
    $genes{$gid}->{sscodon}->[2] = -1;
    $genes{$gid}->{sscodon}->[3] = $exons{ $tids[0] }->[0]->[6]; #get the strand

    for my $t ( @{ $genes{$gid}->{cds} } ) {

        #print STDERR join("\t", @$t), "\n";
        if (   $genes{$gid}->{sscodon}->[1] < 0
            || $genes{$gid}->{sscodon}->[1] > $t->[3] )
        {
            $genes{$gid}->{sscodon}->[1] = $t->[3];
        }
        if ( $genes{$gid}->{sscodon}->[2] < $t->[4] ) {
            $genes{$gid}->{sscodon}->[2] = $t->[4];
        }
    }

    #print STDERR "region: ", join("\t",@{$genes{$gid}->{sscodon}}), "\n";
    $genes{$gid}->{range}->[0] = $genes{$gid}->{sscodon}->[0];
    $genes{$gid}->{range}->[1] =
      max( 0, $genes{$gid}->{sscodon}->[1] - $winmax - 5 )
      ;    ## extra 5 to make sure 2 base pair extra are considered.
    $genes{$gid}->{range}->[2] = $genes{$gid}->{sscodon}->[2] + $winmax + 5;

    #print STDERR "region: ", join("\t",@{$genes{$gid}->{range}}), "\n";

    my $sequ = uc get_chromosome_part(
        $ref,
        $genes{$gid}->{range}->[0],
        $genes{$gid}->{range}->[1],
        $genes{$gid}->{range}->[2]
    );

    my $p_pampair = &matchpam( \$sequ, \@list_pam, $winmin, $winmax );
    if ( scalar(@$p_pampair) == 0 ) {
        print STDERR "ERROR: Can not find enough loci with PAM for gene $gid\n";
        next;
    }

    my @pairscore;    #indicate whether the pair is good
    for ( my $i = 0 ; $i < scalar(@$p_pampair) ; $i++ ) {
        $pairscore[$i] = 100;                             #initial value
        my @test_range = @{ $genes{$gid}->{cds}->[0] };   #copy here for testing
          # test whether the pam fragment  overlaps with the shared CDS regions.
        $test_range[3] = $genes{$gid}->{range}->[1] + @$p_pampair[$i]->[1];
        $test_range[4] = $genes{$gid}->{range}->[1] + @$p_pampair[$i]->[2];
        my $tmp = ref_overlap( \@test_range, $genes{$gid}->{cds} );
        if ( !defined($tmp) ) {
            $pairscore[$i] = 0;
            next;
        }

#if (&ccggfragcheck(\$sequ, @$p_pampair[$i], \@aproh, $spacerlen, $spacerdist) ==0 )  {
#    $pairscore[$i]=0;
#}

        #test whether the spacers contain prohabited oligo sequence.
        my $hang_left  = $extpam - $spacerdist;
        my $hang_right = $nnpam;
        if ( $nnpam > 0 ) {
            $hang_left  = $extpam - $spacerdist - $nnpam;
            $hang_right = 0;
        }
        if (
            &ccggfragcheck2(
                \$sequ,     @$p_pampair[$i], \@aproh, $spacerlen,
                $hang_left, $hang_right,     \@linkseq
            ) == 0
          )
        {
            $pairscore[$i] = 0;
        }
    }

    my @p_furpairs;
    for ( my $i = 0 ; $i < scalar(@$p_pampair) ; $i++ ) {
        if ( $pairscore[$i] > 0 ) {
            my @test_range2 = @{ $genes{$gid}->{cds}->[0] }[ 0 .. 5 ];
            $test_range2[3] = $genes{$gid}->{range}->[1] + @$p_pampair[$i]->[1];
            $test_range2[4] = $genes{$gid}->{range}->[1] + @$p_pampair[$i]->[2];
            if ( @$p_pampair[$i]->[0] == 0 ) {
                $test_range2[5] = "+";
            }
            else {
                $test_range2[5] = "-";
            }

            my $z    = \@test_range2;
            my $pref = "P";
            if ( $z->[5] eq "-" ) {
                $pref = "N";
            }
            my $locusid = $pref . "_" . $z->[0] . "_" . $z->[3] . "_" . $z->[4];
            my $primerid1 = $pref . "L_" . $z->[3];
            my $primerid2 = $pref . "R_" . $z->[4];

            my $s1_r = substr( $sequ, $z->[3] - $genes{$gid}->{range}->[1] + 3,
                $spacerlen );
            my $s1;
            if ( $pref eq "P" ) {
                $s1 = reverseComplement($s1_r);
            }
            else {
                $s1 = $s1_r;
            }

            my $s2_r =
              substr( $sequ,
                $z->[4] - $genes{$gid}->{range}->[1] - 1 - $spacerlen,
                $spacerlen );
            my $s2;
            if ( $pref eq "P" ) {
                $s2 = $s2_r;
            }
            else {
                $s2 = reverseComplement($s2_r);
            }

            $z->[2] = $locusid;
            $z->[6] = $primerid1;
            $z->[7] = $s1;
            $z->[8] = $primerid2;
            $z->[9] = $s2;

            push( @p_furpairs, $z );
        }
    }

    if ( scalar(@p_furpairs) == 0 ) {
        print STDERR
"ERROR: Can not find enough loci after filtering for restriction enzyme sequences for gene $gid\n";
        next;
    }

    @$p_pampair = ();
    @pairscore  = ();

    $p_pampair = \@p_furpairs;

    ## make sure the spacer-pair is unique
    my @p_furpairs2;
    if ( !$not4uniq ) {
        my %spacerids;
        my ( $fh, $tmpfilename ) =
          tempfile( "spacerXXXXXXXXX", SUFFIX => '.txt' );

        for my $z (@p_furpairs) {

            #print STDERR join ("\t", @$z), "\n";

            my $primerid1 = $z->[6];
            my $primerid2 = $z->[8];

            if ( !exists( $spacerids{$primerid1} ) ) {
                print {$fh} ">$primerid1", "\n";
                print {$fh} $z->[7],       "\n";
                $spacerids{$primerid1} = 1;
            }
            if ( !exists( $spacerids{$primerid2} ) ) {
                print {$fh} ">$primerid2", "\n";
                print {$fh} $z->[9],       "\n";
                $spacerids{$primerid2} = 1;
            }
        }
        close $fh;
        my ( $fh2, $tmpfilename2 ) =
          tempfile( "spacerXXXXXXXXX", SUFFIX => '.psl' );
        my $cmd =
          "blat -minScore=10 -minMatch=1 $ref $tmpfilename $tmpfilename2";
        system("$cmd >/dev/null");

        my $secbest = ReadBlatSecondbest($tmpfilename2);
        unlink($tmpfilename);
        unlink($tmpfilename2);

        #remove the pair with ununique spacer sequences.
        for my $z (@p_furpairs) {

            my $primerid1 = $z->[6];
            my $primerid2 = $z->[8];

            if (   exists( $secbest->{$primerid1} )
                && exists( $secbest->{$primerid2} )
                && $secbest->{$primerid1}->[0] > 3
                && $secbest->{$primerid2}->[0] > 3 )
            {
                next;
            }
            push( @p_furpairs2, $z );
        }
        $p_pampair = \@p_furpairs2;

        if ( scalar(@$p_pampair) == 0 ) {
            print STDERR
"ERROR: Can not find enough loci after filtering out oligos with multiple hits on the genome for gene $gid\n";
            next;
        }
    }

    ### rank the pair
    for my $z (@$p_pampair) {
        $z->[1] =
          int( rank4diststart( $z, $genes{$gid}->{sscodon} ) *
              rank4len($z) /
              100 + 0.5 );    ##to keep the score
    }

    my $k = scalar @$p_pampair;
    print "Number of fragments satisfying our requirements: $k\n";

    #for my $z (@$p_pampair) {
    #	print STDERR join ("\t", @$z), "\n";
    #}

    ## now we sort $p_pampair
    my @sorted_oligodesign =
      sort { $b->[1] == $a->[1] ? $b->[3] cmp $a->[3] : $b->[1] <=> $a->[1] }
      @$p_pampair;

    my $url2 = $url;
    $url2 =~ s/%%/$gid/g;
    if ( defined $indexhtml ) {
        print {$phtml} "<tr>";
        print {$phtml} "<td>", "$prefix" . $indgene,               "</td>";
        print {$phtml} "<td>", "$gid",                             "</td>";
        print {$phtml} "<td>", $genes{$gid}->{chr},                "</td>";
        print {$phtml} "<td>", scalar(@tids),                      "</td>";
        print {$phtml} "<td>", scalar( @{ $genes{$gid}->{cds} } ), " </td>";
        print {$phtml} "<td>", "<a href=$url2> MSU </td>";
        my $link = "<a href=\"./$gid.html\"> $k </a>";
        print {$phtml} "<td>", $link, "</td>";
        print {$phtml} "</tr>";
    }

    {
        my $hindiv;
        open $hindiv, ">", "$gid.html" or die "cannot open file $gid.html";
        print {$hindiv}
          "<p>Number of transcripts for gene <a href=$url2> $gid</a> : ",
          scalar(@tids), "</p>\n";

        if ( defined($urlimg) && length($urlimg) > 5 ) {
            my $url3 = $urlimg;
            $url3 =~ s/%%/$gid/g;
            print {$hindiv} "<img src=\"$url3\"/>";
        }
        print {$hindiv} "<p>Number of Candidate CDS regions: ",
          scalar( @{ $genes{$gid}->{cds} } ), "</p>\n";
        &OutputSpacers( $hindiv, \@sorted_oligodesign );

        print {$ptxt} join( "\t",
            "$prefix" . $indgene,
            $gid, $genes{$gid}->{chr},
            scalar(@tids), scalar( @{ $genes{$gid}->{cds} } ), $k );

        if ( $k > 0 ) {
            print {$ptxt} "\t", @sorted_oligodesign[0]->[7];
            if ( scalar( @{ $sorted_oligodesign[0] } ) > 9 ) {
                print {$ptxt} "\t", @sorted_oligodesign[0]->[9];
            }
        }
        print {$ptxt} "\n";

        close $hindiv;
    }
}

if ( defined $indexhtml ) {
    print_sumtail($phtml);
}

sub print_sumheader {
    my ($phtml) = @_;
    print {$phtml} "<p>Below is the list of genes with CRISPR KO design </p>";
    print {$phtml} "<table border='1'>";
    print {$phtml} "<tr>";

    print {$phtml} "<td> No. </td>";
    print {$phtml} "<td> gene </td>";
    print {$phtml} "<td> chrom id</td>";
    print {$phtml} "<td> # transcripts </td>";
    print {$phtml} "<td> # shared CDSs</td>";
    print {$phtml} "<td> annotation </td>";
    print {$phtml} "<td> # valid pairs</td>";
    print {$phtml} "</tr>";
}

sub print_sumtail {
    my ($phtml) = @_;
    print {$phtml} "</table>";
}

sub rank4diststart {
    my ( $p_pam, $p_sharedcds ) = @_;

    #covering all shared CDS
    return 100
      if ( $p_pam->[3] <= $p_sharedcds->[1]
        && $p_pam->[4] >= $p_sharedcds->[2] );

    #part of design is outside
    if ( $p_pam->[3] < $p_sharedcds->[2] && $p_pam->[4] > $p_sharedcds->[1] ) {
        if (
            !(
                   $p_pam->[3] > $p_sharedcds->[1]
                && $p_pam->[4] < $p_sharedcds->[2]
            )
          )
        {
            return 50;
        }
    }

    #only pam fragment inside is preferable
    my $startcodon = $p_sharedcds->[1];
    $startcodon = $p_sharedcds->[2] if ( $p_sharedcds->[3] eq "-" );
    my $cent = ( $p_pam->[3] + $p_pam->[4] ) / 2;
    my $dist = abs( $cent - $startcodon );
    my $olen = $p_sharedcds->[2] - $p_sharedcds->[1];
    return 100 if ( $olen <= 10 );
    return 100 if ( $dist <= 10 );

    my $p = max( 0.5, min( 1 - $dist / $olen, 1 ) ) * 100;
    return $p;
}

sub rank4len {
    my ($p_pam) = @_;

    my $xlen = $p_pam->[4] - $p_pam->[3] + 2;
    return 0 if ( $xlen < 10 );

    my $xu = abs( $xlen - ( 60 + 12 ) );
    return 50 if ( $xu > 30 );
    my $p = max( 0.5, min( 1 - $xu / 30, 1 ) ) * 100;
    return $p;
}

sub usage {
    die(
        qq/
usage: $0 <commmand> [<geneid>...<geneid>]
command options:
  -f [file]	reference sequence
  -g [file]	annotation file in gff, gff3
  -r reg	region investigated, format as Chr4,Chr4:5-27,
  -d fragment   fragment mode, annotation not considered, formate as Chr4:5-27
  -p symbol	str used for the locus id prefix
  -a 		allow the spacer to have multiple hits in the genome
  -t 1 		selection of Cas protein and PAM
  -s 20		length of the spacer
  -i str	first restriction enzyme used
  -j str	second restriction enzyme used
  -h outprefix  prefix for the output file names
\n/
    );
}
